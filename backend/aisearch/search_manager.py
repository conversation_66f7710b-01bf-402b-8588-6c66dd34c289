import os
import logging
from azure.core.credentials import AzureKeyCredential
from azure.identity.aio import DefaultAzureCredential
from azure.search.documents.indexes.aio import SearchIndexClient, SearchIndexerClient
from azure.search.documents.indexes.models import (
    SearchIndex,
    SearchField,
    SearchFieldDataType,
    SearchableField,
    SimpleField,
    VectorSearch,
    HnswAlgorithmConfiguration,
    VectorSearchProfile,
    SemanticConfiguration,
    SemanticSearch,
    SemanticPrioritizedFields,
    SemanticField,
    SearchIndexerDataContainer,
    SearchIndexerDataSourceConnection,
    SearchIndexer,
    IndexingParameters,
    FieldMapping
)
from backend.settings import app_settings # To get search service endpoint

# Define the standard index schema for RAG projects
# This can be customized further based on specific needs
def create_index_definition(index_name: str) -> SearchIndex:
    """Creates the definition for a project-specific search index."""
    fields = [
        SimpleField(name="id", type=SearchFieldDataType.String, key=True, sortable=True, filterable=True, facetable=True),
        SearchableField(name="content", type=SearchFieldDataType.String, searchable=True, retrievable=True),
        # Vector field for storing embeddings
        SearchField(
            name="content_vector",
            type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
            searchable=True,
            dimensions=1536,
            vector_search_profile_name="my-vector-profile",
        ),
        SearchableField(name="filepath", type=SearchFieldDataType.String, filterable=True, facetable=True, searchable=True, retrievable=True),
        SimpleField(name="url", type=SearchFieldDataType.String, filterable=True, facetable=False, retrievable=True), # Optional: URL if content comes from web pages
        SimpleField(name="metadata_storage_name", type=SearchFieldDataType.String, filterable=True, facetable=True, retrievable=True), # Original filename
        SimpleField(name="metadata_storage_path", type=SearchFieldDataType.String, filterable=False, facetable=False, retrievable=True), # Full blob path
        SimpleField(name="metadata_storage_last_modified", type=SearchFieldDataType.DateTimeOffset, sortable=True, filterable=True, facetable=False, retrievable=True),
        SimpleField(name="metadata_storage_size", type=SearchFieldDataType.Int64, sortable=True, filterable=True, facetable=False, retrievable=True),
    ]

    # Vector search configuration
    vector_search = VectorSearch(
        profiles=[VectorSearchProfile(name="my-vector-profile", algorithm_configuration_name="my-hnsw-config")],
        algorithms=[HnswAlgorithmConfiguration(name="my-hnsw-config")]
    )

    # Semantic search configuration
    semantic_search = SemanticSearch(
        configurations=[
            SemanticConfiguration(
                name="default",
                prioritized_fields=SemanticPrioritizedFields(
                    title_field=SemanticField(field_name="filepath"),
                    content_fields=[SemanticField(field_name="content")]
                )
            )
        ]
    )

    # Create the SearchIndex object
    index = SearchIndex(
        name=index_name,
        fields=fields,
        vector_search=vector_search,
        semantic_search=semantic_search
    )
    return index

async def create_project_datasource(
    search_indexer_client: SearchIndexerClient,
    datasource_name: str,
    storage_account_name: str,
    container_name: str,
    storage_connection_string: str | None = None # Optional connection string
):
    """Creates or updates an Azure AI Search Data Source connection."""
    # If no connection string is provided, assume Managed Identity or RBAC access
    # Note: For RBAC, the Search Service Managed Identity needs 'Storage Blob Data Reader' on the container
    if storage_connection_string:
         container = SearchIndexerDataContainer(name=container_name)
         data_source_connection = SearchIndexerDataSourceConnection(
             name=datasource_name,
             type="azureblob",
             connection_string=storage_connection_string,
             container=container,
             description=f"Data source for project container {container_name}"
         )
    else:
         # Using Identity-based connection (Managed Identity or RBAC)
         storage_uri = f"https://{storage_account_name}.blob.core.windows.net/{container_name}"
         container = SearchIndexerDataContainer(name=container_name)
         data_source_connection = SearchIndexerDataSourceConnection(
             name=datasource_name,
             type="azureblob",
             # endpoint=storage_uri, # Removed: Rely on client credential for identity-based auth
             container=container,
             description=f"Data source for project container {container_name} (Identity-based)"
             # identity=DataSourceIdentity(...) # Could specify user-assigned identity if needed
         )

    try:
        # Check if datasource already exists
        try:
            existing_datasource = await search_indexer_client.get_data_source_connection(datasource_name)
            if existing_datasource:
                logging.info(f"Data source connection '{datasource_name}' already exists, updating it.")
                # Continue with update
        except Exception as ex:
            # Datasource doesn't exist, continue with creation
            if "ResourceNotFound" in str(ex) or "404" in str(ex) or "No data source with the name" in str(ex):
                logging.info(f"Data source connection '{datasource_name}' not found, creating new one.")
            else:
                # Some other error occurred when checking for the datasource
                raise ex

        logging.info(f"Creating or updating data source connection '{datasource_name}'...")
        await search_indexer_client.create_or_update_data_source_connection(data_source_connection)
        logging.info(f"Data source connection '{datasource_name}' created or updated successfully.")
        return True
    except Exception as e:
        # Check if the error is because of permissions or other issues
        if "Forbidden" in str(e) or "403" in str(e):
            logging.warning(f"Permission issue with data source connection '{datasource_name}', may already exist: {e}")
            return True
        # If the error is because the datasource doesn't exist, create it
        if "No data source with the name" in str(e):
            try:
                logging.info(f"Creating new data source connection '{datasource_name}'...")
                await search_indexer_client.create_data_source_connection(data_source_connection)
                logging.info(f"Data source connection '{datasource_name}' created successfully.")
                return True
            except Exception as create_ex:
                logging.error(f"Failed to create data source connection '{datasource_name}': {create_ex}")
                raise create_ex
        logging.error(f"Failed to create or update data source connection '{datasource_name}': {e}")
        raise

async def create_project_index(search_index_client: SearchIndexClient, index_name: str):
    """Creates an Azure AI Search Index for a project."""
    index_definition = create_index_definition(index_name)
    try:
        # Check if index already exists
        try:
            existing_index = await search_index_client.get_index(index_name)
            if existing_index:
                logging.info(f"Search index '{index_name}' already exists, skipping creation.")
                return True
        except Exception as ex:
            # Index doesn't exist, continue with creation
            if "ResourceNotFound" in str(ex) or "404" in str(ex) or "No index with the name" in str(ex):
                logging.info(f"Index '{index_name}' not found, creating new index.")
            else:
                # Some other error occurred when checking for the index
                raise ex

        logging.info(f"Creating search index '{index_name}'...")
        await search_index_client.create_index(index_definition)
        logging.info(f"Search index '{index_name}' created successfully.")
        return True
    except Exception as e:
        # Check if the error is because the index already exists
        if "ResourceNameAlreadyInUse" in str(e) or "CannotCreateExistingIndex" in str(e):
            logging.info(f"Search index '{index_name}' already exists, skipping creation.")
            return True
        # If the error is because the index doesn't exist, create it
        if "No index with the name" in str(e):
            try:
                logging.info(f"Creating new search index '{index_name}'...")
                await search_index_client.create_index(index_definition)
                logging.info(f"Search index '{index_name}' created successfully.")
                return True
            except Exception as create_ex:
                logging.error(f"Failed to create search index '{index_name}': {create_ex}")
                raise create_ex
        # Handle potential race conditions or existing index errors gracefully if needed
        logging.error(f"Failed to create search index '{index_name}': {e}")
        raise

async def create_project_indexer(
    search_indexer_client: SearchIndexerClient,
    indexer_name: str,
    datasource_name: str,
    index_name: str
):
    """Creates an Azure AI Search Indexer for a project."""
    # Configure the indexer. Consider adding skillsets later if needed.
    # Parse specific file types like PDF, DOCX, etc.
    parameters = IndexingParameters(
         configuration={
             "indexedFileNameExtensions": ".pdf,.docx,.pptx,.xlsx,.html,.md,.txt",
             "dataToExtract": "contentAndMetadata",
             # "imageAction": "generateNormalizedImages" # Example: if you need image processing
         }
    )

    # Define field mappings (optional, but good practice)
    # Example: map blob metadata to specific index fields
    field_mappings = [
        FieldMapping(source_field_name="metadata_storage_path", target_field_name="filepath"),
        FieldMapping(source_field_name="metadata_storage_name", target_field_name="metadata_storage_name"),
        FieldMapping(source_field_name="metadata_storage_last_modified", target_field_name="metadata_storage_last_modified"),
        FieldMapping(source_field_name="metadata_storage_size", target_field_name="metadata_storage_size"),
        # Add more mappings as needed
    ]

    indexer = SearchIndexer(
        name=indexer_name,
        description=f"Indexer for project {index_name}",
        data_source_name=datasource_name,
        target_index_name=index_name,
        parameters=parameters,
        field_mappings=field_mappings
        # schedule=IndexingSchedule(...) # Optional: define a schedule
        # skillset_name=... # Optional: add cognitive skillsets
    )

    try:
        # Check if indexer already exists
        try:
            existing_indexer = await search_indexer_client.get_indexer(indexer_name)
            if existing_indexer:
                logging.info(f"Search indexer '{indexer_name}' already exists, skipping creation.")
                return True
        except Exception as ex:
            # Indexer doesn't exist, continue with creation
            if "ResourceNotFound" in str(ex) or "404" in str(ex) or "No indexer with the name" in str(ex) or "was not found" in str(ex):
                logging.info(f"Indexer '{indexer_name}' not found, creating new indexer.")
            else:
                # Some other error occurred when checking for the indexer
                raise ex

        # Create the indexer directly without further checks
        try:
            logging.info(f"Creating search indexer '{indexer_name}'...")
            await search_indexer_client.create_indexer(indexer)
            logging.info(f"Search indexer '{indexer_name}' created successfully.")
            # Optionally run the indexer immediately after creation
            # logging.info(f"Running indexer '{indexer_name}'...")
            # await search_indexer_client.run_indexer(indexer_name)
            return True
        except Exception as create_ex:
            # Check if the error is because the indexer already exists
            if "ResourceNameAlreadyInUse" in str(create_ex) or "CannotCreateExistingIndexer" in str(create_ex):
                logging.info(f"Search indexer '{indexer_name}' already exists, skipping creation.")
                return True
            logging.error(f"Failed to create search indexer '{indexer_name}': {create_ex}")
            raise create_ex
    except Exception as e:
        logging.error(f"Failed to create search indexer '{indexer_name}': {e}")
        raise

async def delete_search_resources(
    search_service_endpoint: str,
    credential, # Should be DefaultAzureCredential usually
    index_name: str | None = None,
    indexer_name: str | None = None,
    datasource_name: str | None = None
):
    # Check if we're running locally and should use Azure CLI credentials
    use_cli_cred = os.getenv("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
    if use_cli_cred and isinstance(credential, AzureKeyCredential):
        logging.info("[SEARCH RESOURCES] Using Azure CLI credentials as specified by USE_AZURE_CLI_CREDENTIAL=true")
        from azure.identity.aio import AzureCliCredential
        credential = AzureCliCredential()
    """Attempts to delete project-specific search resources during rollback or project deletion."""
    logging.info(f"[SEARCH RESOURCES] Starting deletion of search resources")
    logging.info(f"[SEARCH RESOURCES] Resources to delete: indexer='{indexer_name}', index='{index_name}', datasource='{datasource_name}'")
    logging.info(f"[SEARCH RESOURCES] Using search service endpoint: {search_service_endpoint}")

    # Track success for each resource type
    deletion_success = {
        "indexer": False,
        "index": False,
        "datasource": False
    }

    # Delete Indexer first (depends on index and datasource)
    if indexer_name:
        try:
            logging.info(f"[SEARCH RESOURCES] Creating SearchIndexerClient for indexer deletion")
            async with SearchIndexerClient(search_service_endpoint, credential) as indexer_client:
                logging.info(f"[SEARCH RESOURCES] Attempting to delete indexer '{indexer_name}'...")
                # Check if indexer exists before attempting deletion
                try:
                    await indexer_client.get_indexer(indexer_name)
                    logging.info(f"[SEARCH RESOURCES] Indexer '{indexer_name}' exists, proceeding with deletion")
                except Exception as check_e:
                    logging.warning(f"[SEARCH RESOURCES] Indexer '{indexer_name}' may not exist: {check_e}")
                    # Continue with deletion attempt anyway, as it might be a different error

                await indexer_client.delete_indexer(indexer_name)
                deletion_success["indexer"] = True
                logging.info(f"[SEARCH RESOURCES] Successfully deleted indexer '{indexer_name}'")
        except Exception as e:
            logging.error(f"[SEARCH RESOURCES] Failed to delete indexer '{indexer_name}': {e}")
            logging.exception("[SEARCH RESOURCES] Full exception details for indexer deletion:")
    else:
        logging.info(f"[SEARCH RESOURCES] No indexer name provided, skipping indexer deletion")

    # Delete Index
    if index_name:
        try:
            logging.info(f"[SEARCH RESOURCES] Creating SearchIndexClient for index deletion")
            async with SearchIndexClient(search_service_endpoint, credential) as index_client:
                logging.info(f"[SEARCH RESOURCES] Attempting to delete index '{index_name}'...")
                # Check if index exists before attempting deletion
                try:
                    await index_client.get_index(index_name)
                    logging.info(f"[SEARCH RESOURCES] Index '{index_name}' exists, proceeding with deletion")
                except Exception as check_e:
                    logging.warning(f"[SEARCH RESOURCES] Index '{index_name}' may not exist: {check_e}")
                    # Continue with deletion attempt anyway, as it might be a different error

                await index_client.delete_index(index_name)
                deletion_success["index"] = True
                logging.info(f"[SEARCH RESOURCES] Successfully deleted index '{index_name}'")
        except Exception as e:
            logging.error(f"[SEARCH RESOURCES] Failed to delete index '{index_name}': {e}")
            logging.exception("[SEARCH RESOURCES] Full exception details for index deletion:")
    else:
        logging.info(f"[SEARCH RESOURCES] No index name provided, skipping index deletion")

    # Delete Data Source Connection
    if datasource_name:
        try:
            logging.info(f"[SEARCH RESOURCES] Creating SearchIndexerClient for datasource deletion")
            async with SearchIndexerClient(search_service_endpoint, credential) as indexer_client:
                logging.info(f"[SEARCH RESOURCES] Attempting to delete data source connection '{datasource_name}'...")
                # Check if datasource exists before attempting deletion
                try:
                    await indexer_client.get_data_source_connection(datasource_name)
                    logging.info(f"[SEARCH RESOURCES] Datasource '{datasource_name}' exists, proceeding with deletion")
                except Exception as check_e:
                    logging.warning(f"[SEARCH RESOURCES] Datasource '{datasource_name}' may not exist: {check_e}")
                    # Continue with deletion attempt anyway, as it might be a different error

                await indexer_client.delete_data_source_connection(datasource_name)
                deletion_success["datasource"] = True
                logging.info(f"[SEARCH RESOURCES] Successfully deleted data source connection '{datasource_name}'")
        except Exception as e:
            logging.error(f"[SEARCH RESOURCES] Failed to delete data source connection '{datasource_name}': {e}")
            logging.exception("[SEARCH RESOURCES] Full exception details for datasource deletion:")
    else:
        logging.info(f"[SEARCH RESOURCES] No datasource name provided, skipping datasource deletion")

    # Log summary of deletion results
    logging.info(f"[SEARCH RESOURCES] Deletion summary:")
    logging.info(f"[SEARCH RESOURCES] - Indexer deleted: {deletion_success['indexer']}")
    logging.info(f"[SEARCH RESOURCES] - Index deleted: {deletion_success['index']}")
    logging.info(f"[SEARCH RESOURCES] - Datasource deleted: {deletion_success['datasource']}")

    return deletion_success
