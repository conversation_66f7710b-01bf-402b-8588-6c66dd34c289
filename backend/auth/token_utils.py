"""
Token Utilities for Delegated Authentication Flow

This module provides utilities for extracting and validating tokens from request headers,
and using them for delegated authentication with Microsoft Graph API.
"""

import logging
import os
import time
from typing import Dict, Any, Optional, Tuple
import jwt
import requests
from fastapi import Request, HTTPException, status
from backend.auth.mock_auth import get_mock_user

# Cache for token validation results to avoid unnecessary API calls
_token_validation_cache = {}
# Cache expiry time in seconds (can be overridden with env var)
TOKEN_CACHE_EXPIRY = int(os.environ.get("TOKEN_CACHE_EXPIRY", "120"))  # Default 2 minutes

def extract_token_from_request(request: Request) -> Optional[str]:
    """
    Extract the Bearer token from the Authorization header.

    Args:
        request: The FastAPI request object

    Returns:
        The token string or None if not found
    """
    # Log all headers for debugging (excluding sensitive information)
    safe_headers = {k: v for k, v in request.headers.items()
                   if k.lower() not in ('authorization', 'cookie', 'x-api-key')}
    logging.info(f"Request headers: {safe_headers}")

    auth_header = request.headers.get("Authorization")
    if not auth_header:
        logging.warning("No Authorization header found in request")
        return None

    # Log that we found an Authorization header (without showing the full token)
    if len(auth_header) > 20:
        logging.info(f"Found Authorization header: {auth_header[:10]}...{auth_header[-10:]} (truncated)")
    else:
        logging.info(f"Found Authorization header with format: {auth_header}")

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        logging.warning(f"Invalid Authorization header format: {parts[0] if len(parts) > 0 else 'empty'}")
        return None

    token = parts[1]
    logging.info(f"Successfully extracted token: {token[:10]}...{token[-10:]} (truncated)")
    return token

async def validate_token(token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Validate the token and extract its claims.

    Args:
        token: The JWT token to validate or Easy Auth principal token

    Returns:
        A tuple of (is_valid, claims)
    """
    # Check cache first
    cache_key = token
    if cache_key in _token_validation_cache:
        cache_entry = _token_validation_cache[cache_key]
        if time.time() < cache_entry["expires_at"]:
            # Ensure token has not expired since it was cached
            claims = cache_entry.get("claims")
            exp = None
            if claims and isinstance(claims, dict):
                exp = claims.get("exp")
            if exp and exp >= time.time():
                return cache_entry["is_valid"], claims
            # If expired or exp missing, drop from cache and continue
            _token_validation_cache.pop(cache_key, None)

    try:
        # Handle special Easy Auth principal tokens
        if token.startswith("easy_auth_principal:"):
            email = token.replace("easy_auth_principal:", "")
            logging.info(f"✅ TOKEN_VALIDATION_DEBUG: Validating Easy Auth principal token for {email}")

            # Create mock claims for Easy Auth principal
            claims = {
                "email": email,
                "preferred_username": email,
                "name": email.split("@")[0].replace(".", " ").title(),
                "aud": "easy_auth",
                "iss": "azure_app_service_easy_auth",
                "exp": int(time.time()) + 3600  # Valid for 1 hour
            }

            _cache_validation_result(token, True, claims)
            return True, claims

        # For basic validation, we'll just decode the token without verification
        # In a production environment, you should verify the token signature
        # using the public key from the issuer

        # Handle token type issues - ensure we're working with a string
        if isinstance(token, bytes):
            token_str = token.decode('utf-8')
        else:
            token_str = str(token)

        claims = jwt.decode(token_str, options={"verify_signature": False})

        # Check if token is expired
        if "exp" in claims and claims["exp"] < time.time():
            logging.warning("Token is expired")
            _cache_validation_result(token, False, None)
            return False, None

        # Cache the result
        _cache_validation_result(token, True, claims)
        return True, claims
    except Exception as e:
        logging.error(f"Error validating token: {e}")
        _cache_validation_result(token, False, None)
        return False, None

def _cache_validation_result(token: str, is_valid: bool, claims: Optional[Dict[str, Any]]) -> None:
    """Cache the token validation result"""
    _token_validation_cache[token] = {
        "is_valid": is_valid,
        "claims": claims,
        "expires_at": time.time() + TOKEN_CACHE_EXPIRY
    }

async def get_user_info_from_graph(token: str) -> Optional[Dict[str, Any]]:
    """
    Get user information from Microsoft Graph API using the delegated token.

    Args:
        token: The delegated access token or Easy Auth principal token

    Returns:
        User information or None if the request fails
    """
    if not token:
        logging.warning("No token provided to get_user_info_from_graph")
        return None

    try:
        # Handle special Easy Auth principal tokens
        if token.startswith("easy_auth_principal:"):
            email = token.replace("easy_auth_principal:", "")
            logging.info(f"✅ GRAPH_API_DEBUG: Using Easy Auth principal data for {email}")

            # Extract name from email (simple approach)
            name_part = email.split("@")[0].replace(".", " ").title()

            return {
                "id": email,  # Use email as ID for Easy Auth users
                "name": name_part,
                "email": email,
                # Don't set a default role here - it will be set from the database
                # or defaulted to REGULAR_USER later if not found in the database
            }

        # Call Microsoft Graph API to get user info
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        logging.info("Calling Microsoft Graph API with delegated token")
        logging.debug(f"Token: {token[:10]}...{token[-10:]} (truncated)")

        response = requests.get(
            "https://graph.microsoft.com/v1.0/me",
            headers=headers
        )

        if response.status_code == 200:
            user_data = response.json()
            logging.info(f"Successfully retrieved user info from Graph API: {user_data.get('displayName')}")

            return {
                "id": user_data.get("id"),
                "name": user_data.get("displayName"),
                "email": user_data.get("userPrincipalName"),
                # Don't set a default role here - it will be set from the database
                # or defaulted to REGULAR_USER later if not found in the database
            }
        else:
            logging.error(f"Error getting user info from Graph API: {response.status_code} - {response.text}")

            # If Graph API fails, try to extract user info from token claims
            # This is common when using ID tokens instead of access tokens
            try:
                import jwt
                token_parts = token.split('.')
                if len(token_parts) == 3:  # Valid JWT format
                    payload = jwt.decode_complete(token, options={"verify_signature": False})["payload"]
                    logging.info(f"✅ GRAPH_API_DEBUG: Graph API failed, extracting user info from token claims")
                    logging.info(f"🔍 GRAPH_API_DEBUG: Token payload aud: {payload.get('aud')}")
                    logging.info(f"🔍 GRAPH_API_DEBUG: Token payload scp: {payload.get('scp')}")

                    # Extract user information from token claims
                    user_email = payload.get('preferred_username') or payload.get('email') or payload.get('upn')
                    user_name = payload.get('name')
                    user_id = payload.get('oid') or payload.get('sub')

                    if user_email:
                        logging.info(f"✅ GRAPH_API_DEBUG: Extracted user info from token claims: {user_name} ({user_email})")

                        # If we don't have a name, try to create one from email
                        if not user_name:
                            name_part = user_email.split("@")[0].replace(".", " ").title()
                            user_name = name_part

                        return {
                            "id": user_id or user_email,  # Use email as fallback ID
                            "name": user_name,
                            "email": user_email,
                        }
                    else:
                        logging.error("❌ GRAPH_API_DEBUG: No email found in token claims")
                else:
                    logging.warning("❌ GRAPH_API_DEBUG: Token is not in valid JWT format")
            except Exception as jwt_error:
                logging.error(f"❌ GRAPH_API_DEBUG: Error extracting user info from token claims: {jwt_error}")

            return None
    except Exception as e:
        logging.error(f"Error calling Microsoft Graph API: {e}")
        return None

async def get_authenticated_user_with_token(request: Request) -> Dict[str, Any]:
    """
    Get the authenticated user using the token from the request.

    This function extracts the token from the request, validates it,
    and uses it to get user information from Microsoft Graph API.
    If the token is invalid or missing, it falls back to the mock user.

    Args:
        request: The FastAPI request object

    Returns:
        User information
    """
    # Extract token from request using Easy Auth-aware function
    from backend.auth.entra_auth import extract_token_from_request_with_easy_auth
    token = extract_token_from_request_with_easy_auth(request)
    if not token:
        logging.warning("No token found in request, using mock user")
        return await get_mock_user()

    # Validate token
    is_valid, claims = await validate_token(token)
    if not is_valid:
        logging.warning("Invalid token, using mock user")
        return await get_mock_user()

    # Get user info from Microsoft Graph API
    user_info = await get_user_info_from_graph(token)
    if not user_info:
        logging.warning("Failed to get user info from Graph API, using mock user")
        return await get_mock_user()

    return user_info
