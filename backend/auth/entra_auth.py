"""
Entra ID Authentication Client for local development.

This module provides functions to authenticate with Entra ID using client credentials
from environment variables, allowing local development to use the same authentication
as the production environment.

It also supports delegated authentication flow, where the frontend passes a token
to the backend, which then uses it to access Microsoft Graph API.
"""

import os
import logging
import json
import time
from typing import Dict, Any, Optional
import requests
import jwt
from fastapi import Request, HTTPException, status
from azure.identity import ClientSecretCredential
from backend.auth.mock_auth import get_mock_user
from backend.auth.token_utils import extract_token_from_request, get_user_info_from_graph

# Get environment variables
AZURE_CLIENT_ID = os.environ.get("AZURE_CLIENT_ID")
AZURE_TENANT_ID = os.environ.get("AZURE_TENANT_ID")
AZURE_APP_SECRET = os.environ.get("AZURE_APP_SECRET")

# Cache for access tokens to avoid unnecessary requests
_token_cache = {}
# Allow falling back to client credentials when no delegated token is supplied
ALLOW_CLIENT_CREDENTIALS_FALLBACK = os.environ.get("ALLOW_CLIENT_CREDENTIALS_FALLBACK", "false").lower() == "true"

def is_entra_auth_configured() -> bool:
    """Check if Entra ID authentication is configured with environment variables."""
    return all([AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_APP_SECRET])

async def get_authenticated_user(request: Request) -> Dict[str, Any]:
    """
    Get the authenticated user information from the request.

    This function validates the token and retrieves user information from Microsoft Graph.
    """
    try:
        token = extract_token_from_request(request)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No authentication token provided"
            )

        user_info = await get_user_info_from_graph(token)
        if not user_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to validate token"
            )

        return user_info
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error getting authenticated user: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to authenticate user"
        )

async def get_entra_token() -> Optional[str]:
    """Get an access token for Entra ID authentication."""
    if not is_entra_auth_configured():
        logging.warning("Entra ID authentication not configured. Missing environment variables.")
        return None

    # Check cache first
    cache_key = f"{AZURE_CLIENT_ID}:{AZURE_TENANT_ID}"
    if cache_key in _token_cache:
        token_data = _token_cache[cache_key]
        # Check if token is still valid (with 5 minute buffer)
        if token_data["expires_at"] > time.time() + 300:
            return token_data["access_token"]

    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=AZURE_TENANT_ID,
            client_id=AZURE_CLIENT_ID,
            client_secret=AZURE_APP_SECRET
        )

        # Get token for Microsoft Graph API
        token = credential.get_token("https://graph.microsoft.com/.default")

        # Cache the token
        _token_cache[cache_key] = {
            "access_token": token.token,
            "expires_at": time.time() + token.expires_on
        }

        return token.token
    except Exception as e:
        logging.error(f"Error getting Entra ID token: {e}")
        return None

async def get_user_info(token: str) -> Optional[Dict[str, Any]]:
    """Get user information from Microsoft Graph API."""
    if not token:
        return None

    try:
        # Call Microsoft Graph API to get user info
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        response = requests.get(
            "https://graph.microsoft.com/v1.0/me",
            headers=headers
        )

        if response.status_code == 200:
            user_data = response.json()
            return {
                "id": user_data.get("id"),
                "name": user_data.get("displayName"),
                "email": user_data.get("userPrincipalName")
                # Don't set a default role here - it will be set from the database
                # or defaulted to REGULAR_USER later if not found in the database
            }
        else:
            logging.error(f"Error getting user info: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logging.error(f"Error calling Microsoft Graph API: {e}")
        return None

async def get_entra_user() -> Dict[str, Any]:
    """Get user information from Entra ID for local development.

    This function attempts to authenticate with Entra ID using client credentials
    from environment variables. If successful, it returns user information from
    Microsoft Graph API. If not, it falls back to the mock user.
    """
    if not is_entra_auth_configured():
        logging.info("Entra ID authentication not configured. Using mock user.")
        return await get_mock_user()

    token = await get_entra_token()
    if not token:
        logging.warning("Failed to get Entra ID token. Using mock user.")
        return await get_mock_user()

    user_info = await get_user_info(token)
    if not user_info:
        logging.warning("Failed to get user info from Microsoft Graph API. Using mock user.")
        return await get_mock_user()

    # Convert to the format expected by the application
    user_data = {
        "id": user_info["id"],
        "name": user_info["name"],
        "email": user_info["email"]
    }

    # Only set a role if one was provided
    if "role" in user_info:
        user_data["role"] = user_info["role"]
    else:
        # Default to REGULAR_USER if no role is provided
        user_data["role"] = "REGULAR_USER"

    return user_data

def _is_token_expired(token: str) -> bool:
    """Return True if the token has expired or is about to expire."""
    try:
        payload = jwt.decode(token, options={"verify_signature": False})
        exp = payload.get("exp")
        if not exp:
            return True
        current = time.time()
        # Consider tokens expiring within 60 seconds as invalid
        if exp < current + 60:
            logging.warning(
                f"TOKEN_EXTRACTION_DEBUG: Token expired or within buffer (exp={exp}, current={current})"
            )
            return True
        return False
    except Exception as e:
        logging.warning(f"TOKEN_EXTRACTION_DEBUG: Could not decode token for expiry check: {e}")
        return True

def extract_token_from_request_with_easy_auth(request: Request) -> Optional[str]:
    """
    Extract the Bearer token from the Authorization header or Azure Easy Auth headers.

    This function implements the token extraction logic from token_utils.md to properly
    handle both standard Authorization headers and Azure App Service Easy Auth headers.

    Args:
        request: The FastAPI request object

    Returns:
        The token string or None if not found
    """
    logging.info("🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request")

    # Log request method and path for context
    logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Request method: {request.method}, path: {request.url.path}")

    # Log all headers for debugging (excluding sensitive information)
    safe_headers = {k: v for k, v in request.headers.items()
                   if k.lower() not in ('authorization', 'cookie', 'x-api-key')}
    logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {safe_headers}")

    # Check for Authorization header first
    auth_header = request.headers.get("Authorization")
    token = None
    if auth_header:
        logging.info("✅ TOKEN_EXTRACTION_DEBUG: Authorization header found")

        # Log header format (without showing the full token)
        if len(auth_header) > 20:
            logging.info(
                f"🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: {auth_header[:10]}...{auth_header[-10:]} (truncated, length: {len(auth_header)})"
            )
        else:
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Authorization header (short): {auth_header}")

        parts = auth_header.split()
        logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Header split into {len(parts)} parts")

        if len(parts) == 2 and parts[0].lower() == "bearer":
            candidate = parts[1]
            logging.info("✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token preview: {candidate[:10]}...{candidate[-10:]} (length: {len(candidate)})")

            if not _is_token_expired(candidate):
                token = candidate
            else:
                logging.warning("❌ TOKEN_EXTRACTION_DEBUG: Authorization token expired or near expiry")
        else:
            logging.warning("❌ TOKEN_EXTRACTION_DEBUG: Invalid Authorization header format")
    else:
        logging.warning("❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request")
        logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Available headers: {list(request.headers.keys())}")

    if not token:
        # Fallback to Azure-specific Entra ID headers when running on Azure Web App
        id_token_header = request.headers.get("x-ms-token-aad-id-token")
        if id_token_header and not _is_token_expired(id_token_header):
            logging.info("✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-id-token header")
            token = id_token_header
        else:
            if id_token_header:
                logging.warning("❌ TOKEN_EXTRACTION_DEBUG: x-ms-token-aad-id-token header expired")

    if not token:
        access_token_header = request.headers.get("x-ms-token-aad-access-token")
        if access_token_header and not _is_token_expired(access_token_header):
            logging.info("✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-access-token header")
            token = access_token_header
        elif access_token_header:
            logging.warning("❌ TOKEN_EXTRACTION_DEBUG: x-ms-token-aad-access-token header expired")

    if not token:
        # If we don't have a valid JWT token, try to extract user info from Easy Auth principal
        # This is useful when tokens are expired but Easy Auth is still providing user context
        principal_header = request.headers.get("x-ms-client-principal")
        if principal_header:
            try:
                import base64
                import json
                decoded_principal = base64.b64decode(principal_header)
                principal_data = json.loads(decoded_principal)

                # Extract email from claims
                email = None
                for claim in principal_data.get("claims", []):
                    if claim.get("typ") == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress":
                        email = claim.get("val")
                        break

                if email:
                    logging.info(f"✅ TOKEN_EXTRACTION_DEBUG: Extracted user email from Easy Auth principal: {email}")
                    # Create a mock token with user info for downstream processing
                    # This allows the rest of the authentication flow to work
                    return f"easy_auth_principal:{email}"
                else:
                    logging.warning("❌ TOKEN_EXTRACTION_DEBUG: No email found in Easy Auth principal claims")
            except Exception as e:
                logging.error(f"❌ TOKEN_EXTRACTION_DEBUG: Error parsing Easy Auth principal: {e}")

        return None

    # Basic JWT format validation
    token_parts = token.split('.')
    if len(token_parts) == 3:
        logging.info("✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)")
        try:
            header = jwt.decode_complete(token, options={"verify_signature": False})["header"]
            payload = jwt.decode_complete(token, options={"verify_signature": False})["payload"]
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token header: {header}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: {list(payload.keys())}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: {payload.get('exp')}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token audience: {payload.get('aud')}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: {payload.get('iss')}")
        except Exception as e:
            logging.warning(f"⚠️ TOKEN_EXTRACTION_DEBUG: Error decoding token for debugging: {e}")
    else:
        logging.warning(f"⚠️ TOKEN_EXTRACTION_DEBUG: Token may not be valid JWT format - has {len(token_parts)} parts instead of 3")

    return token

async def get_entra_user_with_delegated_token(request: Request) -> Dict[str, Any]:
    """Get user information from Entra ID using a delegated token from the request.

    This function uses the token extraction approach from token_utils.md to properly
    handle Azure App Service Easy Auth tokens and standard Authorization headers.

    Args:
        request: The FastAPI request object

    Returns:
        User information
    """
    # Log all headers for debugging
    logging.info(f"All request headers: {dict(request.headers)}")

    # Use the token extraction logic from token_utils.md
    token = extract_token_from_request_with_easy_auth(request)

    if not token:
        logging.warning("No delegated token found in request.")

        # Check if we're in production - if so, we should have Easy Auth tokens
        is_development = os.environ.get('DEVELOPMENT_MODE', 'false').lower() == 'true'
        if not is_development:
            logging.error("Production environment but no Easy Auth tokens or Authorization headers found!")
            # In production, we should always have Easy Auth tokens
            # If we don't, something is wrong with the configuration
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed - no Easy Auth tokens found in production"
            )

        if ALLOW_CLIENT_CREDENTIALS_FALLBACK:
            logging.info("Falling back to client credentials.")
            return await get_entra_user()
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authorization token required")

    # Use the token to get user info from Microsoft Graph API
    user_info = await get_user_info_from_graph(token)
    if not user_info:
        logging.warning("Failed to get user info with delegated token.")
        if ALLOW_CLIENT_CREDENTIALS_FALLBACK:
            logging.info("Falling back to client credentials.")
            return await get_entra_user()
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

    # Convert to the format expected by the application
    # Ensure we have a default role if none was provided by the Graph API
    user_data = {
        "id": user_info["id"],
        "name": user_info["name"],
        "email": user_info["email"]
    }

    # Only set a default role if one wasn't provided by the Graph API
    # This allows the role to be set from the database in get_authenticated_user
    if "role" in user_info:
        user_data["role"] = user_info["role"]
    else:
        # Default to REGULAR_USER if no role is provided
        # This will be overridden by the database role in get_authenticated_user
        user_data["role"] = "REGULAR_USER"

    return user_data