# backend/deployments/deployment_status.py
"""
Module for handling deployment status updates.
"""

import asyncio
import json
import logging
import uuid
import os
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union

import httpx

from backend.deployments.models import DeploymentStatus, ResourceStatus, LogEntry

# Initialize logger
logger = logging.getLogger(__name__)


async def update_project_deployment_status(
    project_id: str,
    status: str,
    message: str,
    details: Dict[str, Any] = None,
    error: str = None,
    api_url: str = "http://localhost:50505",
) -> bool:
    """
    Update the project deployment status via the API endpoint.

    Args:
        project_id (str): The ID of the project
        status (str): The status of the deployment
        message (str): The message to display
        details (dict): Dictionary containing the details of the deployment
        error (str): Error message if any
        api_url (str): The base URL of the API

    Returns:
        bool: True if the update was successful, False otherwise
    """
    update_url = f"{api_url}/api/projects/{project_id}/deployment-status"

    try:
        # Create the status data
        status_data = {
            "status": status,
            "message": message,
            "updated_at": datetime.now(timezone.utc).isoformat(),
        }

        # Add error if provided
        if error:
            status_data["error"] = error

        # Add details if provided
        if details:
            status_data["details"] = details

        # Add headers to indicate we're using Azure CLI credentials
        headers = {
            "Content-Type": "application/json",
            "X-Azure-CLI-Credentials": "true",
        }

        # Send the update
        logger.info(f"Sending status update to {update_url}")
        async with httpx.AsyncClient() as client:
            response = await client.post(update_url, json=status_data, headers=headers)

        if response.status_code == 200:
            logger.info(
                f"Successfully updated deployment status for project {project_id}"
            )
            return True
        else:
            logger.error(
                f"Failed to update deployment status for project {project_id}: {response.status_code} {response.text}"
            )
            return False

    except Exception as e:
        logger.error(f"Error updating deployment status for project {project_id}: {e}")
        return False


class DeploymentSummary:
    """Class for handling deployment summary updates."""

    def __init__(self, project_id: str, api_url: Optional[str] = None):
        """
        Initialize the deployment summary handler.

        Args:
            project_id (str): The ID of the project being deployed
            api_url (Optional[str]): The URL of the API to update the deployment status
        """
        self.project_id = project_id
        self.api_url = api_url
        self.summary_file = f"deployment_summary_{project_id}.json"
        self.status = "pending"
        self.resources = {}
        self.errors = []
        self.timestamp = datetime.now(timezone.utc).isoformat()
        self.summary = None  # Initialize summary attribute

    def load_summary(self) -> Dict[str, Any]:
        """
        Load the deployment summary from the summary file.

        Returns:
            Dict[str, Any]: The deployment summary as a dictionary
        """
        if os.path.exists(self.summary_file):
            try:
                with open(self.summary_file, "r") as f:
                    summary = json.load(f)
                    self.status = summary.get("status", self.status)
                    self.resources = summary.get("resources", self.resources)
                    self.errors = summary.get("errors", self.errors)
                    self.timestamp = summary.get("timestamp", self.timestamp)
                    self.summary = summary  # Set the summary attribute
                    return summary
            except Exception as e:
                logger.error(f"Error loading deployment summary: {str(e)}")
                summary = {
                    "status": self.status,
                    "resources": self.resources,
                    "errors": self.errors,
                    "timestamp": self.timestamp,
                }
                self.summary = summary  # Set the summary attribute
                return summary
        else:
            summary = {
                "status": self.status,
                "resources": self.resources,
                "errors": self.errors,
                "timestamp": self.timestamp,
            }
            self.summary = summary  # Set the summary attribute
            return summary

    def save_summary(self) -> None:
        """Save the deployment summary to the summary file."""
        try:
            # Create the summary dictionary
            summary = {
                "status": self.status,
                "resources": self.resources,
                "errors": self.errors,
                "timestamp": self.timestamp,
            }

            # Update the summary attribute
            self.summary = summary

            # Write to file
            with open(self.summary_file, "w") as f:
                json.dump(summary, f, indent=2)

            logger.info(f"Deployment summary saved to {self.summary_file}")
        except Exception as e:
            logger.error(f"Error saving deployment summary: {str(e)}")

    def update_status(self, status: str, message: Optional[str] = None) -> None:
        """
        Update the deployment status.

        Args:
            status (str): The new status of the deployment
            message (Optional[str]): An optional message to include in the status update
        """
        self.status = status
        self.timestamp = datetime.now(timezone.utc).isoformat()

        # Save the summary
        self.save_summary()

        # Update the status in the database
        if self.api_url:
            asyncio.create_task(self._update_status_in_db(status, message))

    def add_resource(self, resource_name: str, resource_value: Any) -> None:
        """
        Add a resource to the deployment summary.

        Args:
            resource_name (str): The name of the resource
            resource_value (Any): The value of the resource
        """
        self.resources[resource_name] = resource_value
        self.timestamp = datetime.now(timezone.utc).isoformat()

        # Save the summary
        self.save_summary()

    def add_error(self, error: str) -> None:
        """
        Add an error to the deployment summary.

        Args:
            error (str): The error message
        """
        self.errors.append(error)
        self.timestamp = datetime.now(timezone.utc).isoformat()

        # Save the summary
        self.save_summary()

    async def _update_status_in_db(
        self, status: str, message: Optional[str] = None
    ) -> None:
        """
        Update the deployment status in the database.

        Args:
            status (str): The new status of the deployment
            message (Optional[str]): An optional message to include in the status update
        """
        try:
            # Prepare the data
            data = {"status": status}

            if message:
                data["message"] = message

            # Prepare details from resources if available
            details = None
            if self.resources:
                # Create a structured details object based on resources
                details = {
                    "storage": {
                        "storage_account": bool(
                            self.resources.get("storage_account_name")
                        ),
                        "containers": {
                            "uploads": bool(self.resources.get("uploads_container")),
                            "input": bool(self.resources.get("input_container")),
                            "output": bool(self.resources.get("output_container")),
                        },
                    },
                    "storage_complete": bool(
                        self.resources.get("storage_account_name")
                    ),
                    "search": {
                        "search_service": bool(
                            self.resources.get("search_service_name")
                        ),
                        "index": bool(self.resources.get("search_index_name")),
                        "indexer": bool(self.resources.get("search_indexer_name")),
                        "datasource": bool(
                            self.resources.get("search_datasource_name")
                        ),
                    },
                    "search_complete": bool(self.resources.get("search_service_name")),
                    "function": {
                        "function_app": bool(self.resources.get("function_app_name")),
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                    },
                    "function_complete": bool(self.resources.get("function_app_name")),
                    "overall_complete": status == "completed",
                    "completion_percentage": (
                        100
                        if status == "completed"
                        else (0 if status == "pending" else 50)
                    ),
                }

            # Send the request
            await update_project_deployment_status(
                project_id=self.project_id,
                status=status,
                message=message or f"Deployment {status}",
                details=details,
                api_url=self.api_url,
            )
            logger.info(
                f"Successfully sent deployment status update to API for project {self.project_id}"
            )
        except Exception as e:
            logger.error(f"Error updating deployment status in database: {str(e)}")


async def sync_deployment_with_project(
    deployment_id: str,
    project_id: str,
    deployment_service,
    api_url: str = "http://localhost:50505",
) -> bool:
    """
    Synchronize the deployment status with the project deployment status.

    Args:
        deployment_id (str): The ID of the deployment
        project_id (str): The ID of the project
        deployment_service: The deployment service
        api_url (str): The base URL of the API

    Returns:
        bool: True if the sync was successful, False otherwise
    """
    try:
        # Get the deployment
        deployment = await deployment_service.get_deployment(
            deployment_id=deployment_id,
            user_id="system",  # Use system user ID for internal operations
        )

        if not deployment:
            logger.error(f"Deployment {deployment_id} not found")
            return False

        # Map deployment status to project deployment status
        status = deployment.status.value
        message = f"Deployment {status}"

        # Create details
        details = {"completion_percentage": deployment.progress_percentage}

        # Map resources
        resources = deployment.resources
        if resources:
            details["storage"] = {
                "storage_account": resources.get("storage", {}).status
                == ResourceStatus.COMPLETED,
                "containers": {
                    "uploads": resources.get("storage", {}).status
                    == ResourceStatus.COMPLETED,
                    "input": resources.get("storage", {}).status
                    == ResourceStatus.COMPLETED,
                    "output": resources.get("storage", {}).status
                    == ResourceStatus.COMPLETED,
                },
            }
            details["storage_complete"] = (
                resources.get("storage", {}).status == ResourceStatus.COMPLETED
            )

            details["search"] = {
                "search_service": resources.get("search", {}).status
                == ResourceStatus.COMPLETED,
                "index": resources.get("search", {}).status == ResourceStatus.COMPLETED,
                "indexer": resources.get("search", {}).status
                == ResourceStatus.COMPLETED,
                "datasource": resources.get("search", {}).status
                == ResourceStatus.COMPLETED,
            }
            details["search_complete"] = (
                resources.get("search", {}).status == ResourceStatus.COMPLETED
            )

            details["function"] = {
                "function_app": resources.get("function_app", {}).status
                == ResourceStatus.COMPLETED,
                "event_grid": resources.get("event_grid", {}).status
                == ResourceStatus.COMPLETED,
                "maturity_assessment": resources.get("function_app", {}).status
                == ResourceStatus.COMPLETED,
                "executive_summary": resources.get("function_app", {}).status
                == ResourceStatus.COMPLETED,
            }
            details["function_complete"] = (
                resources.get("function_app", {}).status == ResourceStatus.COMPLETED
            )

            details["overall_complete"] = (
                deployment.status == DeploymentStatus.COMPLETED
            )

        # Get error if any
        error = None
        if deployment.logs:
            error_logs = [log for log in deployment.logs if log.level == "error"]
            if error_logs:
                error = error_logs[0].message

        # Update the project deployment status
        return await update_project_deployment_status(
            project_id=project_id,
            status=status,
            message=message,
            details=details,
            error=error,
            api_url=api_url,
        )

    except Exception as e:
        logger.error(
            f"Error synchronizing deployment {deployment_id} with project {project_id}: {e}"
        )
        return False


async def update_deployment_summary(
    project_id: str,
    status: str,
    message: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    api_url: Optional[str] = None,
    max_retries: int = 3,
    retry_delay: float = 2.0,
) -> bool:
    """
    Update the deployment status of a project with retry logic.

    Args:
        project_id (str): The ID of the project being deployed
        status (str): The new status of the deployment
        message (Optional[str]): An optional message to include in the status update
        details (Optional[Dict[str, Any]]): Additional details to include in the status update
        api_url (Optional[str]): The URL of the API to update the deployment status
        max_retries (int): Maximum number of retry attempts
        retry_delay (float): Delay between retry attempts in seconds

    Returns:
        bool: True if the update was successful, False otherwise
    """
    for attempt in range(1, max_retries + 1):
        try:
            # Create a deployment summary handler
            deployment_summary = DeploymentSummary(project_id, api_url)

            # Load the existing summary
            deployment_summary.load_summary()

            # Update the status
            deployment_summary.update_status(status, message)

            # Update details if provided
            if details:
                if (
                    not hasattr(deployment_summary, "summary")
                    or not deployment_summary.summary
                ):
                    logging.warning(
                        f"Cannot update details: summary not loaded for project {project_id}"
                    )
                    # Create a new summary if it doesn't exist
                    if (
                        not hasattr(deployment_summary, "summary")
                        or not deployment_summary.summary
                    ):
                        deployment_summary.summary = {
                            "status": status,
                            "resources": deployment_summary.resources,
                            "errors": deployment_summary.errors,
                            "timestamp": deployment_summary.timestamp,
                            "deployment_status": {
                                "status": status,
                                "message": message or f"Deployment {status}",
                                "updated_at": datetime.now(timezone.utc).isoformat(),
                                "details": {},
                            },
                        }

                # Ensure deployment_status exists and has a details field
                if "deployment_status" not in deployment_summary.summary:
                    deployment_summary.summary["deployment_status"] = {}
                if "details" not in deployment_summary.summary["deployment_status"]:
                    deployment_summary.summary["deployment_status"]["details"] = {}

                # Update the details
                deployment_summary.summary["deployment_status"]["details"].update(
                    details
                )

                # Save the updated summary
                deployment_summary.save_summary()

            logging.info(
                f"Successfully updated deployment status to '{status}' for project {project_id}"
            )
            return True

        except Exception as e:
            logging.error(
                f"Attempt {attempt}/{max_retries} failed to update deployment status: {e}"
            )
            if attempt < max_retries:
                logging.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                logging.error(
                    f"Failed to update deployment status after {max_retries} attempts"
                )
                return False


async def add_deployment_resource(
    project_id: str,
    resource_name: str,
    resource_value: Any,
    api_url: Optional[str] = None,
    max_retries: int = 3,
    retry_delay: float = 2.0,
) -> bool:
    """
    Add a resource to the deployment summary with retry logic.

    Args:
        project_id (str): The ID of the project being deployed
        resource_name (str): The name of the resource
        resource_value (Any): The value of the resource
        api_url (Optional[str]): The URL of the API to update the deployment status
        max_retries (int): Maximum number of retry attempts
        retry_delay (float): Delay between retry attempts in seconds

    Returns:
        bool: True if the update was successful, False otherwise
    """
    for attempt in range(1, max_retries + 1):
        try:
            # Create a deployment summary handler
            deployment_summary = DeploymentSummary(project_id, api_url)

            # Load the existing summary
            deployment_summary.load_summary()

            # Add the resource
            deployment_summary.add_resource(resource_name, resource_value)

            logging.info(
                f"Successfully added resource {resource_name} to deployment summary for project {project_id}"
            )
            return True

        except Exception as e:
            logging.error(
                f"Attempt {attempt}/{max_retries} failed to add resource to deployment summary: {e}"
            )
            if attempt < max_retries:
                logging.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                logging.error(
                    f"Failed to add resource to deployment summary after {max_retries} attempts"
                )
                return False


async def add_deployment_error(
    project_id: str,
    error: str,
    api_url: Optional[str] = None,
    max_retries: int = 3,
    retry_delay: float = 2.0,
) -> bool:
    """
    Add an error to the deployment summary with retry logic.

    Args:
        project_id (str): The ID of the project being deployed
        error (str): The error message
        api_url (Optional[str]): The URL of the API to update the deployment status
        max_retries (int): Maximum number of retry attempts
        retry_delay (float): Delay between retry attempts in seconds

    Returns:
        bool: True if the update was successful, False otherwise
    """
    for attempt in range(1, max_retries + 1):
        try:
            # Create a deployment summary handler
            deployment_summary = DeploymentSummary(project_id, api_url)

            # Load the existing summary
            deployment_summary.load_summary()

            # Add the error
            deployment_summary.add_error(error)

            logging.info(
                f"Successfully added error to deployment summary for project {project_id}"
            )
            return True

        except Exception as e:
            logging.error(
                f"Attempt {attempt}/{max_retries} failed to add error to deployment summary: {e}"
            )
            if attempt < max_retries:
                logging.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
            else:
                logging.error(
                    f"Failed to add error to deployment summary after {max_retries} attempts"
                )
                return False


async def verify_deployment_status(
    project_id: str, api_url: Optional[str] = None
) -> Dict[str, Any]:
    """
    Verify the deployment status by checking actual Azure resources against the CosmosDB entry.

    Args:
        project_id (str): The ID of the project to verify
        api_url (Optional[str]): The URL of the API to update the deployment status

    Returns:
        Dict[str, Any]: A dictionary containing the verification results
    """
    logging.info(f"Verifying deployment status for project {project_id}")

    # Create a deployment summary handler
    deployment_summary = DeploymentSummary(project_id, api_url)

    # Load the existing summary
    summary = deployment_summary.load_summary()

    if not summary:
        logging.error(f"No deployment summary found for project {project_id}")
        return {"status": "error", "message": "No deployment summary found"}

    # Get the current status from the summary
    current_status = summary.get("deployment_status", {}).get("status", "unknown")

    # If the status is already 'completed' or 'failed', no need to verify
    if current_status in ["completed", "failed"]:
        logging.info(
            f"Deployment status for project {project_id} is already {current_status}, no verification needed"
        )
        return {
            "status": current_status,
            "message": f"Deployment status is {current_status}",
        }

    # If the status is 'pending' or 'in_progress', check the actual resources
    try:
        # This would typically involve checking Azure resources
        # For now, we'll just log that we would check resources
        logging.info(f"Would check Azure resources for project {project_id}")

        # Return a placeholder result
        return {
            "status": "verified",
            "message": "Deployment status verified",
            "current_status": current_status,
            "resources_checked": True,
        }
    except Exception as e:
        logging.error(
            f"Error verifying deployment status for project {project_id}: {e}"
        )
        return {"status": "error", "message": f"Error verifying deployment status: {e}"}


async def background_status_checker(
    api_url: Optional[str] = None,
    check_interval: int = 300,  # 5 minutes
    max_age_hours: int = 24,  # Only check projects deployed in the last 24 hours
    max_concurrent: int = 5,  # Maximum number of concurrent checks
) -> None:
    """
    Background job that periodically checks for discrepancies between actual resources and CosmosDB entries.

    Args:
        api_url (Optional[str]): The URL of the API to update the deployment status
        check_interval (int): Interval between checks in seconds
        max_age_hours (int): Only check projects deployed in the last N hours
        max_concurrent (int): Maximum number of concurrent checks
    """
    logging.info(
        f"Starting background deployment status checker (interval: {check_interval}s)"
    )

    while True:
        try:
            # Get a list of recent projects that might need verification
            projects = await get_recent_projects(api_url, max_age_hours)

            if not projects:
                logging.info("No recent projects found for verification")
            else:
                logging.info(f"Found {len(projects)} recent projects for verification")

                # Process projects in batches to limit concurrency
                for i in range(0, len(projects), max_concurrent):
                    batch = projects[i : i + max_concurrent]
                    tasks = [
                        verify_and_update_project(project_id, api_url)
                        for project_id in batch
                    ]
                    await asyncio.gather(*tasks)

        except Exception as e:
            logging.error(f"Error in background status checker: {e}")

        # Sleep until the next check
        logging.info(f"Background status checker sleeping for {check_interval} seconds")
        await asyncio.sleep(check_interval)


async def get_recent_projects(
    api_url: Optional[str] = None, max_age_hours: int = 24
) -> List[str]:
    """
    Get a list of recent projects that might need verification.

    Args:
        api_url (Optional[str]): The URL of the API to get projects
        max_age_hours (int): Only get projects deployed in the last N hours

    Returns:
        List[str]: A list of project IDs
    """
    # This would typically involve querying the database
    # For now, we'll just return an empty list
    logging.info(
        f"Would query database for projects deployed in the last {max_age_hours} hours"
    )
    return []


async def verify_and_update_project(
    project_id: str, api_url: Optional[str] = None
) -> None:
    """
    Verify a project's deployment status and update it if necessary.

    Args:
        project_id (str): The ID of the project to verify
        api_url (Optional[str]): The URL of the API to update the deployment status
    """
    try:
        # Verify the deployment status
        result = await verify_deployment_status(project_id, api_url)

        # If verification found discrepancies, update the status
        if result.get("status") == "verified" and result.get("discrepancies"):
            logging.info(
                f"Found discrepancies in deployment status for project {project_id}, updating..."
            )

            # Update the deployment status
            await update_deployment_summary(
                project_id=project_id,
                status="updated",
                message="Deployment status updated by background checker",
                details={"verification_result": result},
                api_url=api_url,
            )

            logging.info(
                f"Successfully updated deployment status for project {project_id}"
            )
        else:
            logging.info(
                f"No discrepancies found in deployment status for project {project_id}"
            )

    except Exception as e:
        logging.error(f"Error verifying and updating project {project_id}: {e}")


def start_background_checker(api_url: Optional[str] = None) -> asyncio.Task:
    """
    Start the background status checker as an asyncio task.

    Args:
        api_url (Optional[str]): The URL of the API to update the deployment status

    Returns:
        asyncio.Task: The background task
    """
    logging.info("Starting background deployment status checker")

    # Create the background task
    task = asyncio.create_task(
        background_status_checker(
            api_url=api_url,
            check_interval=300,  # 5 minutes
            max_age_hours=24,  # Only check projects deployed in the last 24 hours
            max_concurrent=5,  # Maximum number of concurrent checks
        )
    )

    # Add a callback to log when the task is done
    def on_task_done(task):
        try:
            # Get the result to handle any exceptions
            task.result()
        except asyncio.CancelledError:
            logging.info("Background status checker was cancelled")
        except Exception as e:
            logging.error(f"Background status checker failed with error: {e}")

    task.add_done_callback(on_task_done)

    return task


# This can be used to start the background checker when the application starts
# Example usage:
# background_task = start_background_checker(api_url="http://localhost:50505")
