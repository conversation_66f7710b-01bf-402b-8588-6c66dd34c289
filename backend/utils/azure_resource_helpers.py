import logging
import uuid
from typing import Dict, List

from azure.mgmt.storage import StorageManagementClient
from azure.mgmt.storage.models import StorageAccountCreateParameters, Sku, SkuName, Kind
from azure.mgmt.search import SearchManagementClient
from azure.mgmt.web import WebSiteManagementClient
from azure.mgmt.web.models import AppServicePlan, SkuDescription, Site, ManagedServiceIdentity, SiteConfigResource
from azure.mgmt.eventgrid import EventGridManagementClient
from azure.mgmt.eventgrid.models import SystemTopic, EventSubscription, WebHookEventSubscriptionDestination
from azure.core.credentials import AzureKeyCredential
from azure.search.documents.indexes import SearchIndexClient, SearchIndexerClient
from azure.search.documents.indexes.models import SearchIndex, SimpleField, SearchFieldDataType, SearchIndexer, SearchIndexerDataSourceConnection, SearchIndexerDataContainer


logger = logging.getLogger(__name__)


def create_storage_account_and_containers(
    storage_client: StorageManagementClient,
    resource_group: str,
    location: str,
    project_id: str,
    container_names: List[str] | None = None,
) -> Dict[str, str]:
    """Create a storage account and blob containers."""
    container_names = container_names or ["uploads", "input", "output"]
    account_base = f"st{project_id.replace('-', '')[:18]}"
    account_name = account_base.lower()

    storage_client.storage_accounts.begin_create(
        resource_group_name=resource_group,
        account_name=account_name,
        parameters=StorageAccountCreateParameters(
            sku=Sku(name=SkuName.standard_lrs),
            kind=Kind.storage_v2,
            location=location,
        ),
    ).result()

    for cname in container_names:
        storage_client.blob_containers.create(
            resource_group_name=resource_group,
            account_name=account_name,
            container_name=cname,
            blob_container={},
        )

    result = {"storage_account_name": account_name}
    if container_names:
        mapping = ["uploads_container", "input_container", "output_container"]
        for key, name in zip(mapping, container_names):
            result[key] = name
    return result


def create_search_service_and_resources(
    search_client: SearchManagementClient,
    resource_group: str,
    location: str,
    service_name: str,
    storage_account_name: str,
    storage_account_key: str,
    container_name: str,
    index_name: str,
    datasource_name: str,
    indexer_name: str,
) -> Dict[str, str]:
    """Create a search service and basic index resources."""
    search_client.services.begin_create_or_update(
        resource_group_name=resource_group,
        search_service_name=service_name,
        service_envelope={
            "location": location,
            "sku": {"name": "basic"},
            "replica_count": 1,
            "partition_count": 1,
        },
    ).result()

    keys = search_client.admin_keys.get(resource_group, service_name)
    admin_key = keys.primary_key
    endpoint = f"https://{service_name}.search.windows.net"
    cred = AzureKeyCredential(admin_key)
    index_client = SearchIndexClient(endpoint=endpoint, credential=cred)
    indexer_client = SearchIndexerClient(endpoint=endpoint, credential=cred)

    try:
        index_client.create_index(
            SearchIndex(
                name=index_name,
                fields=[SimpleField(name="id", type=SearchFieldDataType.String, key=True)],
            )
        )
    except Exception:  # pragma: no cover - best effort
        logger.info("Index already exists or could not be created")

    ds = SearchIndexerDataSourceConnection(
        name=datasource_name,
        type="azureblob",
        connection_string=f"DefaultEndpointsProtocol=https;AccountName={storage_account_name};AccountKey={storage_account_key};EndpointSuffix=core.windows.net",
        container=SearchIndexerDataContainer(name=container_name),
    )
    try:
        indexer_client.create_or_update_data_source_connection(ds)
    except Exception:  # pragma: no cover
        logger.info("Data source exists or could not be created")

    indexer = SearchIndexer(
        name=indexer_name,
        data_source_name=datasource_name,
        target_index_name=index_name,
    )
    try:
        indexer_client.create_indexer(indexer)
    except Exception:  # pragma: no cover
        logger.info("Indexer exists or could not be created")

    return {
        "search_service_name": service_name,
        "search_index_name": index_name,
        "search_indexer_name": indexer_name,
        "search_datasource_name": datasource_name,
    }


def create_function_app_from_acr(
    web_client: WebSiteManagementClient,
    resource_group: str,
    location: str,
    project_id: str,
    acr_name: str,
    image_name: str,
    image_tag: str,
    app_settings: Dict[str, str] | None = None,
) -> Dict[str, str]:
    """Create a Function App using a container image from ACR."""
    rnd = uuid.uuid4().hex[:4]
    function_app_name = f"func-{project_id}-{rnd}"
    plan_name = f"plan-{function_app_name}"

    web_client.app_service_plans.begin_create_or_update(
        resource_group_name=resource_group,
        name=plan_name,
        app_service_plan=AppServicePlan(
            location=location,
            sku=SkuDescription(name="B1", tier="Basic"),
            kind="linux",
            reserved=True,
        ),
    ).result()

    site = Site(
        location=location,
        kind="functionapp,linux,container",
        server_farm_id=plan_name,
        identity=ManagedServiceIdentity(type="SystemAssigned"),
    )
    web_client.web_apps.begin_create_or_update(
        resource_group_name=resource_group,
        name=function_app_name,
        site_envelope=site,
    ).result()

    acr_login_server = f"{acr_name}.azurecr.io"
    linux_fx_version = f"DOCKER|{acr_login_server}/{image_name}:{image_tag}"
    settings = [
        {"name": "FUNCTIONS_EXTENSION_VERSION", "value": "~4"},
        {"name": "FUNCTIONS_WORKER_RUNTIME", "value": "python"},
    ]
    if app_settings:
        for k, v in app_settings.items():
            settings.append({"name": k, "value": v})

    config = SiteConfigResource(linux_fx_version=linux_fx_version, app_settings=settings)
    web_client.web_apps.begin_create_or_update_configuration(
        resource_group_name=resource_group,
        name=function_app_name,
        site_config=config,
    ).result()

    return {"function_app_name": function_app_name, "function_app_url": f"https://{function_app_name}.azurewebsites.net"}


def create_event_grid_topic_and_subscription(
    eventgrid_client: EventGridManagementClient,
    resource_group: str,
    location: str,
    topic_name: str,
    source_resource_id: str,
    subscription_name: str,
    endpoint_url: str,
) -> Dict[str, str]:
    """Create a system topic and subscription."""
    eventgrid_client.system_topics.begin_create_or_update(
        resource_group_name=resource_group,
        system_topic_name=topic_name,
        system_topic_info=SystemTopic(location=location, source=source_resource_id, topic_type="Microsoft.Storage.StorageAccounts"),
    ).result()

    eventgrid_client.system_topic_event_subscriptions.begin_create_or_update(
        resource_group_name=resource_group,
        system_topic_name=topic_name,
        event_subscription_name=subscription_name,
        event_subscription_info=EventSubscription(destination=WebHookEventSubscriptionDestination(endpoint_url=endpoint_url)),
    ).result()

    return {
        "event_grid_system_topic_name": topic_name,
        "event_grid_subscription_name": subscription_name,
    }
