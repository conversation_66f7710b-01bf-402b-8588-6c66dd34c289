import os
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional

from azure.storage.blob import (
    generate_account_sas,
    ResourceTypes,
    AccountSasPermissions,
)
from azure.mgmt.storage.aio import StorageManagementClient
from azure.mgmt.web.aio import WebSiteManagementClient

from backend.utils.azure_credentials import get_management_credential_async

logger = logging.getLogger(__name__)


async def _get_function_key(
    web_client: WebSiteManagementClient,
    resource_group: str,
    app_name: str,
    function_name: str,
) -> Optional[str]:
    """Retrieve a function key for the given function name."""
    try:
        result = await web_client.web_apps.list_function_keys(
            resource_group, app_name, function_name
        )
        # Handle StringDictionary object
        if result:
            # Log the actual response for debugging
            logger.debug(f"Function keys response type for {function_name}: {type(result)}")
            logger.debug(f"Function keys response dir for {function_name}: {dir(result)}")
            
            # Try to convert to string to see content
            try:
                logger.debug(f"Function keys response str for {function_name}: {str(result)}")
            except Exception as exc:
                logger.debug(
                    f"Could not stringify function keys result for {function_name}: {exc}"
                )
            
            # StringDictionary objects typically have keys as direct attributes
            # Try to access 'default' as an attribute first
            if hasattr(result, 'default'):
                return result.default
            
            # Some SDK versions might use 'properties' wrapper
            if hasattr(result, 'properties'):
                properties = result.properties
                if properties:
                    logger.debug(f"Properties available: {dir(properties)}")
                    if hasattr(properties, 'default'):
                        return properties.default
                    # Try dictionary-style access on properties
                    if hasattr(properties, '__getitem__'):
                        try:
                            return properties['default']
                        except (KeyError, TypeError):
                            pass
            
            # Try dictionary-style access on result directly
            if hasattr(result, '__getitem__'):
                try:
                    return result['default']
                except (KeyError, TypeError):
                    pass
            
            # Try to access as dictionary items
            if hasattr(result, 'items'):
                try:
                    items = list(result.items())
                    logger.debug(f"Available items in response: {items}")
                    for key, value in items:
                        if key == 'default':
                            return value
                except Exception as e:
                    logger.debug(f"Error iterating items: {e}")
            
            # Check if it has any key access method
            if hasattr(result, 'keys'):
                try:
                    available_keys = list(result.keys())
                    logger.warning(f"Available function keys for {function_name}: {available_keys}")
                    # If there's no 'default' key but other keys exist, use the first one
                    if available_keys and 'default' not in available_keys:
                        first_key = available_keys[0]
                        logger.info(f"Using '{first_key}' key instead of 'default' for {function_name}")
                        try:
                            # Try different access methods for the key
                            if hasattr(result, first_key):
                                return getattr(result, first_key)
                            elif hasattr(result, '__getitem__'):
                                return result[first_key]
                        except Exception as exc:
                            logger.debug(
                                f"Error retrieving key '{first_key}' for {function_name}: {exc}"
                            )
                    elif not available_keys:
                        logger.warning(f"Function {function_name} has no keys available. It may not be fully deployed yet.")
                except Exception as e:
                    logger.debug(f"Error accessing keys: {e}")
            
            # Last resort: try to iterate and find 'default'
            if hasattr(result, '__dict__'):
                result_dict = result.__dict__
                logger.debug(f"Object __dict__ keys: {list(result_dict.keys())}")
                if 'default' in result_dict:
                    return result_dict['default']
                    
            logger.warning(f"Could not find 'default' key in function keys response for {function_name}")
    except Exception as exc:  # pragma: no cover - network required
        logger.warning(f"Could not fetch key for {function_name}: {exc}")
    return None


async def refresh_project_env_vars(cosmos_client, project_id: str) -> bool:
    """Populate missing environment variables for a project from Azure resources.

    This checks the Cosmos DB document for missing values like the storage SAS
    token or function keys. If they are absent, the function attempts to
    retrieve them from Azure and updates the document accordingly.
    """
    # Temporarily set debug logging to see function key response details
    original_level = logger.level
    logger.setLevel(logging.DEBUG)

    storage_client = None
    web_client = None
    cred = None

    try:
        if not cosmos_client:
            logger.error("Cosmos client not available for environment refresh")
            return False

        subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
        resource_group = os.getenv("AZURE_RESOURCE_GROUP")
        cred = get_management_credential_async()

        storage_client = (
            StorageManagementClient(cred, subscription_id) if subscription_id else None
        )
        web_client = (
            WebSiteManagementClient(cred, subscription_id) if subscription_id else None
        )

        project = None
        if hasattr(cosmos_client, "get_project_by_id"):
            try:
                project = await cosmos_client.get_project_by_id(project_id)
            except Exception as exc:
                logger.error(f"Error retrieving project {project_id}: {exc}")
        if not project:
            logger.warning(f"Project {project_id} not found for environment refresh")
            return False

        env = project.get("environment", {})
        updated = False

        storage_name = project.get("storage_account_name") or env.get(
            "STORAGE_ACCOUNT_NAME"
        )
        if (
            storage_name
            and resource_group
            and storage_client
            and not env.get("STORAGE_ACCOUNT_SAS_TOKEN")
        ):
            try:
                keys = await storage_client.storage_accounts.list_keys(
                    resource_group, storage_name
                )
                account_key = keys.keys[0].value if keys.keys else None
                if account_key:
                    sas = generate_account_sas(
                        account_name=storage_name,
                        account_key=account_key,
                        resource_types=ResourceTypes(
                            service=True, container=True, object=True
                        ),
                        permission=AccountSasPermissions(
                            read=True,
                            write=True,
                            delete=True,
                            list=True,
                            add=True,
                            create=True,
                            update=True,
                            process=True,
                        ),
                        expiry=datetime.now(timezone.utc) + timedelta(days=365),
                    )
                    env["STORAGE_ACCOUNT_SAS_TOKEN"] = sas
                    updated = True
            except Exception as exc:  # pragma: no cover - network required
                logger.warning(f"Failed to generate SAS token for {storage_name}: {exc}")

        function_app = project.get("function_app_name")
        if function_app and resource_group and web_client:
            host = f"https://{function_app}.azurewebsites.net"
            if not env.get("FUNCTION_APP_URL"):
                env["FUNCTION_APP_URL"] = host
                updated = True
            func_map = [
                (
                    "HttpTriggerAppMaturityAssessment",
                    "FUNCTION_KEY_MATURITY",
                    "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL",
                ),
                (
                    "HttpTriggerAppExecutiveSummary",
                    "FUNCTION_KEY_EXECUTIVE_SUMMARY",
                    "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL",
                ),
                ("HttpTriggerPowerPointGenerator", "FUNCTION_KEY_POWERPOINT", None),
            ]
            for func, key_field, url_field in func_map:
                if not env.get(key_field):
                    key = await _get_function_key(
                        web_client, resource_group, function_app, func
                    )
                    if key:
                        env[key_field] = key
                        if url_field:
                            env[url_field] = f"{host}/api/{func}"
                        updated = True

        if updated:
            project["environment"] = env
            project["updated_at"] = datetime.now(timezone.utc).isoformat()
            try:
                await cosmos_client.container_client.upsert_item(body=project)
                logger.info(f"Updated environment variables for project {project_id}")
            except Exception as exc:  # pragma: no cover - network required
                logger.error(f"Failed to update project {project_id} in Cosmos DB: {exc}")
                return False

        return updated
    finally:
        if hasattr(storage_client, "close"):
            await storage_client.close()
        if hasattr(web_client, "close"):
            await web_client.close()
        if hasattr(cred, "close"):
            await cred.close()

        # Restore original logging level
        logger.setLevel(original_level)
