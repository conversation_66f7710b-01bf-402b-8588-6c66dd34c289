import os
from azure.identity import DefaultAzureCredential, AzureCliCredential
from azure.identity.aio import DefaultAzureCredential as AsyncDefaultAzureCredential
from azure.identity.aio import AzureCliCredential as AsyncAzureCliCredential

def get_management_credential():
    """Return AzureCliCredential if USE_AZURE_CLI_CREDENTIAL=true else DefaultAzureCredential."""
    if os.environ.get("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true":
        return AzureCliCredential()
    return DefaultAzureCredential()

def get_management_credential_async():
    """Async counterpart for management credential selection."""
    if os.environ.get("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true":
        return AsyncAzureCliCredential()
    return AsyncDefaultAzureCredential()
