"""Cost analytics API routes."""
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from typing import List, Optional
import logging
from pydantic import BaseModel
from backend.cost_management.cost_management_service import CostManagementService
from backend.cost_management.cost_allocation_service import CostAllocationService
from backend.auth.mock_auth import get_current_user
from backend.models.rbac import UserRole
from backend.rbac.rbac_routes import get_rbac_client, get_authenticated_user
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

router = APIRouter(prefix="/api/cost", tags=["cost"])
logger = logging.getLogger(__name__)


class ProjectCost(BaseModel):
    project: str
    projectId: str
    cost: float
    budget: float | None = None
    region: str | None = None
    regionId: str | None = None


class ResourceCost(BaseModel):
    name: str
    resourceId: str
    resourceType: str
    cost: float
    isShared: bool | None = None
    region: str | None = None
    regionId: str | None = None


class ContainerCost(BaseModel):
    name: str
    storageAccount: str
    cost: float
    projectId: str | None = None
    project: str | None = None
    region: str | None = None
    regionId: str | None = None


class IndexerCost(BaseModel):
    name: str
    searchService: str
    cost: float
    projectId: str | None = None
    project: str | None = None
    region: str | None = None
    regionId: str | None = None


class ResourceCostsResponse(BaseModel):
    resources: List[ResourceCost]
    totalCost: float


class ContainerCostsResponse(BaseModel):
    containers: List[ContainerCost]
    totalCost: float


class IndexerCostsResponse(BaseModel):
    indexers: List[IndexerCost]
    totalCost: float


class ServiceCost(BaseModel):
    service: str
    cost: float
    isShared: bool | None = None


class RegionCost(BaseModel):
    region: str
    regionId: str
    cost: float


class CostOverviewResponse(BaseModel):
    projectCosts: List[ProjectCost]
    serviceCosts: List[ServiceCost]
    regionCosts: List[RegionCost]
    totalCost: float


cost_service = CostManagementService()
allocator = CostAllocationService()


@router.get("/overview", response_model=CostOverviewResponse)
async def get_cost_overview(
    request: Request,
    time_range: str = Query("month"),
    region_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    current_user=Depends(get_authenticated_user),
    rbac_client: CosmosRbacClient = Depends(get_rbac_client),
):
    """Return basic cost overview."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")


    accessible_map = None
    if current_user.role != UserRole.SUPER_ADMIN:
        projects = await rbac_client.get_accessible_projects(current_user["id"])
        accessible_map = {p["id"]: p.get("region") for p in projects}

        if project_id and project_id not in accessible_map:
            raise HTTPException(status_code=403, detail="Project not accessible")

        if current_user.role == UserRole.REGIONAL_ADMIN:
            user_region = current_user.get("region")
            if region_id and region_id != user_region:
                raise HTTPException(status_code=403, detail="Region not accessible")
            region_id = user_region

    result = await cost_service.query_cost_by_tag(time_range, "projectId", project_id)

    project_costs: List[ProjectCost] = []
    total = 0.0
    try:
        rows = getattr(result, "rows", [])
        project_ids = []
        project_cost_data = []

        # First pass: collect project IDs and cost data
        for row in rows:
            pid = str(row[0])
            if accessible_map is not None and pid not in accessible_map:
                continue
            project_cost = float(row[-1])
            region = accessible_map.get(pid) if accessible_map is not None else None

            project_ids.append(pid)
            project_cost_data.append({
                'id': pid,
                'cost': project_cost,
                'region': region
            })
            total += project_cost

        # Fetch project names from Cosmos DB
        project_names_map = await cost_service.get_project_names_map(rbac_client, project_ids)

        # Second pass: create ProjectCost objects with names
        for data in project_cost_data:
            pid = data['id']
            project_name = project_names_map.get(pid, pid)  # Fallback to ID if name not found

            project_costs.append(
                ProjectCost(
                    project=project_name,
                    projectId=pid,
                    cost=data['cost'],
                    budget=data['cost'] * 1.5,  # Arbitrary budget as 1.5x the current cost for now
                    region=data['region'],
                    regionId=data['region'],
                )
            )
    except Exception:
        logger.exception("Failed parsing cost overview response")

    return CostOverviewResponse(projectCosts=project_costs, serviceCosts=[], regionCosts=[], totalCost=total)


@router.get("/resources", response_model=ResourceCostsResponse)
async def get_resource_costs(
    request: Request,
    time_range: str = Query("month"),
    region_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    resource_type: Optional[str] = Query(None),
    current_user=Depends(get_authenticated_user),
    rbac_client: CosmosRbacClient = Depends(get_rbac_client),
):
    """Get cost data for Azure resources."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")

    if current_user.role != UserRole.SUPER_ADMIN:
        projects = await rbac_client.get_accessible_projects(current_user["id"])
        accessible_ids = {p["id"] for p in projects}
        if project_id and project_id not in accessible_ids:
            raise HTTPException(status_code=403, detail="Project not accessible")
        if current_user.role == UserRole.REGIONAL_ADMIN:
            user_region = current_user.get("region")
            if region_id and region_id != user_region:
                raise HTTPException(status_code=403, detail="Region not accessible")
            region_id = user_region

    result = await cost_service.query_cost_by_resource(time_range, region_id, resource_type)
    resources: List[ResourceCost] = []
    total = 0.0
    try:
        rows = getattr(result, "rows", [])
        for row in rows:
            cost = float(row[-1])
            resources.append(
                ResourceCost(
                    name=row[0],
                    resourceId=row[0],
                    resourceType=resource_type or "",
                    cost=cost,
                    region=region_id,
                    regionId=region_id,
                )
            )
            total += cost
    except Exception:
        logger.exception("Failed parsing resource cost response")

    return ResourceCostsResponse(resources=resources, totalCost=total)


@router.get("/containers", response_model=ContainerCostsResponse)
async def get_container_costs(
    time_range: str = Query("month"),
    storage_account: str = Query(..., description="Storage account name"),
    current_user=Depends(get_authenticated_user),
):
    """Get cost data allocated to blob containers."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")

    resource_result = await cost_service.query_cost_by_resource(time_range, None, "Microsoft.Storage/storageAccounts")
    total_account_cost = 0.0
    if getattr(resource_result, "rows", None):
        for row in resource_result.rows:
            if storage_account.lower() in row[0].lower():
                total_account_cost = float(row[-1])
                break

    metrics = await allocator.get_container_metrics(storage_account)
    allocated = await allocator.allocate_storage_costs(metrics, total_account_cost)

    containers: List[ContainerCost] = []
    for name, cost in allocated.items():
        containers.append(ContainerCost(name=name, storageAccount=storage_account, cost=cost))

    return ContainerCostsResponse(containers=containers, totalCost=sum(allocated.values()))


@router.get("/indexers", response_model=IndexerCostsResponse)
async def get_indexer_costs(
    time_range: str = Query("month"),
    search_service: str = Query(..., description="Search service name"),
    current_user=Depends(get_authenticated_user),
):
    """Get cost data allocated to search indexers."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")

    resource_result = await cost_service.query_cost_by_resource(time_range, None, "Microsoft.Search/searchServices")
    total_service_cost = 0.0
    if getattr(resource_result, "rows", None):
        for row in resource_result.rows:
            if search_service.lower() in row[0].lower():
                total_service_cost = float(row[-1])
                break

    metrics = await allocator.get_indexer_metrics(search_service)
    allocated = await allocator.allocate_search_costs(metrics, total_service_cost)

    indexers: List[IndexerCost] = []
    for name, cost in allocated.items():
        indexers.append(IndexerCost(name=name, searchService=search_service, cost=cost))

    return IndexerCostsResponse(indexers=indexers, totalCost=sum(allocated.values()))

