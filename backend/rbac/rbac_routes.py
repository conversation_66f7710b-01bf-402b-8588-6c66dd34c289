# backend/api/rbac_routes.py
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, FastAPI, Request
from typing import List, Optional, Dict, Any
from backend.models.rbac import UserRole, TeamUserRole, ProjectUserRole
from backend.auth.mock_auth import get_mock_user
from backend.auth.entra_auth import get_entra_user, get_entra_user_with_delegated_token, is_entra_auth_configured, get_authenticated_user
from backend.auth.token_utils import validate_token
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.settings import app_settings
import logging
import os
import asyncio
from backend.utils.logging_config import configure_rbac_logger

# Configure a dedicated logger for RBAC operations
rbac_logger = configure_rbac_logger()

router = APIRouter(prefix="/api/rbac", tags=["RBAC"])
_rbac_client_instance: Optional[CosmosRbacClient] = None

# Environment variable to enable Entra ID authentication for local development
USE_ENTRA_AUTH = os.environ.get("USE_ENTRA_AUTH", "false").lower() == "true"
RBAC_LOG_LEVEL = os.environ.get("RBAC_LOG_LEVEL", "INFO").upper()
logging.getLogger().setLevel(RBAC_LOG_LEVEL)
logging.basicConfig(level=RBAC_LOG_LEVEL)
rbac_logger.info(f"RBAC log level set to {RBAC_LOG_LEVEL}")

async def _initialize_rbac_client_instance():
    """Initialize the global RBAC client instance."""
    global _rbac_client_instance
    try:
        # Check CosmosDB settings
        if not app_settings.chat_history:
            rbac_logger.error("CosmosDB settings not configured. RBAC requires CosmosDB.")
            return None

        # Initialize CosmosDB client
        cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
        client = CosmosRbacClient(
            cosmos_endpoint,
            app_settings.chat_history.account_key,
            app_settings.chat_history.database
        )

        # Initialize the client
        init_result = await client.initialize()
        if init_result:
            rbac_logger.info("Global RBAC client initialized successfully.")
            _rbac_client_instance = client
            return client
        else:
            rbac_logger.error("Failed to initialize global RBAC client")
            return None
    except Exception as e:
        rbac_logger.error(f"Error initializing global RBAC client: {e}", exc_info=True)
        return None

@router.get("/verify-token", response_model=Dict[str, Any])
async def verify_token(request: Request, current_user: dict = Depends(get_authenticated_user)):
    """
    Verify the authentication token and ensure RBAC client is properly initialized.
    This endpoint should be called after successful authentication to ensure everything is set up correctly.
    """
    try:
        # Verify we have a valid token
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing or invalid Authorization header"
            )

        # Extract token
        token = auth_header.split(' ')[1]

        # Initialize RBAC client if not already initialized
        if _rbac_client_instance is None:
            await _initialize_rbac_client_instance()

        # Verify RBAC client is available
        if _rbac_client_instance is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="RBAC service not available"
            )

        return {
            "status": "success",
            "message": "Token verified successfully",
            "token_valid": True,
            "rbac_initialized": True
        }
    except HTTPException:
        raise
    except Exception as e:
        rbac_logger.error(f"Error verifying token: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to verify token: {str(e)}"
        )

# Renamed to avoid conflict with the new function that takes a Request
async def initialize_rbac_client_with_token(token: str) -> CosmosRbacClient:
    """Initialize RBAC client with authentication token."""
    try:
        # Validate token
        if not token:
            rbac_logger.error("No authentication token provided")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication token required"
            )

        # Verify token
        is_valid, _ = await validate_token(token)
        if not is_valid:
            rbac_logger.error("Invalid authentication token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )

        # Check CosmosDB settings
        if not app_settings.chat_history:
            rbac_logger.error("CosmosDB settings not configured. RBAC requires CosmosDB.")
            raise RuntimeError("CosmosDB settings for RBAC are not configured.")

        # Initialize CosmosDB client
        cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"

        client = CosmosRbacClient(
            cosmos_endpoint,
            app_settings.chat_history.account_key,
            app_settings.chat_history.database
        )

        # Retry initialization with exponential backoff
        max_retries = 3
        retry_delay = 1  # Start with 1 second delay
        for attempt in range(max_retries):
            try:
                init_result = await client.initialize()
                if init_result:
                    rbac_logger.info("RBAC client initialized successfully.")
                    return client
                else:
                    rbac_logger.warning(f"Attempt {attempt + 1}/{max_retries} failed to initialize RBAC client")
            except Exception as e:
                if attempt == max_retries - 1:  # Last attempt
                    rbac_logger.error(f"Final attempt failed to initialize RBAC client: {e}", exc_info=True)
                    raise
                else:
                    rbac_logger.warning(f"Attempt {attempt + 1}/{max_retries} failed: {e}. Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff

        raise Exception("Failed to initialize RBAC client after multiple attempts")

    except Exception as e:
        rbac_logger.error(f"Error initializing RBAC client: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize RBAC client: {str(e)}"
        )

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for the RBAC FastAPI app."""
    rbac_logger.info("RBAC FastAPI app lifespan starting...")
    yield
    rbac_logger.info("RBAC FastAPI app lifespan shutting down...")

async def initialize_rbac_client(request: Request) -> CosmosRbacClient:
    """Initialize RBAC client with token from request."""
    try:
        # Extract token from request using the Easy Auth-aware function
        from backend.auth.entra_auth import extract_token_from_request_with_easy_auth
        token = extract_token_from_request_with_easy_auth(request)

        if not token:
            # In development mode, create a client without token validation
            if os.environ.get("DEVELOPMENT_MODE", "false").lower() == "true":
                rbac_logger.warning("No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.")
                # Check CosmosDB settings
                if not app_settings.chat_history:
                    rbac_logger.error("CosmosDB settings not configured. RBAC requires CosmosDB.")
                    raise RuntimeError("CosmosDB settings for RBAC are not configured.")

                # Initialize CosmosDB client
                cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
                client = CosmosRbacClient(
                    cosmos_endpoint,
                    app_settings.chat_history.account_key,
                    app_settings.chat_history.database
                )

                init_result = await client.initialize()
                if init_result:
                    rbac_logger.info("RBAC client initialized successfully in development mode.")
                    return client
                else:
                    raise Exception("Failed to initialize RBAC client in development mode")
            else:
                rbac_logger.error("No token found in request")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication token required"
                )

        # Call the token-based initialize_rbac_client
        return await initialize_rbac_client_with_token(token)
    except Exception as e:
        rbac_logger.error(f"Error initializing RBAC client from request: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initialize RBAC client: {str(e)}"
        )


async def get_rbac_client(request: Request = None) -> CosmosRbacClient:
    """Dependency to get the initialized RBAC client with request context."""
    try:
        if request:
            return await initialize_rbac_client(request)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Request context is required to initialize RBAC client"
            )
    except Exception as e:
        rbac_logger.error(f"Error getting RBAC client: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get RBAC client: {str(e)}"
        )

# This is the new local helper that uses the injected client
async def get_project_by_id_local_helper(project_id: str, client: CosmosRbacClient = Depends(get_rbac_client)) -> Optional[Dict[str, Any]]:
    """Helper function to get a project by ID using an injected RBAC client."""
    if not client: # This check is somewhat redundant due to Depends, but good for clarity
        rbac_logger.error("RBAC client not available in get_project_by_id_local_helper")
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="RBAC service is not available.")

    # Directly use the client's method to fetch the project.
    # This client.get_project method should handle querying the projects_container.
    project = await client.get_project(project_id=project_id)
    return project

# Authentication dependency that uses Entra ID when configured
async def get_authenticated_user(request: Request = None, rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """Get the authenticated user.

    If USE_ENTRA_AUTH is True and Entra ID is configured, use Entra ID authentication.
    Otherwise, fall back to mock authentication or header-based authentication.

    This function now supports delegated authentication flow, where the frontend
    passes a token to the backend, which then uses it to access Microsoft Graph API.
    """
    user_id = "1"

    # First check if we should use Entra ID authentication
    if USE_ENTRA_AUTH and is_entra_auth_configured():
        rbac_logger.info("Using Entra ID authentication")

        # Try to use delegated token first
        if request:
            rbac_logger.info("Attempting to use delegated token from request")
            try:
                user = await get_entra_user_with_delegated_token(request)
                # Don't return the user yet - we need to enrich it with database info first
                rbac_logger.info(f"Delegated token authentication returned user: {user}")
                # We'll continue to the database enrichment step below
            except HTTPException as exc:
                rbac_logger.error(f"Delegated token authentication failed: {exc.detail}")
                raise
        else:
            # Only fall back to client credentials flow if no request is provided
            rbac_logger.info("No request object provided, using client credentials flow")
            user = await get_entra_user()
            rbac_logger.info(f"Client credentials authentication returned user: {user}")
    else:
        # Fall back to header-based or mock authentication
        if request:
            current_user_id = request.headers.get("X-Current-User-ID")
            if current_user_id:
                rbac_logger.info(f"Found current user ID in header: {current_user_id}")
                user_id = current_user_id

        rbac_logger.info(f"Using mock authentication with user_id: {user_id}")
        user = await get_mock_user(user_id)
        rbac_logger.info(f"get_mock_user returned: {user}")

    # Enrich user with database information if available
    if user and rbac_client:
        try:
            db_user = await rbac_client.get_user(user["id"])
            if db_user:
                rbac_logger.info(f"Found user in database: {db_user}")
                # CRITICAL: Always use the role from the database if available
                # This is the key fix for the issue where the user's role is not being properly recognized
                user["role"] = db_user.get("role", user.get("role", "REGULAR_USER"))
                rbac_logger.info(f"Set user role from database to: {user['role']}")

                # Copy other fields from the database
                for key, value in db_user.items():
                    if key not in user:
                        user[key] = value
                rbac_logger.info(f"Updated user with database info: {user}")
            else:
                # If user not found in database, ensure they have a default role
                if "role" not in user:
                    user["role"] = "REGULAR_USER"
                    rbac_logger.info(f"User not found in database, defaulting to REGULAR_USER role: {user}")
        except Exception as e:
            rbac_logger.error(f"Error enriching user with database info: {e}")
            # Ensure user has a role even if there was an error
            if "role" not in user:
                user["role"] = "REGULAR_USER"
                rbac_logger.info(f"Error getting user from database, defaulting to REGULAR_USER role: {user}")
    else:
        rbac_logger.warning("RBAC client not initialized, cannot check database")

    # Final check to ensure role is set
    if "role" not in user or not user["role"]:
        user["role"] = "REGULAR_USER"
        rbac_logger.info(f"Final check: Setting default REGULAR_USER role for user: {user}")

    rbac_logger.info(f"Returning authenticated user with role {user.get('role')}: {user}")
    return user

# For backward compatibility
async def get_test_user(request: Request = None, rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """Get a test user for development."""
    return await get_authenticated_user(request, rbac_client)

# User Management Endpoints
@router.get("/users", response_model=List[Dict[str, Any]])
async def get_users(request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """Get users based on current user's permissions"""
    users = await rbac_client.get_accessible_users(current_user["id"])
    return users

@router.post("/users", response_model=Dict[str, Any])
async def create_user(user_data: Dict[str, Any], request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] not in [UserRole.SUPER_ADMIN.value, UserRole.REGIONAL_ADMIN.value]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to create users")
    if current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if user_data.get("role") == UserRole.SUPER_ADMIN.value:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins cannot create super admins")
        user_region = user_data.get("region")
        if user_region != current_user.get("region"):
            # Log the attempt to create a user in a different region
            rbac_logger.warning(
                f"Regional admin {current_user['id']} attempted to create a user in region {user_region} "
                f"but was restricted to their region {current_user.get('region')}"
            )
            # Override the region with the admin's region
            user_data["region"] = current_user.get("region")
    created_user = await rbac_client.create_user(user_data)
    return created_user

@router.get("/users/{user_id}", response_model=Dict[str, Any])
async def get_user(user_id: str, request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """Get a user by ID"""
    # Check if user has permission to view this user
    accessible_users = await rbac_client.get_accessible_users(current_user["id"])
    accessible_user_ids = [user["id"] for user in accessible_users]
    if user_id not in accessible_user_ids and user_id != current_user["id"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this user")
    user = await rbac_client.get_user(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return user

@router.put("/users/{user_id}", response_model=Dict[str, Any])
async def update_user(user_id: str, user_data: Dict[str, Any], request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["id"] != user_id and current_user["role"] not in [UserRole.SUPER_ADMIN.value, UserRole.REGIONAL_ADMIN.value]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to update this user")
    if current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        target_user = await rbac_client.get_user(user_id)
        if not target_user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        if target_user.get("role") == UserRole.SUPER_ADMIN.value:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins cannot update super admins")
        if target_user.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins can only update users in their region")
        if user_data.get("role") == UserRole.SUPER_ADMIN.value:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins cannot change users to super admins")
        if "region" in user_data and user_data["region"] != current_user.get("region"):
            # Log the attempt to update a user to a different region
            rbac_logger.warning(
                f"Regional admin {current_user['id']} attempted to update user {user_id} to region {user_data['region']} "
                f"but was restricted to their region {current_user.get('region')}"
            )
            # Override the region with the admin's region
            user_data["region"] = current_user.get("region")
    updated_user = await rbac_client.update_user(user_id, user_data)
    if not updated_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return updated_user

@router.put("/users/{user_id}/role", response_model=Dict[str, Any])
async def change_user_role(user_id: str, role_data: Dict[str, Any], request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """Change a user's role"""
    # Log the request details for debugging
    rbac_logger.info(f"Role change request - User ID: {user_id}, Current user: {current_user['id']}, Role data: {role_data}")

    # Get the target user
    target_user = await rbac_client.get_user(user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    if "role" not in role_data:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Role is required")
    new_role = role_data["role"]
    current_role = target_user.get("role")
    if user_id == current_user["id"]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You cannot change your own role")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if current_role == UserRole.SUPER_ADMIN.value:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins cannot update super admins")
        if target_user.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins can only update users in their region")
        if (current_role == UserRole.REGULAR_USER.value and new_role == UserRole.REGIONAL_ADMIN.value) or \
           (current_role == UserRole.REGIONAL_ADMIN.value and new_role == UserRole.REGULAR_USER.value):
            pass
        else:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins can only promote regular users to regional admin or demote regional admins to regular users")
    else:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regular users cannot change roles")
    update_data = {"role": new_role}
    updated_user = await rbac_client.update_user(user_id, update_data)
    if not updated_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return updated_user

@router.delete("/users/{user_id}", response_model=Dict[str, Any])
async def delete_user(user_id: str, request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """Delete a user by ID"""
    # Add detailed logging
    rbac_logger.info(f"DELETE /users/{user_id} - Current user: {current_user}")
    rbac_logger.info(f"User ID to delete: {user_id}, Current user ID: {current_user['id']}")
    rbac_logger.info(f"Are they equal? {user_id == current_user['id']}")

    # Prevent users from deleting themselves
    if user_id == current_user["id"]:
        rbac_logger.warning(f"User {current_user['id']} attempted to delete their own account")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You cannot delete your own account"
        )

    # Check if user has permission to delete this user
    if current_user["role"] not in [UserRole.SUPER_ADMIN.value, UserRole.REGIONAL_ADMIN.value]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete users")
    if current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        target_user = await rbac_client.get_user(user_id)
        if not target_user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        if target_user.get("role") == UserRole.SUPER_ADMIN.value:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins cannot delete super admins")
        if target_user.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins can only delete users in their region")
    success = await rbac_client.delete_user(user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return {"message": "User deleted successfully"}

# Region Management Endpoints
@router.get("/regions", response_model=List[Dict[str, Any]])
async def get_regions(request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        regions = await rbac_client.get_regions()
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        region_id = current_user.get("region")
        if not region_id: return []
        region = await rbac_client.get_region(region_id)
        regions = [region] if region else []
    else:
        region_id = current_user.get("region")
        if not region_id: return []
        region = await rbac_client.get_region(region_id)
        regions = [region] if region else []
    return regions

@router.post("/regions", response_model=Dict[str, Any])
async def create_region(region_data: Dict[str, Any], request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] != UserRole.SUPER_ADMIN.value:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only super admins can create regions")
    region_data["created_by"] = current_user["id"]
    created_region = await rbac_client.create_region(region_data)
    return created_region

@router.get("/regions/{region_id}", response_model=Dict[str, Any])
async def get_region(region_id: str, request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    region = await rbac_client.get_region(region_id)
    if not region:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Region not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] in [UserRole.REGIONAL_ADMIN.value, UserRole.REGULAR_USER.value]:
        if region_id != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view other regions")
    return region

@router.put("/regions/{region_id}", response_model=Dict[str, Any])
async def update_region(region_id: str, region_data: Dict[str, Any], request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] != UserRole.SUPER_ADMIN.value:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only super admins can update regions")
    region = await rbac_client.get_region(region_id)
    if not region:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Region not found")
    updated_region = await rbac_client.update_region(region_id, region_data)
    if not updated_region:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Region not found")
    return updated_region

@router.delete("/regions/{region_id}", response_model=Dict[str, Any])
async def delete_region(region_id: str, request: Request, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] != UserRole.SUPER_ADMIN.value:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only super admins can delete regions")
    region = await rbac_client.get_region(region_id)
    if not region:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Region not found")
    success = await rbac_client.delete_region(region_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Region not found")
    return {"message": "Region deleted successfully"}

# Team Management Endpoints
@router.get("/teams", response_model=List[Dict[str, Any]])
async def get_teams(current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    teams = await rbac_client.get_accessible_teams(current_user["id"])
    return teams

@router.post("/teams", response_model=Dict[str, Any])
async def create_team(team_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] not in [UserRole.SUPER_ADMIN.value, UserRole.REGIONAL_ADMIN.value]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to create teams")
    if current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team_data.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins can only create teams in their region")
    team_data["created_by"] = current_user["id"]
    created_team = await rbac_client.create_team(team_data)
    await rbac_client.add_user_to_team(created_team["id"], current_user["id"], TeamUserRole.LEADER.value, current_user["id"])
    return created_team

@router.get("/teams/{team_id}", response_model=Dict[str, Any])
async def get_team(team_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    team = await rbac_client.get_team(team_id)
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view teams in other regions")
    else:
        team_members = await rbac_client.get_team_members(team_id)
        is_member = any(member.get("userId") == current_user["id"] for member in team_members)
        if not is_member:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this team")
    return team

@router.put("/teams/{team_id}", response_model=Dict[str, Any])
async def update_team(team_id: str, team_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    team = await rbac_client.get_team(team_id)
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to update teams in other regions")
    else:
        team_members = await rbac_client.get_team_members(team_id)
        is_leader = any(member.get("userId") == current_user["id"] and member.get("role") == TeamUserRole.LEADER.value for member in team_members)
        if not is_leader:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only team leaders can update teams")
    updated_team = await rbac_client.update_team(team_id, team.get("region"), team_data)
    if not updated_team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    return updated_team

@router.delete("/teams/{team_id}", response_model=Dict[str, Any])
async def delete_team(team_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    team = await rbac_client.get_team(team_id)
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete teams in other regions")
    else:
        team_members = await rbac_client.get_team_members(team_id)
        is_leader = any(member.get("userId") == current_user["id"] and member.get("role") == TeamUserRole.LEADER.value for member in team_members)
        if not is_leader:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only team leaders can delete teams")
    success = await rbac_client.delete_team(team_id, team.get("region"))
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    return {"message": "Team deleted successfully"}

# Project Management Endpoints
@router.get("/projects", response_model=List[Dict[str, Any]])
async def get_projects(current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    projects = await rbac_client.get_accessible_projects(current_user["id"])
    return projects

@router.get("/projects/{project_id}/deployment-status", response_model=Dict[str, Any])
async def get_project_deployment_status(project_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_member = any(user.get("userId") == current_user["id"] for user in project_users)
        if not is_member:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this project")
    deployment_status = project.get("deployment_status", {"status": "pending", "message": "Waiting for deployment to start...", "updated_at": datetime.now(timezone.utc).isoformat(), "details": {"storage": {"storage_account": False, "containers": {"uploads": False, "input": False, "output": False}}, "storage_complete": False, "search": {"search_service": False, "index": False, "indexer": False, "datasource": False}, "search_complete": False, "function": {"function_app": False, "event_grid_topic": False, "event_grid_system_topic": False, "event_grid": False, "maturity_assessment": False, "executive_summary": False}, "function_complete": False, "overall_complete": False, "completion_percentage": 0}})
    return deployment_status

@router.post("/projects/{project_id}/deployment-status", response_model=Dict[str, Any])
async def update_project_deployment_status(project_id: str, status_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to update projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
        if not is_owner:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can update project status")
    status_val = status_data.get("status")
    if not status_val:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Status is required")
    current_deployment_status = project.get("deployment_status", {})
    deployment_status = {"status": status_val, "updated_at": datetime.now(timezone.utc).isoformat()}
    if "message" in status_data: deployment_status["message"] = status_data["message"]
    elif "message" in current_deployment_status: deployment_status["message"] = current_deployment_status["message"]
    if "details" in status_data: deployment_status["details"] = status_data["details"]
    elif "details" in current_deployment_status: deployment_status["details"] = current_deployment_status["details"]
    else: deployment_status["details"] = {"storage": {"storage_account": False, "containers": {"uploads": False, "input": False, "output": False}}, "storage_complete": False, "search": {"search_service": False, "index": False, "indexer": False, "datasource": False}, "search_complete": False, "function": {"function_app": False, "event_grid_topic": False, "event_grid_system_topic": False, "event_grid": False, "maturity_assessment": False, "executive_summary": False}, "function_complete": False, "overall_complete": False, "completion_percentage": 0}
    if "error" in status_data: deployment_status["error"] = status_data["error"]
    region = project.get("region")
    if not region:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Project region not found")
    project["deployment_status"] = deployment_status
    updated_project = await rbac_client.update_project(project_id, region, project)
    try:
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        parameters = [{"name": "@projectId", "value": project_id}]
        conversation_projects = [item async for item in rbac_client.conversations_project_container.query_items(query=query, parameters=parameters)]
        if conversation_projects:
            conversation_project = conversation_projects[0]
            conversation_project["deployment_status"] = deployment_status
            await rbac_client.conversations_project_container.upsert_item(conversation_project)
    except Exception as e:
        rbac_logger.error(f"Error updating project in conversations_project container: {e}")
    return deployment_status

@router.post("/projects", response_model=Dict[str, Any])
async def create_project(project_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    if current_user["role"] not in [UserRole.SUPER_ADMIN.value, UserRole.REGIONAL_ADMIN.value]:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to create projects")
    if current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project_data.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Regional admins can only create projects in their region")
    project_data["owner"] = current_user["id"]
    created_project = await rbac_client.create_project(project_data)
    await rbac_client.assign_user_to_project(created_project["id"], current_user["id"], ProjectUserRole.OWNER.value, current_user["id"])
    return created_project

@router.get("/projects/{project_id}", response_model=Dict[str, Any])
async def get_project(project_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    rbac_logger.info(f"GET /api/rbac/projects/{project_id} called by user {current_user.get('id')}")
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")

    # Check if user is SuperAdmin - SuperAdmins can access any project
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        rbac_logger.info(f"User {current_user.get('id')} is a SuperAdmin, granting access to project {project_id}")
        return project
    # Check if user is RegionalAdmin for the project's region
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            rbac_logger.warning(f"RegionalAdmin {current_user.get('id')} attempted to access project {project_id} in region {project.get('region')} but is assigned to region {current_user.get('region')}")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view projects in other regions")
        return project
    # For regular users, check if they're assigned to the project
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_member = any(user.get("userId") == current_user["id"] for user in project_users)
        if not is_member:
            rbac_logger.warning(f"User {current_user.get('id')} attempted to access project {project_id} but is not a member")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this project")
        return project

@router.put("/projects/{project_id}", response_model=Dict[str, Any])
async def update_project(project_id: str, project_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to update projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
        if not is_owner:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can update projects")
    region = project.get("region")
    if not region:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Project region not found")
    updated_project = await rbac_client.update_project(project_id, region, project_data)
    if not updated_project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    return updated_project

@router.delete("/projects/{project_id}", response_model=Dict[str, Any])
async def delete_project(project_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to delete projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
        if not is_owner:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can delete projects")
    region = project.get("region")
    if not region:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Project region not found")
    success = await rbac_client.delete_project(project_id, region)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    return {"message": "Project deleted successfully"}

# Team Membership Endpoints
@router.get("/teams/{team_id}/members", response_model=List[Dict[str, Any]])
async def get_team_members(team_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    team = await rbac_client.get_team(team_id)
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view teams in other regions")
    else:
        team_members = await rbac_client.get_team_members(team_id)
        is_member = any(member.get("userId") == current_user["id"] for member in team_members)
        if not is_member:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only team members can view the team")
    team_members = await rbac_client.get_team_members(team_id)
    return team_members

@router.post("/teams/{team_id}/members", response_model=Dict[str, Any])
async def add_team_member(team_id: str, member_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    team = await rbac_client.get_team(team_id)
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to add members to teams in other regions")
    else:
        team_members = await rbac_client.get_team_members(team_id)
        is_leader = any(member.get("userId") == current_user["id"] and member.get("role") == TeamUserRole.LEADER.value for member in team_members)
        if not is_leader:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only team leaders can add members")
    user_id = member_data.get("userId") or member_data.get("user_id")
    role = member_data.get("role", TeamUserRole.MEMBER.value)
    if not user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="userId or user_id is required")
    user = await rbac_client.get_user(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    assignment = await rbac_client.add_user_to_team(team_id, user_id, role, current_user["id"])
    return assignment

@router.delete("/teams/{team_id}/members/{user_id}", response_model=Dict[str, Any])
async def remove_team_member(team_id: str, user_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    team = await rbac_client.get_team(team_id)
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if team.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to remove members from teams in other regions")
    else:
        if current_user["id"] != user_id:
            team_members = await rbac_client.get_team_members(team_id)
            is_leader = any(member.get("userId") == current_user["id"] and member.get("role") == TeamUserRole.LEADER.value for member in team_members)
            if not is_leader:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only team leaders can remove members")
    user = await rbac_client.get_user(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    success = await rbac_client.remove_user_from_team(team_id, user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found in team")
    return {"message": "User removed from team successfully"}

# Project User Endpoints
@router.get("/projects/{project_id}/users", response_model=List[Dict[str, Any]])
async def get_project_users(project_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view projects in other regions")
    else:
        project_users_list = await rbac_client.get_project_users(project_id)
        is_member = any(user.get("id") == current_user["id"] for user in project_users_list) # Corrected key to 'id'
        if not is_member:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project members can view the project")
    project_users_list = await rbac_client.get_project_users(project_id)
    return project_users_list

@router.post("/projects/{project_id}/users", response_model=Dict[str, Any])
async def assign_user_to_project(project_id: str, user_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to assign users to projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
        if not is_owner:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can assign users")
    user_id = user_data.get("userId") or user_data.get("user_id")
    role = user_data.get("role", "MEMBER")
    if not user_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="userId or user_id is required")
    user = await rbac_client.get_user(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    assignment = await rbac_client.assign_user_to_project(project_id, user_id, role, current_user["id"])
    return assignment

@router.delete("/projects/{project_id}/users/{user_id}", response_model=Dict[str, Any])
async def remove_user_from_project(project_id: str, user_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to remove users from projects in other regions")
    else:
        if current_user["id"] != user_id:
            project_users = await rbac_client.get_project_users(project_id)
            is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
            if not is_owner:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can remove users")
    user = await rbac_client.get_user(user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    success = await rbac_client.remove_user_from_project(project_id, user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found in project")
    return {"message": "User removed from project successfully"}

# Project Team Assignment Endpoints
@router.get("/projects/{project_id}/teams", response_model=List[Dict[str, Any]])
async def get_project_teams(project_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_member = any(user.get("id") == current_user["id"] for user in project_users) # Corrected key to 'id'
        if not is_member:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project members can view the project")
    project_teams = await rbac_client.get_project_teams(project_id)
    return project_teams

@router.post("/projects/{project_id}/teams", response_model=Dict[str, Any])
async def assign_team_to_project(project_id: str, team_data: Dict[str, Any], current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to assign teams to projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
        if not is_owner:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can assign teams")
    team_id = team_data.get("teamId") or team_data.get("team_id")
    if not team_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="teamId or team_id is required")
    team = await rbac_client.get_team(team_id) # Use rbac_client to get team
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    if team.get("region") != project.get("region"):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Team and project must be in the same region")
    assignment = await rbac_client.assign_team_to_project(project_id, team_id, current_user["id"])
    team_members = await rbac_client.get_team_members(team_id)
    for member in team_members:
        try:
            user_id_to_assign = member.get("userId")
            if user_id_to_assign:
                await rbac_client.assign_user_to_project(project_id, user_id_to_assign, "MEMBER", current_user["id"])
        except Exception as e:
            rbac_logger.error(f"Error adding team member {member.get('userId')} to project: {str(e)}")
    return assignment

@router.delete("/projects/{project_id}/teams/{team_id}", response_model=Dict[str, Any])
async def remove_team_from_project(project_id: str, team_id: str, current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    project = await get_project_by_id_local_helper(project_id, rbac_client)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        pass
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") != current_user.get("region"):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to remove teams from projects in other regions")
    else:
        project_users = await rbac_client.get_project_users(project_id)
        is_owner = any(user.get("userId") == current_user["id"] and user.get("role") == ProjectUserRole.OWNER.value for user in project_users)
        if not is_owner:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Only project owners can remove teams")
    team = await rbac_client.get_team(team_id) # Use rbac_client to get team
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")
    success = await rbac_client.remove_team_from_project(project_id, team_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found in project")
    return {"message": "Team removed from project successfully"}

# This is the old helper function, now renamed. It uses the global _rbac_client_instance.
# It's kept for reference or if any internal logic still relies on the old pattern,
# but new route developments should use the injected client via get_project_by_id_local_helper or direct injection.
async def get_project_by_id_with_global_client(project_id: str, client: CosmosRbacClient = Depends(get_rbac_client)): # Added client param for consistency, though it uses the global one if not careful
    """Helper function to get a project by ID using the global RBAC client instance. (For review/phasing out)"""
    # This function now explicitly takes a client.
    # The global rbac_client is being phased out from direct use in routes.

    # Use the provided client if available, otherwise fallback to global (with a warning)
    active_client = client if client else _rbac_client_instance

    if not active_client:
        rbac_logger.error("RBAC client not initialized in get_project_by_id_with_global_client")
        # If client was passed and is None, or global is None
        if client is None and _rbac_client_instance is None:
             rbac_logger.warning("Attempted to use get_project_by_id_with_global_client when _rbac_client_instance is also None.")
        raise HTTPException(status_code=500, detail="Database client not provided or initialized")

    project_query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
    project_parameters = [{"name": "@projectId", "value": project_id}]
    projects = []
    try:
        rbac_logger.debug(f"Querying projects_container for project_id: {project_id} using {'passed' if client else 'global'} client in get_project_by_id_with_global_client")
        if not hasattr(active_client, 'projects_container') or active_client.projects_container is None:
            rbac_logger.error("active_client.projects_container is not available in get_project_by_id_with_global_client.")
            raise HTTPException(status_code=500, detail="Project data store (projects_container) not configured on active_client.")

        async for item in active_client.projects_container.query_items(
            query=project_query,
            parameters=project_parameters
        ):
            projects.append(item)
        rbac_logger.debug(f"Found {len(projects)} projects for ID {project_id} after query in get_project_by_id_with_global_client.")

    except HTTPException:
        raise
    except Exception as e:
        rbac_logger.error(f"Error querying projects_container for project_id {project_id} in get_project_by_id_with_global_client: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error accessing project data: {str(e)}")

    if not projects:
        rbac_logger.debug(f"Project not found in DB for project_id: {project_id} in get_project_by_id_with_global_client")
        return None

    rbac_logger.debug(f"Returning project data for project_id: {project_id} from get_project_by_id_with_global_client")
    return projects[0]


# Consolidated Data Endpoints for Performance Optimization
@router.get("/teams/consolidated", response_model=List[Dict[str, Any]])
async def get_teams_consolidated(current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """
    Get all teams with their members and assigned projects in a single call.
    This reduces the N+1 query problem in the frontend.
    """
    import asyncio

    # Get accessible teams
    teams = await rbac_client.get_accessible_teams(current_user["id"])

    # Get all projects to map team assignments
    projects = await rbac_client.get_accessible_projects(current_user["id"])

    # Build a map of project_id -> team_ids for efficient lookup
    project_teams_map = {}
    for project in projects:
        try:
            project_teams = await rbac_client.get_project_teams(project["id"])
            project_teams_map[project["id"]] = [pt["teamId"] for pt in project_teams]
        except Exception as e:
            rbac_logger.warning(f"Failed to get teams for project {project['id']}: {e}")
            project_teams_map[project["id"]] = []

    # Enrich teams with members and projects
    enriched_teams = []
    for team in teams:
        try:
            # Get team members
            team_members = await rbac_client.get_team_members(team["id"])
            member_ids = [member["userId"] for member in team_members]

            # Find projects this team is assigned to
            assigned_projects = []
            for project_id, team_ids in project_teams_map.items():
                if team["id"] in team_ids:
                    assigned_projects.append(project_id)

            # Add enriched data to team
            enriched_team = {
                **team,
                "members": member_ids,
                "projects": assigned_projects
            }
            enriched_teams.append(enriched_team)

        except Exception as e:
            rbac_logger.warning(f"Failed to enrich team {team['id']}: {e}")
            # Add team with empty members/projects if enrichment fails
            enriched_teams.append({
                **team,
                "members": [],
                "projects": []
            })

    return enriched_teams


@router.get("/projects/consolidated", response_model=List[Dict[str, Any]])
async def get_projects_consolidated(current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """
    Get all projects with their assigned teams in a single call.
    This reduces the N+1 query problem in the frontend.
    """
    # Get accessible projects
    projects = await rbac_client.get_accessible_projects(current_user["id"])

    # Enrich projects with team assignments
    enriched_projects = []
    for project in projects:
        try:
            # Get teams assigned to this project
            project_teams = await rbac_client.get_project_teams(project["id"])
            team_ids = [pt["teamId"] for pt in project_teams]

            # Add enriched data to project
            enriched_project = {
                **project,
                "teams": team_ids
            }
            enriched_projects.append(enriched_project)

        except Exception as e:
            rbac_logger.warning(f"Failed to enrich project {project['id']}: {e}")
            # Add project with empty teams if enrichment fails
            enriched_projects.append({
                **project,
                "teams": []
            })

    return enriched_projects


@router.get("/dashboard-data", response_model=Dict[str, Any])
async def get_dashboard_data(current_user: dict = Depends(get_test_user), rbac_client: CosmosRbacClient = Depends(get_rbac_client)):
    """
    Get all data needed for admin dashboard pages in a single call.
    Returns teams, projects, users, and regions with all relationships.
    """
    import asyncio

    try:
        # Get all base data in parallel
        teams_task = rbac_client.get_accessible_teams(current_user["id"])
        projects_task = rbac_client.get_accessible_projects(current_user["id"])
        users_task = rbac_client.get_accessible_users(current_user["id"])

        # Get regions based on user role
        if current_user["role"] == UserRole.SUPER_ADMIN.value:
            regions_task = rbac_client.get_regions()
        elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
            region_id = current_user.get("region")
            if region_id:
                region = await rbac_client.get_region(region_id)
                regions_task = [region] if region else []
            else:
                regions_task = []
        else:
            region_id = current_user.get("region")
            if region_id:
                region = await rbac_client.get_region(region_id)
                regions_task = [region] if region else []
            else:
                regions_task = []

        # Wait for base data
        teams, projects, users = await asyncio.gather(teams_task, projects_task, users_task)
        regions = regions_task if isinstance(regions_task, list) else await regions_task

        # Build project-team relationships
        project_teams_map = {}
        for project in projects:
            try:
                project_teams = await rbac_client.get_project_teams(project["id"])
                project_teams_map[project["id"]] = [pt["teamId"] for pt in project_teams]
            except Exception as e:
                rbac_logger.warning(f"Failed to get teams for project {project['id']}: {e}")
                project_teams_map[project["id"]] = []

        # Enrich teams with members and projects
        enriched_teams = []
        for team in teams:
            try:
                team_members = await rbac_client.get_team_members(team["id"])
                member_ids = [member["userId"] for member in team_members]

                # Find projects this team is assigned to
                assigned_projects = []
                for project_id, team_ids in project_teams_map.items():
                    if team["id"] in team_ids:
                        assigned_projects.append(project_id)

                enriched_teams.append({
                    **team,
                    "members": member_ids,
                    "projects": assigned_projects
                })
            except Exception as e:
                rbac_logger.warning(f"Failed to enrich team {team['id']}: {e}")
                enriched_teams.append({
                    **team,
                    "members": [],
                    "projects": []
                })

        # Enrich projects with teams
        enriched_projects = []
        for project in projects:
            team_ids = project_teams_map.get(project["id"], [])
            enriched_projects.append({
                **project,
                "teams": team_ids
            })

        return {
            "teams": enriched_teams,
            "projects": enriched_projects,
            "users": users,
            "regions": regions
        }

    except Exception as e:
        rbac_logger.error(f"Error fetching dashboard data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch dashboard data")
