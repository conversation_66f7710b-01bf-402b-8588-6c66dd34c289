#!/usr/bin/env python3
"""
Update a project in CosmosDB with deployment summary.
This script demonstrates how to use the deployment summary to update a project in CosmosDB.
"""

import os
import sys
import json
import logging
import argparse
import uuid
import time
from datetime import datetime, timezone
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def is_valid_uuid(uuid_to_test: str, version: int = 4) -> bool:
    """
    Check if uuid_to_test is a valid UUID.

    Args:
        uuid_to_test (str): String to test
        version (int): UUID version (1, 3, 4, or 5)

    Returns:
        bool: True if valid UUID, False otherwise
    """
    try:
        uuid_obj = uuid.UUID(str(uuid_to_test), version=version)
        return str(uuid_obj) == str(uuid_to_test).lower()
    except (ValueError, AttributeError, TypeError):
        return False


def validate_deployment_summary(summary: dict) -> bool:
    """
    Validate the deployment summary structure.

    Args:
        summary (dict): The deployment summary to validate

    Returns:
        bool: True if valid, False otherwise
    """
    if not isinstance(summary, dict):
        logger.error("Deployment summary must be a dictionary")
        return False

    required_fields = ["status", "resources"]
    for field in required_fields:
        if field not in summary:
            logger.error(f"Deployment summary is missing required field: {field}")
            return False

    return True


def update_project_with_deployment(project_id: str, deployment_summary: dict) -> bool:
    """
    Update a project in CosmosDB with deployment summary.

    Args:
        project_id (str): The ID of the project (must be a valid UUID v4)
        deployment_summary (dict): Dictionary with deployment summary

    Returns:
        bool: True if update was successful, False otherwise
    """
    # Validate project_id
    if not is_valid_uuid(project_id):
        logger.error(
            f"Invalid project_id format: {project_id}. Must be a valid UUID v4."
        )
        return False

    # Validate deployment_summary
    if not validate_deployment_summary(deployment_summary):
        logger.error("Invalid deployment summary provided")
        return False

    logger.info(
        f"[update_project_with_deployment] Starting update for project {project_id}"
    )
    logger.debug(
        f"[update_project_with_deployment] Deployment summary keys: {list(deployment_summary.keys())}"
    )

    try:
        # Log the deployment summary (without sensitive data)
        safe_summary = {k: v for k, v in deployment_summary.items()}
        if "resources" in safe_summary and isinstance(safe_summary["resources"], dict):
            safe_summary["resources"] = {
                k: (
                    "[REDACTED]"
                    if "key" in k.lower()
                    or "secret" in k.lower()
                    or "token" in k.lower()
                    else v
                )
                for k, v in safe_summary["resources"].items()
            }

        logger.info(
            f"[update_project_with_deployment] Processing deployment summary for project {project_id}"
        )
        logger.debug(
            f"[update_project_with_deployment] Safe deployment summary: {json.dumps(safe_summary, indent=2)}"
        )

        # Import the CosmosClient
        try:
            from azure.cosmos import CosmosClient
        except ImportError:
            logger.error(
                "azure-cosmos package not installed. Install it with: pip install azure-cosmos"
            )
            return False

        # Get CosmosDB connection details from environment variables with validation
        missing_vars = []

        cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT") or os.environ.get(
            "COSMOSDB_ACCOUNT"
        )
        if not cosmos_account:
            missing_vars.append("AZURE_COSMOSDB_ACCOUNT or COSMOSDB_ACCOUNT")

        cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY") or os.environ.get(
            "COSMOSDB_KEY"
        )
        if not cosmos_key:
            missing_vars.append("AZURE_COSMOSDB_ACCOUNT_KEY or COSMOSDB_KEY")

        cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE") or os.environ.get(
            "COSMOSDB_DATABASE"
        )
        if not cosmos_database:
            missing_vars.append("AZURE_COSMOSDB_DATABASE or COSMOSDB_DATABASE")

        if missing_vars:
            logger.error(
                f"[update_project_with_deployment] Missing required environment variables: {', '.join(missing_vars)}"
            )
            return False

        cosmos_container = "projects"  # Always use 'projects' container

        # Log configuration (without sensitive data)
        logger.info(
            f"[update_project_with_deployment] Using CosmosDB account: {cosmos_account}"
        )
        logger.info(
            f"[update_project_with_deployment] Using database: {cosmos_database}"
        )
        logger.info(
            f"[update_project_with_deployment] Using container: {cosmos_container}"
        )

        # Create a CosmosClient with retry logic
        from azure.cosmos import exceptions as cosmos_exceptions
        from azure.core.exceptions import ServiceRequestError

        max_retries = 3
        retry_count = 0
        client = None

        while retry_count < max_retries:
            try:
                logger.info(
                    f"[update_project_with_deployment] Creating CosmosClient (attempt {retry_count + 1}/{max_retries})"
                )
                endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
                client = CosmosClient(endpoint, cosmos_key)
                logger.info(
                    "[update_project_with_deployment] Successfully created CosmosClient"
                )
                break  # Success, exit retry loop

            except (
                cosmos_exceptions.CosmosHttpResponseError,
                ServiceRequestError,
            ) as e:
                retry_count += 1
                if retry_count >= max_retries:
                    logger.error(
                        f"[update_project_with_deployment] Failed to create CosmosClient after {max_retries} attempts: {str(e)}"
                    )
                    return False

                logger.warning(
                    f"[update_project_with_deployment] Error creating CosmosClient (attempt {retry_count}/{max_retries}): {str(e)}"
                )
                time.sleep(2**retry_count)  # Exponential backoff

        if client is None:
            logger.error(
                "[update_project_with_deployment] Failed to create CosmosClient after all retries"
            )
            return False

        # Get the database with error handling
        try:
            logger.info(
                f"[update_project_with_deployment] Getting database client for '{cosmos_database}'"
            )
            database = client.get_database_client(cosmos_database)
            # Verify database exists by reading its properties
            database.read()
            logger.info(
                f"[update_project_with_deployment] Successfully connected to database '{cosmos_database}'"
            )
        except Exception as e:
            logger.error(
                f"[update_project_with_deployment] Error accessing database '{cosmos_database}': {str(e)}"
            )
            return False

        # Get the projects container with error handling
        try:
            logger.info(
                f"[update_project_with_deployment] Getting container client for '{cosmos_container}'"
            )
            projects_container = database.get_container_client(cosmos_container)
            # Verify container exists by reading its properties
            projects_container.read()
            logger.info(
                f"[update_project_with_deployment] Successfully connected to container '{cosmos_container}'"
            )
        except Exception as e:
            logger.error(
                f"[update_project_with_deployment] Error accessing container '{cosmos_container}': {str(e)}"
            )
            return False

        # Query for the project with detailed logging
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        parameters = [{"name": "@projectId", "value": project_id}]

        logger.info(
            f"[update_project_with_deployment] Querying for project with ID: {project_id}"
        )
        logger.debug(
            f"[update_project_with_deployment] Query: {query} with parameters: {parameters}"
        )

        try:
            # Execute the query
            projects = list(
                projects_container.query_items(
                    query=query,
                    parameters=parameters,
                    enable_cross_partition_query=True,
                )
            )
            logger.info(
                f"[update_project_with_deployment] Found {len(projects)} matching projects"
            )

            if not projects:
                logger.warning(
                    f"[update_project_with_deployment] Project {project_id} not found in CosmosDB"
                )

                # Log sample of existing projects for debugging (without sensitive data)
                try:
                    sample_query = "SELECT TOP 3 c.id, c.type, c.name FROM c WHERE c.type = 'project'"
                    sample_projects = list(
                        projects_container.query_items(
                            query=sample_query, enable_cross_partition_query=True
                        )
                    )
                    logger.debug(
                        f"[update_project_with_deployment] Sample of existing projects: {json.dumps(sample_projects, indent=2)}"
                    )
                except Exception as e:
                    logger.debug(
                        f"[update_project_with_deployment] Could not fetch sample projects: {str(e)}"
                    )

                return False

        except Exception as e:
            logger.error(
                f"[update_project_with_deployment] Error querying for project: {str(e)}",
                exc_info=True,
            )
            return False

        # Get the project document
        project_doc = projects[0]
        logger.info(
            f"[update_project_with_deployment] Found project document for {project_id}"
        )

        # Log document fields for debugging (excluding sensitive data)
        doc_fields = [k for k in project_doc.keys() if not k.startswith("_")]
        logger.debug(
            f"[update_project_with_deployment] Project document fields: {doc_fields}"
        )

        # Retrieve the project's region for partition key operations
        region = project_doc.get("region")
        if not region:
            logger.error(
                f"[update_project_with_deployment] Project document for {project_id} is missing the required 'region' field"
            )
            logger.debug(
                f"[update_project_with_deployment] Available fields: {list(project_doc.keys())}"
            )
            return False

        logger.info(f"[update_project_with_deployment] Project region: {region}")

        # Extract resource data from deployment summary
        resources = deployment_summary.get("resources", {})

        # Log the full resources dictionary for debugging
        logger.info(
            f"Resources from deployment summary: {json.dumps(resources, indent=2)}"
        )

        # Map deployment summary fields to project document fields
        field_mapping = {
            "storage_account_name": "storage_account_name",
            "uploads_container": "storage_container_uploads",
            "input_container": "storage_container_input",
            "output_container": "storage_container_output",
            "search_service_name": "search_service_name",
            "search_index_name": "search_index_name",
            "search_indexer_name": "search_indexer_name",
            "search_datasource_name": "search_datasource_name",
            "function_app_name": "function_app_name",
            "function_app_url": "function_app_url",
            "event_grid_subscription_name": "event_grid_subscription_name",
        }

        # Update the project document with resource data
        updated = False

        # Log the current state of container fields before update
        logger.info("Current container field values in project document:")
        logger.info(
            f"storage_container_uploads: {project_doc.get('storage_container_uploads', 'None')}"
        )
        logger.info(
            f"storage_container_input: {project_doc.get('storage_container_input', 'None')}"
        )
        logger.info(
            f"storage_container_output: {project_doc.get('storage_container_output', 'None')}"
        )

        # Log the container values from resources
        logger.info("Container values from deployment resources:")
        logger.info(f"uploads_container: {resources.get('uploads_container', 'None')}")
        logger.info(f"input_container: {resources.get('input_container', 'None')}")
        logger.info(f"output_container: {resources.get('output_container', 'None')}")

        for src_field, dest_field in field_mapping.items():
            if src_field in resources and resources[src_field]:
                # Log before update for container fields
                if src_field in [
                    "uploads_container",
                    "input_container",
                    "output_container",
                ]:
                    logger.info(
                        f"Updating container field: {src_field} -> {dest_field}"
                    )
                    logger.info(
                        f"  Before: {dest_field}={project_doc.get(dest_field, 'None')}"
                    )
                    logger.info(f"  After: {dest_field}={resources[src_field]}")

                # Update the field
                project_doc[dest_field] = resources[src_field]
                updated = True
                logger.info(f"Updated {dest_field} to {resources[src_field]}")

        # Initialize or get the environment object
        environment = project_doc.get("environment", {})

        # Extract environment variables from resources
        env_vars = {
            "AZURE_SEARCH_KEY": resources.get(
                "search_key"
            ),  # Handle both naming conventions
            "AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG": resources.get(
                "azure_search_semantic_search_config"
            ),
            "STORAGE_ACCOUNT_SAS_TOKEN": resources.get("storage_account_sas_token"),
            "FUNCTION_KEY_MATURITY": resources.get("function_key_maturity"),
            "FUNCTION_KEY_EXECUTIVE_SUMMARY": resources.get(
                "function_key_executive_summary"
            ),
            "FUNCTION_KEY_POWERPOINT": resources.get("function_key_powerpoint"),
            "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL": resources.get(
                "azure_function_maturity_assessment_url"
            ),
            "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL": resources.get(
                "azure_function_executive_summary_url"
            ),
            "FUNCTION_APP_URL": resources.get("function_app_url"),
        }

        # Ensure we have the correct storage and search service names in the environment
        if resources.get("storage_account_name"):
            env_vars["STORAGE_ACCOUNT_NAME"] = resources.get("storage_account_name")

        # Add container names to environment variables
        if resources.get("uploads_container"):
            env_vars["STORAGE_CONTAINER_UPLOADS"] = resources.get("uploads_container")

        if resources.get("input_container"):
            env_vars["STORAGE_CONTAINER_INPUT"] = resources.get("input_container")

        if resources.get("output_container"):
            env_vars["STORAGE_CONTAINER_OUTPUT"] = resources.get("output_container")

        if resources.get("search_service_name"):
            env_vars["SEARCH_SERVICE_NAME"] = resources.get("search_service_name")

        if resources.get("search_index_name"):
            env_vars["SEARCH_INDEX"] = resources.get("search_index_name")

        # Track which environment variables get updated
        updated_env_vars = []

        # Update environment variables in the project document
        for key, value in env_vars.items():
            if value is not None:
                environment[key] = value
                updated_env_vars.append(key)
                updated = True

        # If there are environment variables directly in the deployment summary, use those too
        if "environment" in deployment_summary and deployment_summary["environment"]:
            for key, value in deployment_summary["environment"].items():
                if value is not None:
                    environment[key] = value
                    updated_env_vars.append(key)
                    updated = True

        # Write environment back to the project document
        project_doc["environment"] = environment

        if updated_env_vars:
            logger.info(f"Updated environment variables: {', '.join(updated_env_vars)}")

        # Update the timestamp
        if updated:
            project_doc["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update deployment status
            if "deployment_status" not in project_doc:
                project_doc["deployment_status"] = {}

            project_doc["deployment_status"]["status"] = deployment_summary.get(
                "status", "success"
            )
            project_doc["deployment_status"]["message"] = "Project deployment completed"
            project_doc["deployment_status"]["updated_at"] = datetime.now(
                timezone.utc
            ).isoformat()

            resource_durations = deployment_summary.get("resource_durations")
            if resource_durations:
                details = project_doc["deployment_status"].get("details", {})
                details.setdefault("resource_times", {}).update(resource_durations)
                project_doc["deployment_status"]["details"] = details


            # Log the final state of container fields before update to CosmosDB
            logger.info("Final container field values before CosmosDB update:")
            logger.info(
                f"storage_container_uploads: {project_doc.get('storage_container_uploads', 'None')}"
            )
            logger.info(
                f"storage_container_input: {project_doc.get('storage_container_input', 'None')}"
            )
            logger.info(
                f"storage_container_output: {project_doc.get('storage_container_output', 'None')}"
            )

            # Update the project document
            update_success = False
            try:
                # Use the document's id as the item parameter, not _rid
                logger.info(
                    f"Attempting to update project {project_id} with partition key {region}"
                )

                # Create a copy of the document for logging (without sensitive data)
                safe_doc = {
                    k: v
                    for k, v in project_doc.items()
                    if not (
                        isinstance(v, dict)
                        and any(
                            sensitive in k.lower()
                            for sensitive in ["key", "token", "secret"]
                        )
                    )
                }
                logger.debug(
                    f"Document to update (safe version): {json.dumps(safe_doc, indent=2)}"
                )

                projects_container.replace_item(
                    item=project_doc["id"],
                    body=project_doc,
                    partition_key=region,  # Add partition key for proper routing
                )
                logger.info(f"Successfully updated project {project_id} in CosmosDB")
                update_success = True
            except Exception as e:
                logger.error(f"Failed to replace project {project_id} in CosmosDB: {e}")
                logger.error(f"Error type: {type(e).__name__}")

                # Try alternative approach without partition key
                try:
                    logger.info(f"Retrying without partition key...")
                    projects_container.replace_item(
                        item=project_doc["id"], body=project_doc
                    )
                    logger.info(
                        f"Successfully updated project {project_id} in CosmosDB on second attempt"
                    )
                    update_success = True
                except Exception as e2:
                    logger.error(
                        f"Failed to replace project {project_id} in CosmosDB on second attempt: {e2}"
                    )
                    logger.error(f"Error type: {type(e2).__name__}")
                    update_success = False

            # Verify the update was successful by querying the document again
            if update_success:
                try:
                    logger.info(f"Verifying update by querying the document again")
                    query = (
                        "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
                    )
                    parameters = [{"name": "@projectId", "value": project_id}]

                    updated_projects = list(
                        projects_container.query_items(
                            query=query,
                            parameters=parameters,
                            enable_cross_partition_query=True,
                        )
                    )

                    if updated_projects:
                        updated_doc = updated_projects[0]
                        logger.info("Container fields after update verification:")
                        logger.info(
                            f"storage_container_uploads: {updated_doc.get('storage_container_uploads', 'None')}"
                        )
                        logger.info(
                            f"storage_container_input: {updated_doc.get('storage_container_input', 'None')}"
                        )
                        logger.info(
                            f"storage_container_output: {updated_doc.get('storage_container_output', 'None')}"
                        )
                    else:
                        logger.warning(
                            f"Could not find project {project_id} after update"
                        )
                except Exception as e:
                    logger.error(f"Error verifying update: {e}")
                    # Don't change update_success here, as the update might have succeeded even if verification fails

            # Only notify WebSocket service if update was successful
            if update_success:
                # Notify the WebSocket service to update the project configuration
                try:
                    import requests

                    # Get the WebSocket service URL from environment or use default
                    websocket_service_url = os.environ.get(
                        "WEBSOCKET_SERVICE_URL", "http://localhost:50505"
                    )
                    project_update_url = f"{websocket_service_url}/api/project-update"

                    # Call the project-update endpoint
                    logger.info(
                        f"Notifying WebSocket service to update project {project_id}"
                    )
                    response = requests.post(
                        project_update_url,
                        json={"project_id": project_id, "user_id": "anonymous"},
                    )

                    if response.status_code == 200:
                        logger.info(
                            f"Successfully notified WebSocket service: {response.json()}"
                        )
                    else:
                        logger.warning(
                            f"Failed to notify WebSocket service: {response.status_code} - {response.text}"
                        )
                except Exception as e:
                    logger.warning(f"Error notifying WebSocket service: {e}")

            return update_success
        else:
            logger.info("No changes needed to project document")
            return True

    except Exception as e:
        logger.error(f"Error updating project with deployment summary: {e}")
        return False


def create_dummy_deployment_summary(project_id, project_name="test-project"):
    """
    Create a dummy deployment summary for testing.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project

    Returns:
        dict: Dummy deployment summary
    """
    return {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": "a50e18f3-178d-169a-70a50-a51a52a53a54a55a56",
        "resources": {
            "storage_account_name": f"st{project_name.lower().replace('-', '').replace('_', '')}01",
            "storage_account_sas_token": "?sv=2021-06-08&ss=bfqt&srt=sco&sp=rwdlacupitfx&se=2023-06-01T00:00:00Z&st=2022-06-01T00:00:00Z&spr=https&sig=DUMMY_SAS_TOKEN",
            "uploads_container": f"uploads-{project_name.lower()}-01",
            "input_container": f"input-{project_name.lower()}-01",
            "output_container": f"output-{project_name.lower()}-01",
            "search_service_name": f"search-{project_name.lower()}-01",
            "search_index_name": f"project-{project_name.lower()}-index",
            "search_indexer_name": f"project-{project_name.lower()}-indexer",
            "search_key": "DUMMY_SEARCH_API_KEY_00000000000000000000000000000000",  # Use consistent naming with actual deployment summaries
            "azure_search_semantic_search_config": "default",
            "search_datasource_name": f"project-{project_name.lower()}-ds",
            "function_app_name": f"func-{project_name.lower()}-01",
            "function_app_url": f"https://func-{project_name.lower()}-01.azurewebsites.net",
            "function_key_maturity": "DUMMY_FUNCTION_KEY_MATURITY",
            "function_key_executive_summary": "DUMMY_FUNCTION_KEY_EXECUTIVE_SUMMARY",
            "function_key_powerpoint": "DUMMY_FUNCTION_KEY_POWERPOINT",
            "azure_function_maturity_assessment_url": "https://func-dummy-test.azurewebsites.net/api/HttpTriggerAppMaturityAssessment",
            "azure_function_executive_summary_url": "https://func-dummy-test.azurewebsites.net/api/HttpTriggerAppExecutiveSummary",
            "event_grid_subscription_name": f"evgs-{project_name.lower()}-01",
        },
        "status": "success",
        "deployment_time": "5s",
        "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
    }


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Update a project in CosmosDB with deployment summary"
    )
    parser.add_argument("project_id", help="The UUID of the project")
    parser.add_argument(
        "--summary-file", help="Path to the deployment summary JSON file"
    )
    parser.add_argument(
        "--generate-dummy",
        action="store_true",
        help="Generate a dummy deployment summary for testing",
    )

    args = parser.parse_args()

    # Get the deployment summary
    deployment_summary = None

    if args.summary_file:
        # Read from file
        try:
            with open(args.summary_file, "r") as f:
                deployment_summary = json.load(f)
            logger.info(f"Read deployment summary from {args.summary_file}")
        except Exception as e:
            logger.error(f"Error reading deployment summary file: {e}")
            return 1
    elif args.generate_dummy:
        # Generate a dummy deployment summary
        try:
            # Generate the dummy summary using our local function
            project_name = f"test-{datetime.now().strftime('%m%d%H%M')}"
            deployment_summary = create_dummy_deployment_summary(
                args.project_id, project_name
            )
            logger.info(
                f"Generated dummy deployment summary for project {args.project_id}"
            )

            # Save the dummy summary to a file for reference
            dummy_file = f"dummy_deployment_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(dummy_file, "w") as f:
                json.dump(deployment_summary, f, indent=2)
            logger.info(f"Saved dummy deployment summary to {dummy_file}")
        except Exception as e:
            logger.error(f"Error generating dummy deployment summary: {e}")
            return 1
    else:
        logger.error(
            "No deployment summary provided. Use --summary-file or --generate-dummy"
        )
        return 1

    # Update the project
    success = update_project_with_deployment(args.project_id, deployment_summary)

    if success:
        logger.info(
            f"Successfully updated project {args.project_id} with deployment summary"
        )
        return 0
    else:
        logger.error(
            f"Failed to update project {args.project_id} with deployment summary"
        )
        return 1


if __name__ == "__main__":
    sys.exit(main())
