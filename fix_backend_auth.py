#!/usr/bin/env python3
"""
Backend Authentication Fix Script

This script fixes authentication issues in the AI Scope App backend by:
1. Ensuring user roles are correctly preserved from the database
2. Improving logging for authentication flows
3. Fixing the default role assignment for new users

Usage:
    python fix_backend_auth.py

Author: AI Assistant
"""

import os
import sys
import re
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('backend-auth-fix')

# Define the workspace root
WORKSPACE_ROOT = Path('/workspaces/ai-scope-app2')

def fix_user_context():
    """Fix the user_context.py file to ensure roles are preserved from the database."""
    user_context_path = WORKSPACE_ROOT / 'backend' / 'rbac' / 'user_context.py'
    
    if not user_context_path.exists():
        logger.error(f"User context file not found at {user_context_path}")
        return False
    
    logger.info(f"Fixing user context at {user_context_path}")
    
    with open(user_context_path, 'r') as f:
        content = f.read()
    
    # Find all occurrences of the role assignment pattern
    role_assignment_pattern = r'            # Ensure role is preserved from database\n            current_user\["role"\] = db_user\.get\("role", current_user\.get\("role", UserRole\.REGULAR_USER\.value\)\)'
    
    # Count occurrences to ensure we're replacing the right ones
    occurrences = re.findall(role_assignment_pattern, content)
    logger.info(f"Found {len(occurrences)} occurrences of role assignment pattern")
    
    # Replace with improved role assignment logic
    improved_role_assignment = '''            # Ensure role is preserved from database
            # This is critical - the database role should always take precedence
            db_role = db_user.get("role")
            if db_role:
                current_user["role"] = db_role
                logging.info(f"Using role from database for user {current_user.get('email')}: {db_role}")
            else:
                # Only use the current_user role if no role in database
                current_user["role"] = current_user.get("role", UserRole.REGULAR_USER.value)
                logging.info(f"No role in database for user {current_user.get('email')}, using: {current_user['role']}")'''
    
    # Replace each occurrence individually with context to ensure we're replacing the right ones
    for i, occurrence in enumerate(occurrences):
        # Get some context around the occurrence to make the replacement more specific
        context_pattern = r'            # Update current_user with database values while preserving authentication info\n            for key, value in db_user\.items\(\):\n                if key not in current_user:\n                    current_user\[key\] = value\n\n            # Ensure role is preserved from database\n            current_user\["role"\] = db_user\.get\("role", current_user\.get\("role", UserRole\.REGULAR_USER\.value\)\)'
        
        replacement = '''            # Update current_user with database values while preserving authentication info
            for key, value in db_user.items():
                if key not in current_user:
                    current_user[key] = value

            # Ensure role is preserved from database
            # This is critical - the database role should always take precedence
            db_role = db_user.get("role")
            if db_role:
                current_user["role"] = db_role
                logging.info(f"Using role from database for user {current_user.get('email')}: {db_role}")
            else:
                # Only use the current_user role if no role in database
                current_user["role"] = current_user.get("role", UserRole.REGULAR_USER.value)
                logging.info(f"No role in database for user {current_user.get('email')}, using: {current_user['role']}")'''
        
        content = content.replace(context_pattern, replacement, 1)
    
    # Write the updated content back to the file
    with open(user_context_path, 'w') as f:
        f.write(content)
    
    logger.info("User context fixed successfully")
    return True

def fix_entra_auth():
    """Fix the entra_auth.py file to improve role handling."""
    entra_auth_path = WORKSPACE_ROOT / 'backend' / 'auth' / 'entra_auth.py'
    
    if not entra_auth_path.exists():
        logger.error(f"Entra auth file not found at {entra_auth_path}")
        return False
    
    logger.info(f"Fixing Entra auth at {entra_auth_path}")
    
    with open(entra_auth_path, 'r') as f:
        content = f.read()
    
    # Fix the role assignment in get_entra_user_with_delegated_token
    role_assignment_pattern = r'    # Convert to the format expected by the application\n    # Ensure we have a default role if none was provided by the Graph API\n    user_data = \{\n        "id": user_info\["id"\],\n        "name": user_info\["name"\],\n        "email": user_info\["email"\]\n    \}\n\n    # Only set a default role if one wasn\'t provided by the Graph API\n    # This allows the role to be set from the database in get_authenticated_user\n    if "role" in user_info:\n        user_data\["role"\] = user_info\["role"\]\n    else:\n        # Default to REGULAR_USER if no role is provided\n        # This will be overridden by the database role in get_authenticated_user\n        user_data\["role"\] = "REGULAR_USER"'
    
    improved_role_assignment = '''    # Convert to the format expected by the application
    # Ensure we have a default role if none was provided by the Graph API
    user_data = {
        "id": user_info["id"],
        "name": user_info["name"],
        "email": user_info["email"]
    }

    # Only set a default role if one wasn't provided by the Graph API
    # This allows the role to be set from the database in get_authenticated_user
    if "role" in user_info:
        user_data["role"] = user_info["role"]
        logging.info(f"Using role from Graph API for user {user_info['email']}: {user_info['role']}")
    else:
        # Don't set a default role here to allow the database role to take precedence
        # in get_authenticated_user. This helps ensure the correct role is used.
        logging.info(f"No role in Graph API for user {user_info['email']}, will use database role")'''
    
    content = content.replace(role_assignment_pattern, improved_role_assignment)
    
    # Write the updated content back to the file
    with open(entra_auth_path, 'w') as f:
        f.write(content)
    
    logger.info("Entra auth fixed successfully")
    return True

def main():
    """Main function to fix backend authentication issues."""
    logger.info("Starting backend authentication fix script")
    
    # Fix backend files
    fix_user_context()
    fix_entra_auth()
    
    logger.info("Backend authentication fix script completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
