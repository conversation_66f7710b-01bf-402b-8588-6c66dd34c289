#!/usr/bin/env python3
"""
Authentication Fix Script

This script fixes authentication issues in the AI Scope App by:
1. Ensuring the correct client ID is used in the frontend
2. Fixing redirect URI configuration
3. Ensuring user roles are correctly preserved from the database

Usage:
    python fix_auth.py

Author: AI Assistant
"""

import os
import sys
import re
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('auth-fix')

# Define the workspace root
WORKSPACE_ROOT = Path('/workspaces/ai-scope-app2')

def fix_msal_config():
    """Fix the MSAL configuration in the frontend."""
    msal_config_path = WORKSPACE_ROOT / 'frontend' / 'src' / 'auth' / 'msal-config.ts'
    
    if not msal_config_path.exists():
        logger.error(f"MSAL config file not found at {msal_config_path}")
        return False
    
    logger.info(f"Fixing MSAL config at {msal_config_path}")
    
    with open(msal_config_path, 'r') as f:
        content = f.read()
    
    # 1. Fix client ID
    content = re.sub(
        r'export const clientId = import\.meta\.env\.VITE_AZURE_CLIENT_ID \|\| "d172aee2-f5e4-408f-acb9-4c245c31bddc";',
        'export const clientId = import.meta.env.VITE_AZURE_CLIENT_ID || "bb1ebfc1-47d8-4273-9206-3acc107c1e35";',
        content
    )
    
    # 2. Fix redirect URI configuration
    content = re.sub(
        r'  if \(isProduction\) \{\n    \/\/ Use the actual Azure Web App domain\n    return `https:\/\/ai-scope-app3\.azurewebsites\.net\/\.auth\/login\/aad\/callback`;\n  \} else \{\n    \/\/ Use localhost for development\n    return `http:\/\/localhost:50508\/auth\/callback`;\n  \}',
        '''  if (isProduction) {
    // Use the actual Azure Web App domain from environment variables or fallback
    const prodRedirectUri = import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI || 
      `https://ai-scope-app3.azurewebsites.net/.auth/login/aad/callback`;
    console.log('Using production redirect URI:', prodRedirectUri);
    return prodRedirectUri;
  } else {
    // Use localhost for development from environment variables or fallback
    const devRedirectUri = import.meta.env.VITE_AZURE_REDIRECT_URI || 
      `http://localhost:50508/auth/callback`;
    console.log('Using development redirect URI:', devRedirectUri);
    return devRedirectUri;
  }''',
        content
    )
    
    # 3. Update validRedirectUris array
    content = re.sub(
        r'\/\/ List of all valid redirect URIs \(should match those configured in Azure Portal\)\nconst validRedirectUris = \[\n  `http:\/\/localhost:50508\/`,\n  `http:\/\/localhost:50508\/auth\/callback`,\n  `http:\/\/localhost:8000\/test_browser_auth\.html`,\n  `http:\/\/localhost:8000\/test_redirect_auth\.html`,\n  `https:\/\/ai-scope-app3\.azurewebsites\.net\/\.auth\/login\/aad\/callback`\n\];',
        '''// List of all valid redirect URIs (should match those configured in Azure Portal)
// This array is used for validation and debugging purposes
const validRedirectUris = [
  // Development URIs
  `http://localhost:50508/`,
  `http://localhost:50508/auth/callback`,
  `http://localhost:8000/test_browser_auth.html`,
  `http://localhost:8000/test_redirect_auth.html`,
  // Production URIs - include both hardcoded and environment variable values
  `https://ai-scope-app3.azurewebsites.net/.auth/login/aad/callback`,
  `https://ai-scope-app1.azurewebsites.net/.auth/login/aad/callback`,
  // Add environment variable values if they exist and are not already included
  ...(import.meta.env.VITE_AZURE_REDIRECT_URI ? [import.meta.env.VITE_AZURE_REDIRECT_URI] : []),
  ...(import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI ? [import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI] : [])
];''',
        content
    )
    
    with open(msal_config_path, 'w') as f:
        f.write(content)
    
    logger.info("MSAL config fixed successfully")
    return True

def fix_auto_login():
    """Fix the AutoLogin component."""
    auto_login_path = WORKSPACE_ROOT / 'frontend' / 'src' / 'pages' / 'AutoLogin.tsx'
    
    if not auto_login_path.exists():
        logger.error(f"AutoLogin file not found at {auto_login_path}")
        return False
    
    logger.info(f"Fixing AutoLogin at {auto_login_path}")
    
    with open(auto_login_path, 'r') as f:
        content = f.read()
    
    # Fix redirect URI in AutoLogin
    content = re.sub(
        r'        \/\/ Use dynamic redirect URI based on environment\n        const redirectUri = window\.location\.hostname === \'localhost\'\n          \? `http:\/\/localhost:50508\/auth\/callback`\n          : `\$\{window\.location\.origin\}\/auth\/callback`;',
        '''        // Use dynamic redirect URI based on environment
        const redirectUri = window.location.hostname === 'localhost'
          ? (import.meta.env.VITE_AZURE_REDIRECT_URI || `http://localhost:50508/auth/callback`)
          : (import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI || `${window.location.origin}/.auth/login/aad/callback`);
        
        console.log('AutoLogin - Using redirect URI:', redirectUri);''',
        content
    )
    
    with open(auto_login_path, 'w') as f:
        f.write(content)
    
    logger.info("AutoLogin fixed successfully")
    return True

def fix_auth_header_service():
    """Fix the authHeaderService.ts file."""
    auth_header_path = WORKSPACE_ROOT / 'frontend' / 'src' / 'services' / 'authHeaderService.ts'
    
    if not auth_header_path.exists():
        logger.error(f"Auth header service file not found at {auth_header_path}")
        return False
    
    logger.info(f"Fixing auth header service at {auth_header_path}")
    
    with open(auth_header_path, 'r') as f:
        content = f.read()
    
    # Fix scopes in getAuthHeaders
    content = re.sub(
        r'  \/\/ Ensure we\'re requesting User\.Read scope for Microsoft Graph API\n  \/\/ Only use specific scopes \(not \.default\) as per Azure Portal configuration\n  const scopesWithUserRead = customScopes \?\n    \[\.\.\\.customScopes, \'User\.Read\', \'openid\'\] :\n    undefined; \/\/ undefined will use default scopes in getAuthToken\n\n  const token = await getAuthToken\(scopesWithUserRead\);',
        '''  // Ensure we're requesting User.Read scope for Microsoft Graph API
  // Only use specific scopes (not .default) as per Azure Portal configuration
  const scopesWithUserRead = customScopes ?
    [...new Set([...customScopes, 'User.Read', 'openid', 'offline_access'])] :
    ['User.Read', 'openid', 'offline_access']; // Always include these essential scopes

  console.log('Getting auth token with scopes:', scopesWithUserRead);
  const token = await getAuthToken(scopesWithUserRead);''',
        content
    )
    
    # Fix scopes in getAuthToken
    content = re.sub(
        r'  \/\/ Use specific scopes only \(not \.default\) as per Azure Portal configuration\n  let scopes = customScopes \|\| \[\.\.\\.graphScopes\];\n\n  \/\/ Ensure we have at least the basic scopes\n  if \(!scopes\.includes\(\'User\.Read\'\)\) \{\n    scopes = \[\.\.\\.scopes, \'User\.Read\', \'openid\', \'profile\'\];\n  \}\n\n  \/\/ Ensure User\.Read scope is included for Microsoft Graph API calls\n  if \(!scopes\.includes\(\'User\.Read\'\)\) scopes\.push\(\'User\.Read\'\);\n  \/\/ Ensure openid scope is included for authentication\n  if \(!scopes\.includes\(\'openid\'\)\) scopes\.push\(\'openid\'\);\n\n  \/\/ Remove any \.default scopes as they cannot be combined with resource-specific scopes\n  scopes = scopes\.filter\(scope => !scope\.endsWith\(\'\.default\'\)\);',
        '''  // Use specific scopes only (not .default) as per Azure Portal configuration
  // Start with the provided scopes or default to graphScopes
  let scopes = customScopes || [...graphScopes];

  // Ensure we have all the essential scopes using Set to avoid duplicates
  const essentialScopes = ['User.Read', 'openid', 'offline_access', 'profile', 'email'];
  scopes = [...new Set([...scopes, ...essentialScopes])];

  // Remove any .default scopes as they cannot be combined with resource-specific scopes
  scopes = scopes.filter(scope => !scope.endsWith('.default'));
  
  console.log('Final scopes for token acquisition:', scopes);''',
        content
    )
    
    with open(auth_header_path, 'w') as f:
        f.write(content)
    
    logger.info("Auth header service fixed successfully")
    return True

def main():
    """Main function to fix authentication issues."""
    logger.info("Starting authentication fix script")
    
    # Fix frontend files
    fix_msal_config()
    fix_auto_login()
    fix_auth_header_service()
    
    logger.info("Authentication fix script completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
