#!/usr/bin/env python3

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from azure.cosmos import CosmosClient, PartitionKey

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_cosmos_query_and_replace():
    """
    Test the Cosmos DB query and replace functionality with the fixed implementation.
    """
    logger.info("Testing Cosmos DB query and replace functionality")
    
    # Get CosmosDB connection details from environment variables
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")
    
    if not cosmos_account or not cosmos_key or not cosmos_database:
        logger.error("AZURE_COSMOSDB_ACCOUNT, AZURE_COSMOSDB_ACCOUNT_KEY, and AZURE_COSMOSDB_DATABASE environment variables must be set")
        return False
    
    # Construct the CosmosDB endpoint URL
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
    
    logger.info(f"Using CosmosDB endpoint: {cosmos_endpoint}")
    logger.info(f"Using CosmosDB database: {cosmos_database}")
    
    # Create a CosmosClient
    client = CosmosClient(cosmos_endpoint, credential=cosmos_key)
    
    # Get the database
    database = client.get_database_client(cosmos_database)
    
    # Get the projects container
    projects_container = database.get_container_client("projects")
    
    # Test project ID and region
    project_id = "29edc487-1c86-44aa-b183-c26c84eab470"
    region_id = "25aed46f-1196-8502-3d250-251252253254255256"
    
    try:
        # First, we need to find the region for the project
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        parameters = [{"name": "@projectId", "value": project_id}]
        
        # Handle potential API differences between SDK versions for query_items
        try:
            # First try with enable_cross_partition_query parameter
            logger.info("Trying query with enable_cross_partition_query parameter")
            projects = list(projects_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))
        except TypeError as e:
            if "unexpected keyword argument 'enable_cross_partition_query'" in str(e):
                # If that fails, try without the parameter
                logger.info("Retrying query without enable_cross_partition_query parameter")
                projects = list(projects_container.query_items(
                    query=query,
                    parameters=parameters
                ))
            else:
                # Re-raise if it's a different TypeError
                raise
        
        if not projects:
            logger.error(f"Project {project_id} not found in database")
            return False
        
        project_doc = projects[0]
        region = project_doc.get("region")
        
        logger.info(f"Found project {project_id} with region {region}")
        
        # Create a test update
        test_field = f"test_field_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        project_doc["test_field"] = test_field
        project_doc["updated_at"] = datetime.now(timezone.utc).isoformat()
        
        # Update the project document
        # Handle potential API differences between SDK versions
        try:
            # First try without partition_key parameter since we're using SDK 4.5.1
            logger.info("Trying replace_item without partition_key parameter")
            projects_container.replace_item(
                item=project_id,
                body=project_doc
            )
            logger.info(f"Successfully updated project {project_id} without partition_key")
            return True
        except Exception as e:
            logger.error(f"Error replacing item without partition_key: {e}")
            # If that fails, try with partition_key parameter as a fallback
            try:
                logger.info("Retrying with partition_key parameter")
                projects_container.replace_item(
                    item=project_id,
                    body=project_doc,
                    partition_key=region
                )
                logger.info(f"Successfully updated project {project_id} with partition_key")
                return True
            except Exception as e2:
                logger.error(f"Error with fallback approach: {e2}")
                return False
    
    except Exception as e:
        logger.error(f"Error testing Cosmos DB query and replace: {e}")
        return False

if __name__ == "__main__":
    # Load environment variables from .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
        logger.info("Loaded environment variables from .env file")
    except ImportError:
        logger.warning("python-dotenv not installed, skipping .env file loading")
    
    # Set the required environment variables explicitly if needed
    if not os.environ.get("AZURE_COSMOSDB_ACCOUNT"):
        os.environ["AZURE_COSMOSDB_ACCOUNT"] = "internal-ai-conversation-history-db"
        logger.info("Set AZURE_COSMOSDB_ACCOUNT environment variable")
    
    if not os.environ.get("AZURE_COSMOSDB_DATABASE"):
        os.environ["AZURE_COSMOSDB_DATABASE"] = "db_conversation_history"
        logger.info("Set AZURE_COSMOSDB_DATABASE environment variable")
    
    # Run the test
    success = test_cosmos_query_and_replace()
    
    if success:
        print("✅ Test completed successfully!")
        sys.exit(0)
    else:
        print("❌ Test failed!")
        sys.exit(1)
