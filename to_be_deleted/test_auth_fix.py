#!/usr/bin/env python3
"""
Test script to verify the authentication fixes work correctly.
This script tests both development and production authentication scenarios.
"""

import asyncio
import os
import sys
import logging

# Add the project root to the Python path
sys.path.insert(0, '/workspaces/ai-scope-app2')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_development_mode():
    """Test authentication in development mode"""
    logger.info("Testing development mode authentication...")
    
    # Set development mode
    os.environ['DEVELOPMENT_MODE'] = 'true'
    
    # Import after setting environment variable
    from backend.auth.auth_utils import get_authenticated_user_details
    
    # Mock request headers (no auth headers)
    mock_headers = {}
    
    try:
        user = await get_authenticated_user_details(mock_headers)
        if user:
            logger.info(f"✅ Development mode: Successfully got user: {user.get('user_email')}")
            logger.info(f"   User ID: {user.get('user_principal_id')}")
            logger.info(f"   User Name: {user.get('user_name')}")
            return True
        else:
            logger.error("❌ Development mode: Failed to get user")
            return False
    except Exception as e:
        logger.error(f"❌ Development mode: Exception: {e}")
        return False

async def test_production_mode_with_easy_auth():
    """Test authentication in production mode with Easy Auth headers"""
    logger.info("Testing production mode with Easy Auth headers...")
    
    # Set production mode
    os.environ['DEVELOPMENT_MODE'] = 'false'
    
    # Import after setting environment variable
    from backend.auth.auth_utils import get_authenticated_user_details
    
    # Mock Easy Auth headers
    mock_headers = {
        'x-ms-client-principal': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9',
        'x-ms-token-aad-id-token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyIsImtpZCI6Ii1LSTNROW5OUjdiUm9meG1lWm9YcWJIWkdldyJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fake_signature'
    }
    
    try:
        user = await get_authenticated_user_details(mock_headers)
        if user:
            logger.info(f"✅ Production mode (Easy Auth): Successfully got user: {user.get('user_email')}")
            logger.info(f"   User ID: {user.get('user_principal_id')}")
            logger.info(f"   User Name: {user.get('user_name')}")
            return True
        else:
            logger.error("❌ Production mode (Easy Auth): Failed to get user")
            return False
    except Exception as e:
        logger.error(f"❌ Production mode (Easy Auth): Exception: {e}")
        return False

async def test_production_mode_no_auth():
    """Test authentication in production mode without auth headers (should fail)"""
    logger.info("Testing production mode without auth headers (should fail)...")
    
    # Set production mode
    os.environ['DEVELOPMENT_MODE'] = 'false'
    
    # Import after setting environment variable
    from backend.auth.auth_utils import get_authenticated_user_details
    
    # Mock request headers (no auth headers)
    mock_headers = {}
    
    try:
        user = await get_authenticated_user_details(mock_headers)
        if user is None:
            logger.info("✅ Production mode (no auth): Correctly returned None")
            return True
        else:
            logger.error(f"❌ Production mode (no auth): Should have returned None but got: {user.get('user_email')}")
            return False
    except Exception as e:
        logger.error(f"❌ Production mode (no auth): Exception: {e}")
        return False

async def test_websocket_auth():
    """Test WebSocket authentication"""
    logger.info("Testing WebSocket authentication...")
    
    # Test development mode
    os.environ['DEVELOPMENT_MODE'] = 'true'
    
    from backend.web_sockets.index_blob_status import handle_websocket_connection
    
    # Mock WebSocket object
    class MockWebSocket:
        def __init__(self, headers=None):
            self.headers = headers or {}
            self.accepted = False
            self.closed = False
            self.close_code = None
            self.close_reason = None
        
        async def accept(self):
            self.accepted = True
        
        async def close(self, code=None, reason=None):
            self.closed = True
            self.close_code = code
            self.close_reason = reason
    
    try:
        # Test development mode
        mock_ws = MockWebSocket()
        # This should work in development mode
        await handle_websocket_connection(mock_ws, "blob", "test-project")
        
        if mock_ws.accepted and not mock_ws.closed:
            logger.info("✅ WebSocket (development): Successfully accepted connection")
            return True
        else:
            logger.error("❌ WebSocket (development): Connection was not accepted or was closed")
            return False
    except Exception as e:
        logger.error(f"❌ WebSocket (development): Exception: {e}")
        return False

async def main():
    """Run all authentication tests"""
    logger.info("Starting authentication fix tests...")
    
    tests = [
        ("Development Mode", test_development_mode),
        ("Production Mode with Easy Auth", test_production_mode_with_easy_auth),
        ("Production Mode without Auth", test_production_mode_no_auth),
        ("WebSocket Authentication", test_websocket_auth),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Authentication fixes are working correctly.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
