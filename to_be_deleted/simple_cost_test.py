#!/usr/bin/env python3
"""
Enhanced test script to check Quart cost API response with real Azure Cost Management data
"""

import requests
import json
import os

def get_auth_headers():
    """Get authentication headers for API calls"""
    # For development, you might need to set these environment variables
    # or modify this function to use your authentication method
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    # Add authorization header if available
    auth_token = os.getenv('AUTH_TOKEN')
    if auth_token:
        headers['Authorization'] = f'Bearer {auth_token}'

    return headers

def test_cost_api():
    """Test the Quart cost API endpoints with real data"""

    base_url = "http://localhost:50505"

    # Known project IDs from backend logs
    test_project_ids = [
        '35a7db33-b409-4873-b3bc-54e42931bd3d',  # Brazil Testing Environment
        'f529b9df-c309-40be-926d-07212d006bfd',  # EU test Environment
        'a70782ac-8957-4e59-b778-b74f86549cad',  # EU test Environment 2
        '0336582f-072a-46c0-a44b-d177ac2e66fc',  # US Testing Environment
        '050813-fix3-test'  # Priority Plot testing project
    ]

    try:
        print("Testing Quart Cost API with Real Azure Data...")
        print("=" * 60)

        headers = get_auth_headers()
        print(f"Using headers: {list(headers.keys())}")

        # Test different time ranges
        time_ranges = ["week", "month", "quarter", "year"]

        for time_range in time_ranges:
            print(f"\n📊 Testing time range: {time_range}")
            print("-" * 40)

            # Test overview endpoint
            print(f"\n🔍 Testing Overview Endpoint")
            url = f"{base_url}/api/cost/overview"
            params = {"timeRange": time_range}

            print(f"Calling: {url}")
            print(f"Parameters: {params}")

            response = requests.get(url, params=params, headers=headers, timeout=10)
            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()

                print(f"\n📋 RAW OVERVIEW RESPONSE:")
                print(json.dumps(data, indent=2))

                print(f"\n📊 OVERVIEW SUMMARY for {time_range}:")
                print(f"- Project Costs: {len(data.get('projectCosts', []))} projects")
                print(f"- Service Costs: {len(data.get('serviceCosts', []))} services")
                print(f"- Region Costs: {len(data.get('regionCosts', []))} regions")
                print(f"- Total Cost: ${data.get('totalCost', 0):,.2f}")

                # Print detailed project costs
                project_costs = data.get('projectCosts', [])
                if project_costs:
                    print(f"\n📋 PROJECT COST DETAILS:")
                    for i, project in enumerate(project_costs, 1):
                        name = project.get('project', 'N/A')
                        project_id = project.get('projectId', 'N/A')
                        cost = project.get('cost', 0)
                        budget = project.get('budget', 0)
                        region = project.get('region', 'N/A')

                        print(f"{i:2d}. {name}")
                        print(f"    ID: {project_id}")
                        print(f"    Cost: ${cost:,.2f}")
                        print(f"    Budget: ${budget:,.2f}")
                        print(f"    Region: {region}")

                        if budget > 0:
                            usage_pct = (cost / budget) * 100
                            print(f"    Usage: {usage_pct:.1f}%")
                        print()

                # Print service costs
                service_costs = data.get('serviceCosts', [])
                if service_costs:
                    print(f"\n🔧 SERVICE COST DETAILS:")
                    for service in service_costs:
                        shared_text = " (Shared)" if service.get('isShared') else ""
                        print(f"- {service.get('service', 'N/A')}{shared_text}: ${service.get('cost', 0):,.2f}")

                # Print region costs
                region_costs = data.get('regionCosts', [])
                if region_costs:
                    print(f"\n🌍 REGION COST DETAILS:")
                    for region in region_costs:
                        print(f"- {region.get('region', 'N/A')}: ${region.get('cost', 0):,.2f}")

                rg_costs = data.get('resourceGroupCosts', [])
                if rg_costs:
                    print(f"\n🏷️ RESOURCE GROUP COST DETAILS:")
                    for rg in rg_costs:
                        print(f"- {rg.get('resourceGroup', 'N/A')}: ${rg.get('cost', 0):,.2f}")

            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")

            # Test resources endpoint
            print(f"\n� Testing Resources Endpoint")
            url = f"{base_url}/api/cost/resources"
            params = {"timeRange": time_range}

            response = requests.get(url, params=params, headers=headers, timeout=10)
            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()

                print(f"\n� RAW RESOURCES RESPONSE:")
                print(json.dumps(data, indent=2))

                print(f"\n🔧 RESOURCES SUMMARY for {time_range}:")
                resources = data.get('resources', [])
                print(f"- Total Resources: {len(resources)}")
                print(f"- Total Cost: ${data.get('totalCost', 0):,.2f}")

                if resources:
                    print(f"\n📋 RESOURCE DETAILS:")
                    for i, resource in enumerate(resources[:10], 1):  # Show top 10
                        name = resource.get('name', 'N/A')
                        resource_type = resource.get('resourceType', 'N/A')
                        cost = resource.get('cost', 0)
                        is_shared = resource.get('isShared', False)
                        region = resource.get('region', 'N/A')

                        shared_text = " (Shared)" if is_shared else ""
                        print(f"{i:2d}. {name}{shared_text}")
                        print(f"    Type: {resource_type}")
                        print(f"    Cost: ${cost:,.2f}")
                        print(f"    Region: {region}")
                        print()

            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")

        # Test specific project filtering
        print(f"\n🎯 Testing Project-Specific Filtering")
        print("-" * 40)

        for project_id in test_project_ids[:3]:  # Test first 3 projects
            print(f"\n📊 Testing Project: {project_id}")

            # Test overview with project filter
            url = f"{base_url}/api/cost/overview"
            params = {"timeRange": "month", "projectId": project_id}

            response = requests.get(url, params=params, headers=headers, timeout=10)
            print(f"Overview Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"Project-filtered costs: {len(data.get('projectCosts', []))} projects")
                print(f"Total cost for project: ${data.get('totalCost', 0):,.2f}")

                # Show the actual project data
                project_costs = data.get('projectCosts', [])
                for project in project_costs:
                    if project.get('projectId') == project_id:
                        print(f"✅ Found project: {project.get('project', 'N/A')}")
                        print(f"   Cost: ${project.get('cost', 0):,.2f}")
                        print(f"   Budget: ${project.get('budget', 0):,.2f}")
                        break
            else:
                print(f"❌ Project filter failed: {response.text}")

        print("\n" + "=" * 60)

    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the application running on localhost:50505?")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cost_api()
