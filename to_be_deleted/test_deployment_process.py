#!/usr/bin/env python3
"""
Test script for the deployment process.
This script tests the deployment of project resources and the CosmosDB SDK client implementation.
"""

import os
import sys
import json
import uuid
import logging
import asyncio
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import the deployment functions
from deploy_project_resources import update_project_resources, update_deployment_status
from backend.utils.azure_resource_helpers import (
    create_storage_account_and_containers,
    create_search_service_and_resources,
    create_function_app_from_acr,
    create_event_grid_topic_and_subscription,
)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Create a file handler for detailed logs
os.makedirs('logs', exist_ok=True)
log_file = f"logs/test_deployment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)
logging.getLogger().addHandler(file_handler)

logging.info(f"Detailed logs will be written to {log_file}")

async def test_update_project_resources():
    """
    Test the update_project_resources function with mock data.
    """
    logging.info("Testing update_project_resources function...")

    # Generate a test project ID
    test_project_id = str(uuid.uuid4())
    logging.info(f"Using test project ID: {test_project_id}")

    # Create mock resource data similar to Bicep deployment outputs
    mock_resource_data = {
        "storageAccountName": {"value": f"st{datetime.now().strftime('%m%d')}test"},
        "uploadsContainerName": {"value": f"uploads-test-{datetime.now().strftime('%m%d')}"},
        "inputContainerName": {"value": f"input-test-{datetime.now().strftime('%m%d')}"},
        "outputContainerName": {"value": f"output-test-{datetime.now().strftime('%m%d')}"},
        "searchServiceName": {"value": f"search-test-{datetime.now().strftime('%m%d')}"},
        "searchIndexName": {"value": "project-test-index"},
        "searchIndexerName": {"value": "project-test-indexer"},
        "searchDatasourceName": {"value": "project-test-ds"}
    }

    # Create a flattened version of the mock data (as if processed from Bicep outputs)
    flattened_resource_data = {}
    for key, value_obj in mock_resource_data.items():
        flattened_resource_data[key] = value_obj["value"]

    logging.info(f"Mock resource data: {json.dumps(flattened_resource_data, indent=2)}")

    # Test the update_project_resources function
    try:
        # First, create a test project in CosmosDB
        from azure.cosmos import CosmosClient

        # Get CosmosDB connection details from environment variables
        cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT", os.environ.get("COSMOSDB_ACCOUNT"))
        cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY", os.environ.get("COSMOSDB_KEY"))
        cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE", os.environ.get("COSMOSDB_DATABASE"))
        cosmos_container = "projects"  # Container name is 'projects'

        # Create a CosmosClient
        endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
        client = CosmosClient(endpoint, cosmos_key)
        logging.info(f"Created CosmosClient for {endpoint}")

        # Get the database
        database = client.get_database_client(cosmos_database)
        logging.info(f"Got database client for {cosmos_database}")

        # Get the projects container
        projects_container = database.get_container_client(cosmos_container)
        logging.info(f"Got container client for {cosmos_container}")

        # Create a test project document
        test_project = {
            "id": test_project_id,
            "type": "project",
            "name": f"test-project-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "description": "Test project for deployment process",
            "region": "westeurope",
            "owner": "1",
            "deployment_status": {
                "status": "pending",
                "message": "Project deployment started",
                "updated_at": datetime.now().isoformat(),
                "details": {
                    "storage": {
                        "storage_account": False,
                        "containers": {
                            "uploads": False,
                            "input": False,
                            "output": False
                        }
                    },
                    "storage_complete": False,
                    "search": {
                        "search_service": False,
                        "index": False,
                        "indexer": False,
                        "datasource": False
                    },
                    "search_complete": False,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "completion_percentage": 0
                }
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # Create the test project in CosmosDB
        try:
            projects_container.create_item(body=test_project)
            logging.info(f"Created test project {test_project_id} in CosmosDB")
        except Exception as e:
            logging.error(f"Error creating test project in CosmosDB: {e}")
            return False

        # Now test the update_project_resources function
        result = await update_project_resources(test_project_id, flattened_resource_data)

        if result:
            logging.info("Successfully updated project resources")

            # Verify the update by querying the project
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": test_project_id}]

            projects = list(projects_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))

            if projects:
                updated_project = projects[0]
                logging.info(f"Retrieved updated project: {json.dumps(updated_project, indent=2)}")

                # Check if the resources were updated correctly
                success = True
                for key, value in flattened_resource_data.items():
                    # Map Bicep output fields to project document fields
                    field_mapping = {
                        "storageAccountName": "storage_account_name",
                        "uploadsContainerName": "storage_container_uploads",
                        "inputContainerName": "storage_container_input",
                        "outputContainerName": "storage_container_output",
                        "searchServiceName": "search_service_name",
                        "searchIndexName": "search_index_name",
                        "searchIndexerName": "search_indexer_name",
                        "searchDatasourceName": "search_datasource_name"
                    }

                    if key in field_mapping:
                        project_field = field_mapping[key]
                        if project_field in updated_project and updated_project[project_field] == value:
                            logging.info(f"Field {project_field} was updated correctly to {value}")
                        else:
                            logging.error(f"Field {project_field} was not updated correctly. Expected {value}, got {updated_project.get(project_field, 'not found')}")
                            success = False

                # Check if deployment status was updated
                if updated_project["deployment_status"]["status"] == "success":
                    logging.info("Deployment status was updated to success")
                else:
                    logging.error(f"Deployment status was not updated to success. Current status: {updated_project['deployment_status']['status']}")
                    success = False

                # Clean up - delete the test project
                try:
                    projects_container.delete_item(item=test_project_id, partition_key=test_project_id)
                    logging.info(f"Deleted test project {test_project_id}")
                except Exception as e:
                    logging.error(f"Error deleting test project: {e}")

                return success
            else:
                logging.error(f"Could not retrieve updated project {test_project_id}")
                return False
        else:
            logging.error("Failed to update project resources")
            return False
    except Exception as e:
        logging.error(f"Error testing update_project_resources: {e}")
        return False

async def test_deployment_process(project_name, region_id="westeurope", resource_group="rg-internal-ai", location="westeurope"):
    """
    Test the full deployment process with a real project.

    Args:
        project_name (str): The name of the project
        region_id (str): The Azure region ID
        resource_group (str): The resource group name
        location (str): The Azure region to deploy to
    """
    logging.info(f"Testing deployment process for project {project_name}...")

    # Generate a test project ID
    test_project_id = f"{datetime.now().strftime('%m%d')}-{project_name.lower()}"
    logging.info(f"Using test project ID: {test_project_id}")

    # Create a test project in CosmosDB
    try:
        from azure.cosmos import CosmosClient

        # Get CosmosDB connection details from environment variables
        cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT", os.environ.get("COSMOSDB_ACCOUNT"))
        cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY", os.environ.get("COSMOSDB_KEY"))
        cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE", os.environ.get("COSMOSDB_DATABASE"))
        cosmos_container = "projects"  # Container name is 'projects'

        # Create a CosmosClient
        endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
        client = CosmosClient(endpoint, cosmos_key)
        logging.info(f"Created CosmosClient for {endpoint}")

        # Get the database
        database = client.get_database_client(cosmos_database)
        logging.info(f"Got database client for {cosmos_database}")

        # Get the projects container
        projects_container = database.get_container_client(cosmos_container)
        logging.info(f"Got container client for {cosmos_container}")

        # Create a test project document
        test_project = {
            "id": test_project_id,
            "type": "project",
            "name": project_name,
            "description": "Test project for deployment process",
            "region": region_id,
            "owner": "1",
            "deployment_status": {
                "status": "pending",
                "message": "Project deployment started",
                "updated_at": datetime.now().isoformat(),
                "details": {
                    "storage": {
                        "storage_account": False,
                        "containers": {
                            "uploads": False,
                            "input": False,
                            "output": False
                        }
                    },
                    "storage_complete": False,
                    "search": {
                        "search_service": False,
                        "index": False,
                        "indexer": False,
                        "datasource": False
                    },
                    "search_complete": False,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "completion_percentage": 0
                }
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # Create the test project in CosmosDB
        try:
            projects_container.create_item(body=test_project)
            logging.info(f"Created test project {test_project_id} in CosmosDB")
        except Exception as e:
            logging.error(f"Error creating test project in CosmosDB: {e}")
            return False

        # Create resources using helper functions
        try:
            logging.info(f"Running resource creation for project {test_project_id}...")
            credential = get_management_credential()
            subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
            storage_client = StorageManagementClient(credential, subscription_id)
            search_client = SearchManagementClient(credential, subscription_id)

            resource_data = {}
            storage_info = create_storage_account_and_containers(
                storage_client,
                resource_group,
                location,
                test_project_id,
            )
            resource_data.update(storage_info)

            keys = storage_client.storage_accounts.list_keys(
                resource_group, storage_info["storage_account_name"]
            )
            account_key = keys.keys[0].value

            search_info = create_search_service_and_resources(
                search_client,
                resource_group,
                location,
                f"search-{test_project_id[:8]}",
                storage_info["storage_account_name"],
                account_key,
                storage_info["uploads_container"],
                f"idx-{test_project_id[:8]}",
                f"ds-{test_project_id[:8]}",
                f"ixr-{test_project_id[:8]}",
            )
            resource_data.update(search_info)
            deployment_outputs = {"resources": resource_data}

            logging.info(f"Bicep deployment completed with outputs: {json.dumps(deployment_outputs, indent=2)}")
            logging.info(f"Resource data: {json.dumps(resource_data, indent=2)}")

            # Update the project resources in CosmosDB
            result = await update_project_resources(test_project_id, resource_data)

            if result:
                logging.info("Successfully updated project resources")

                # Verify the update by querying the project
                query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
                parameters = [{"name": "@projectId", "value": test_project_id}]

                projects = list(projects_container.query_items(
                    query=query,
                    parameters=parameters,
                    enable_cross_partition_query=True
                ))

                if projects:
                    updated_project = projects[0]
                    logging.info(f"Retrieved updated project: {json.dumps(updated_project, indent=2)}")

                    # Check if deployment status was updated
                    if updated_project["deployment_status"]["status"] == "success":
                        logging.info("Deployment status was updated to success")
                        return True
                    else:
                        logging.error(f"Deployment status was not updated to success. Current status: {updated_project['deployment_status']['status']}")
                        return False
                else:
                    logging.error(f"Could not retrieve updated project {test_project_id}")
                    return False
            else:
                logging.error("Failed to update project resources")
                return False
        except Exception as e:
            logging.error(f"Error running Bicep deployment: {e}")
            return False
    except Exception as e:
        logging.error(f"Error testing deployment process: {e}")
        return False

async def main():
    """
    Main function to run the tests.
    """
    parser = argparse.ArgumentParser(description='Test the deployment process')
    parser.add_argument('--test-type', choices=['mock', 'real'], default='mock',
                        help='Type of test to run: mock (uses mock data) or real (runs actual deployment)')
    parser.add_argument('--project-name', default=f'test-{datetime.now().strftime("%Y%m%d%H%M%S")}',
                        help='Project name for real deployment test')
    parser.add_argument('--region-id', default='westeurope',
                        help='Azure region ID for real deployment test')
    parser.add_argument('--resource-group', default='rg-internal-ai',
                        help='Resource group for real deployment test')
    parser.add_argument('--location', default='westeurope',
                        help='Azure region for real deployment test')

    args = parser.parse_args()

    if args.test_type == 'mock':
        logging.info("Running mock test of update_project_resources...")
        success = await test_update_project_resources()
    else:
        logging.info(f"Running real deployment test for project {args.project_name}...")
        success = await test_deployment_process(
            args.project_name,
            args.region_id,
            args.resource_group,
            args.location
        )

    if success:
        logging.info("Test completed successfully!")
        return 0
    else:
        logging.error("Test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
