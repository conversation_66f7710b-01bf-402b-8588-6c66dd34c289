// add_conversations_project_container.bicep
// This Bicep file adds a new 'conversations_project' container to an existing Cosmos DB account

param accountName string
param databaseName string = 'db_conversation_history'

// Reference the existing database
resource database 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases@2022-05-15' existing = {
  name: '${accountName}/${databaseName}'
}

// Create the new conversations_project container
resource conversationsProjectContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/conversations_project'
  properties: {
    resource: {
      id: 'conversations_project'
      partitionKey: { paths: [ '/userId' ] }
    }
    options: {}
  }
}

// Output the container name
output containerName string = conversationsProjectContainer.name
