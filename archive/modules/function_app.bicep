@description('Name of the Azure Function App')
param functionAppName string

@description('App Service Plan name')
param appServicePlanName string

@description('Location for all resources')
param location string = resourceGroup().location

@description('Project ID')
param projectId string

@description('Project name')
param projectName string

// Removed unused parameter: storageAccountName

@description('Storage connection string')
@secure()
param storageConnectionString string

@description('Project storage container for uploads')
param uploadsContainerName string

@description('Project storage container for input')
param inputContainerName string

@description('Project storage container for output')
param outputContainerName string

@description('Project search service name')
param searchServiceName string

@description('Project search API key')
@secure()
param searchApiKey string

@description('Project search index name')
param searchIndexName string

@description('Project search indexer name')
param searchIndexerName string

@description('Project search datasource name')
param searchDatasourceName string

@description('Python version for the Function App')
param pythonVersion string = '3.9'

@description('OpenAI service name')
param openAiServiceName string = 'openai-service'

@description('OpenAI API key')
@secure()
param openAiApiKey string = ''

@description('OpenAI model deployment name')
param openAiModelDeployment string = 'gpt-35-turbo'

// App Service Plan (Linux)
resource appServicePlan 'Microsoft.Web/serverfarms@2022-03-01' = {
  name: appServicePlanName
  location: location
  kind: 'linux'
  sku: {
    name: 'B1' // Basic tier
    tier: 'Basic'
  }
  properties: {
    reserved: true // Required for Linux
  }
}

// Function App
resource functionApp 'Microsoft.Web/sites@2022-03-01' = {
  name: functionAppName
  location: location
  kind: 'functionapp,linux'
  properties: {
    serverFarmId: appServicePlan.id
    reserved: true // Required for Linux
    siteConfig: {
      linuxFxVersion: 'PYTHON|${pythonVersion}'
      alwaysOn: true // Required for Basic tier
      appSettings: [
        {
          name: 'AzureWebJobsStorage'
          value: storageConnectionString
        }
        {
          name: 'FUNCTIONS_EXTENSION_VERSION'
          value: '~4'
        }
        {
          name: 'FUNCTIONS_WORKER_RUNTIME'
          value: 'python'
        }
        {
          name: 'WEBSITE_RUN_FROM_PACKAGE'
          value: '1'
        }
        {
          name: 'PYTHON_VERSION'
          value: pythonVersion
        }
        // Project-specific settings
        {
          name: '__PROJECT_ID__'
          value: projectId
        }
        {
          name: '__PROJECT_NAME__'
          value: projectName
        }
        {
          name: '__PROJECT_UPLOADS_CONTAINER__'
          value: uploadsContainerName
        }
        {
          name: '__PROJECT_INPUT_CONTAINER__'
          value: inputContainerName
        }
        {
          name: '__PROJECT_OUTPUT_CONTAINER__'
          value: outputContainerName
        }
        {
          name: '__PROJECT_SEARCH_INDEX__'
          value: searchIndexName
        }
        {
          name: '__PROJECT_INDEXER_NAME__'
          value: searchIndexerName
        }
        {
          name: '__PROJECT_DATASOURCE_NAME__'
          value: searchDatasourceName
        }
        {
          name: '__PROJECT_FUNCTION_APP_NAME__'
          value: functionAppName
        }
        {
          name: '__SHARED_STORAGE_CONNECTION_STRING__'
          value: storageConnectionString
        }
        {
          name: '__SHARED_SEARCH_SERVICE__'
          value: searchServiceName
        }
        {
          name: '__SHARED_SEARCH_KEY__'
          value: searchApiKey
        }
        {
          name: '__SHARED_OPENAI_SERVICE__'
          value: openAiServiceName
        }
        {
          name: '__SHARED_OPENAI_KEY__'
          value: openAiApiKey
        }
        {
          name: '__SHARED_OPENAI_DEPLOYMENT__'
          value: openAiModelDeployment
        }
      ]
    }
    httpsOnly: true
  }
  identity: {
    type: 'SystemAssigned'
  }
}

// Deployment script to deploy the functions
resource functionDeploymentScript 'Microsoft.Resources/deploymentScripts@2020-10-01' = {
  name: 'function-deployment-script'
  location: location
  kind: 'AzureCLI'
  properties: {
    azCliVersion: '2.40.0'
    timeout: 'PT30M'
    retentionInterval: 'P1D'
    environmentVariables: [
      {
        name: 'FUNCTION_APP_NAME'
        value: functionApp.name
      }
      {
        name: 'RESOURCE_GROUP'
        value: resourceGroup().name
      }
      {
        name: 'PROJECT_ID'
        value: projectId
      }
      {
        name: 'PROJECT_NAME'
        value: projectName
      }
      {
        name: 'STORAGE_CONNECTION_STRING'
        secureValue: storageConnectionString
      }
      {
        name: 'UPLOADS_CONTAINER'
        value: uploadsContainerName
      }
      {
        name: 'INPUT_CONTAINER'
        value: inputContainerName
      }
      {
        name: 'OUTPUT_CONTAINER'
        value: outputContainerName
      }
      {
        name: 'SEARCH_SERVICE'
        value: searchServiceName
      }
      {
        name: 'SEARCH_API_KEY'
        secureValue: searchApiKey
      }
      {
        name: 'SEARCH_INDEX'
        value: searchIndexName
      }
      {
        name: 'SEARCH_INDEXER'
        value: searchIndexerName
      }
      {
        name: 'SEARCH_DATASOURCE'
        value: searchDatasourceName
      }
    ]
    scriptContent: '''
      #!/bin/bash
      set -e

      echo "Deploying functions to Function App: $FUNCTION_APP_NAME"

      # Create a temporary directory for function deployment
      TEMP_DIR=$(mktemp -d)
      echo "Created temporary directory: $TEMP_DIR"

      # Clone the function code from the repository
      # Note: In a real scenario, you would either:
      # 1. Clone from a Git repository
      # 2. Use a package from a storage account
      # 3. Use inline function code
      # For this example, we'll create minimal function files

      # Create the function app structure
      mkdir -p $TEMP_DIR/HttpTriggerAppMaturityAssessment
      mkdir -p $TEMP_DIR/HttpTriggerAppExecutiveSummary
      mkdir -p $TEMP_DIR/EventGridTriggerBlobIndexer

      # Create function.json files
      cat > $TEMP_DIR/HttpTriggerAppMaturityAssessment/function.json << EOF
      {
        "bindings": [
          {
            "authLevel": "function",
            "type": "httpTrigger",
            "direction": "in",
            "name": "req",
            "methods": ["get", "post"]
          },
          {
            "type": "http",
            "direction": "out",
            "name": "$return"
          }
        ]
      }
      EOF

      cat > $TEMP_DIR/HttpTriggerAppExecutiveSummary/function.json << EOF
      {
        "bindings": [
          {
            "authLevel": "function",
            "type": "httpTrigger",
            "direction": "in",
            "name": "req",
            "methods": ["get", "post"]
          },
          {
            "type": "http",
            "direction": "out",
            "name": "$return"
          }
        ]
      }
      EOF

      cat > $TEMP_DIR/EventGridTriggerBlobIndexer/function.json << EOF
      {
        "bindings": [
          {
            "type": "eventGridTrigger",
            "name": "event",
            "direction": "in"
          }
        ]
      }
      EOF

      # Create __init__.py files with minimal function code
      cat > $TEMP_DIR/HttpTriggerAppMaturityAssessment/__init__.py << EOF
      import logging
      import azure.functions as func
      import json

      def main(req: func.HttpRequest) -> func.HttpResponse:
          logging.info('Python HTTP trigger function processed a request.')

          try:
              # Return a simple response for now
              return func.HttpResponse(
                  json.dumps({"status": "success", "message": "Maturity Assessment function is working", "project_id": "$PROJECT_ID"}),
                  mimetype="application/json",
                  status_code=200
              )
          except Exception as e:
              logging.error(f"Error in HttpTriggerAppMaturityAssessment: {str(e)}")
              return func.HttpResponse(
                  json.dumps({"status": "error", "message": str(e)}),
                  mimetype="application/json",
                  status_code=500
              )
      EOF

      cat > $TEMP_DIR/HttpTriggerAppExecutiveSummary/__init__.py << EOF
      import logging
      import azure.functions as func
      import json

      def main(req: func.HttpRequest) -> func.HttpResponse:
          logging.info('Python HTTP trigger function processed a request.')

          try:
              # Return a simple response for now
              return func.HttpResponse(
                  json.dumps({"status": "success", "message": "Executive Summary function is working", "project_id": "$PROJECT_ID"}),
                  mimetype="application/json",
                  status_code=200
              )
          except Exception as e:
              logging.error(f"Error in HttpTriggerAppExecutiveSummary: {str(e)}")
              return func.HttpResponse(
                  json.dumps({"status": "error", "message": str(e)}),
                  mimetype="application/json",
                  status_code=500
              )
      EOF

      cat > $TEMP_DIR/EventGridTriggerBlobIndexer/__init__.py << EOF
      import logging
      import azure.functions as func
      import json

      def main(event: func.EventGridEvent):
          logging.info('Python EventGrid trigger function processed an event')

          try:
              result = json.dumps({
                  'id': event.id,
                  'data': event.get_json(),
                  'topic': event.topic,
                  'subject': event.subject,
                  'event_type': event.event_type,
              })

              logging.info(f'Event data: {result}')
          except Exception as e:
              logging.error(f"Error in EventGridTriggerBlobIndexer: {str(e)}")
      EOF

      # Create host.json
      cat > $TEMP_DIR/host.json << EOF
      {
        "version": "2.0",
        "logging": {
          "applicationInsights": {
            "samplingSettings": {
              "isEnabled": true,
              "excludedTypes": "Request"
            }
          }
        },
        "extensionBundle": {
          "id": "Microsoft.Azure.Functions.ExtensionBundle",
          "version": "[3.*, 4.0.0)"
        }
      }
      EOF

      # Create requirements.txt
      cat > $TEMP_DIR/requirements.txt << EOF
      azure-functions==1.17.0
      azure-storage-blob==12.17.0
      azure-search-documents==11.6.0
      azure-eventgrid==4.13.0
      azure-identity==1.15.0
      azure-ai-formrecognizer==3.3.0
      openai==1.83.0
      python-dotenv==1.0.0
      EOF

      # Create local.settings.json with project-specific settings
      cat > $TEMP_DIR/local.settings.json << EOF
      {
        "IsEncrypted": false,
        "Values": {
          "FUNCTIONS_WORKER_RUNTIME": "python",
          "AzureWebJobsStorage": "$STORAGE_CONNECTION_STRING",
          "__PROJECT_ID__": "$PROJECT_ID",
          "__PROJECT_NAME__": "$PROJECT_NAME",
          "__PROJECT_UPLOADS_CONTAINER__": "$UPLOADS_CONTAINER",
          "__PROJECT_INPUT_CONTAINER__": "$INPUT_CONTAINER",
          "__PROJECT_OUTPUT_CONTAINER__": "$OUTPUT_CONTAINER",
          "__PROJECT_SEARCH_INDEX__": "$SEARCH_INDEX",
          "__PROJECT_INDEXER_NAME__": "$SEARCH_INDEXER",
          "__PROJECT_DATASOURCE_NAME__": "$SEARCH_DATASOURCE",
          "__PROJECT_FUNCTION_APP_NAME__": "$FUNCTION_APP_NAME",
          "__SHARED_STORAGE_CONNECTION_STRING__": "$STORAGE_CONNECTION_STRING",
          "__SHARED_SEARCH_SERVICE__": "$SEARCH_SERVICE",
          "__SHARED_SEARCH_KEY__": "$SEARCH_API_KEY"
        }
      }
      EOF

      # Create a zip package for deployment
      echo "Creating zip package for deployment"
      cd $TEMP_DIR
      zip -r function_app.zip .

      # Deploy using az functionapp deployment
      echo "Deploying functions to Function App: $FUNCTION_APP_NAME using az CLI"

      # First, make sure the function app is ready
      echo "Waiting for Function App to be ready..."
      MAX_RETRIES=10
      RETRY_COUNT=0
      READY=false

      while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$READY" != "true" ]; do
        STATUS=$(az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query "state" -o tsv 2>/dev/null)
        if [ "$STATUS" == "Running" ]; then
          READY=true
          echo "Function App is ready!"
        else
          echo "Function App not ready yet. Status: $STATUS. Waiting 30 seconds..."
          sleep 30
          RETRY_COUNT=$((RETRY_COUNT+1))
        fi
      done

      if [ "$READY" != "true" ]; then
        echo "Function App did not become ready in time. Proceeding with deployment anyway..."
      fi

      # Try deployment with retries
      echo "Deploying functions to Function App: $FUNCTION_APP_NAME"
      MAX_DEPLOY_RETRIES=3
      DEPLOY_RETRY_COUNT=0
      DEPLOY_SUCCESS=false

      while [ $DEPLOY_RETRY_COUNT -lt $MAX_DEPLOY_RETRIES ] && [ "$DEPLOY_SUCCESS" != "true" ]; do
        if az functionapp deployment source config-zip \
          --resource-group $RESOURCE_GROUP \
          --name $FUNCTION_APP_NAME \
          --src function_app.zip \
          --build-remote true; then
          DEPLOY_SUCCESS=true
          echo "Function deployment successful!"
        else
          echo "Deployment failed. Retrying in 30 seconds..."
          sleep 30
          DEPLOY_RETRY_COUNT=$((DEPLOY_RETRY_COUNT+1))
        fi
      done

      if [ "$DEPLOY_SUCCESS" != "true" ]; then
        echo "Function deployment failed after $MAX_DEPLOY_RETRIES attempts."
        exit 1
      fi

      # Clean up
      echo "Cleaning up temporary directory"
      rm -rf $TEMP_DIR

      echo "Function deployment completed successfully!"
    '''
  }
  // Removed unnecessary dependsOn
}

// Outputs
output functionAppName string = functionApp.name
output functionAppUrl string = 'https://${functionApp.properties.defaultHostName}'
output functionAppResourceId string = functionApp.id
