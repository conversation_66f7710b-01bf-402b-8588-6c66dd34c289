@description('Azure AI Search service name')
param searchServiceName string

@description('Location for the search service')
param location string = resourceGroup().location

@description('Search index name')
param searchIndexName string

@description('Search indexer name')
param searchIndexerName string

@description('Search datasource name')
param searchDatasourceName string

@description('SKU name for the Azure AI Search service (e.g. basic, standard)')
param searchSkuName string = 'standard'

// Removed unused parameter: storageAccountName

@description('Storage connection string')
@secure()
param storageConnectionString string

@description('Uploads container name')
param uploadsContainerName string

@description('Dimension of the vector used for the contentVector field')
param vectorDimensions int = 1536

@description('Name of the vector search profile')
param vectorProfileName string = 'default-vector-profile'

@description('Name of the HNSW algorithm configuration for vector search')
param vectorAlgorithmName string = 'my-hnsw-config-1'

@description('HNSW parameter m')
param vectorAlgorithmM int = 4

@description('HNSW efConstruction')
param vectorAlgorithmEfConstruction int = 400

@description('HNSW efSearch')
param vectorAlgorithmEfSearch int = 500

@description('HNSW distance metric')
param vectorAlgorithmMetric string = 'cosine'

@description('Resource tags')
param tags object = {}

// Azure AI Search Service
resource searchService 'Microsoft.Search/searchServices@2022-09-01' = {
  name: searchServiceName
  location: location
  tags: tags
  sku: {
    name: searchSkuName
  }
  properties: {
    replicaCount: 1
    partitionCount: 1
    hostingMode: 'default'
  }
}

// Note: The actual index, indexer, and datasource creation requires REST API calls
// Bicep doesn't directly support these resources, so we'll use a deployment script
// to create them after the search service is deployed

// Deployment script to create index, indexer, and datasource
resource searchResourcesDeploymentScript 'Microsoft.Resources/deploymentScripts@2020-10-01' = {
  name: 'search-resources-deployment-script-${uniqueString(searchService.id)}'
  location: location
  tags: tags
  kind: 'AzureCLI'
  properties: {
    azCliVersion: '2.40.0'
    timeout: 'PT30M'
    retentionInterval: 'P1D'
    forceUpdateTag: uniqueString(deployment().name) // Force the script to run every time
    environmentVariables: [
      {
        name: 'SEARCH_SERVICE_NAME'
        value: searchService.name
      }
      {
        name: 'SEARCH_API_KEY'
        value: searchService.listAdminKeys().primaryKey
      }
      {
        name: 'SEARCH_INDEX_NAME'
        value: searchIndexName
      }
      {
        name: 'SEARCH_INDEXER_NAME'
        value: searchIndexerName
      }
      {
        name: 'SEARCH_DATASOURCE_NAME'
        value: searchDatasourceName
      }
      {
        name: 'STORAGE_CONNECTION_STRING'
        secureValue: storageConnectionString
      }
      {
        name: 'STORAGE_CONTAINER_NAME'
        value: uploadsContainerName
      }
      {
        name: 'VECTOR_DIMENSION'
        value: string(vectorDimensions)
      }
      {
        name: 'VECTOR_PROFILE_NAME'
        value: vectorProfileName
      }
      {
        name: 'VECTOR_ALGORITHM_NAME'
        value: vectorAlgorithmName
      }
      {
        name: 'VECTOR_ALGORITHM_M'
        value: string(vectorAlgorithmM)
      }
      {
        name: 'VECTOR_ALGORITHM_EF_CONSTRUCTION'
        value: string(vectorAlgorithmEfConstruction)
      }
      {
        name: 'VECTOR_ALGORITHM_EF_SEARCH'
        value: string(vectorAlgorithmEfSearch)
      }
      {
        name: 'VECTOR_ALGORITHM_METRIC'
        value: vectorAlgorithmMetric
      }
    ]
    scriptContent: '''
      #!/bin/bash
      set -e

      echo "Starting search resources deployment script"
      echo "Search Service Name: $SEARCH_SERVICE_NAME"
      echo "Search Index Name: $SEARCH_INDEX_NAME"
      echo "Search Indexer Name: $SEARCH_INDEXER_NAME"
      echo "Search Datasource Name: $SEARCH_DATASOURCE_NAME"
      echo "Storage Container Name: $STORAGE_CONTAINER_NAME"
      echo "Vector Dimension: $VECTOR_DIMENSION"
      echo "Vector Profile Name: $VECTOR_PROFILE_NAME"

      # Verify search service is available
      echo "Verifying search service is available..."
      az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY" || {
          echo "Error: Cannot connect to search service $SEARCH_SERVICE_NAME"
          exit 1
        }

      # Create the datasource if it doesn't exist
      if az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/datasources/$SEARCH_DATASOURCE_NAME?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY" > /dev/null 2>&1; then
        echo "Datasource $SEARCH_DATASOURCE_NAME already exists - skipping"
      else
        echo "Creating datasource: $SEARCH_DATASOURCE_NAME"
        DATASOURCE_RESULT=$(az rest --method put \
          --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/datasources/$SEARCH_DATASOURCE_NAME?api-version=2023-11-01" \
          --headers "Content-Type=application/json" "api-key=$SEARCH_API_KEY" \
          --body "{
            \"name\": \"$SEARCH_DATASOURCE_NAME\",
            \"type\": \"azureblob\",
            \"credentials\": {
              \"connectionString\": \"$STORAGE_CONNECTION_STRING\"
            },
            \"container\": {
              \"name\": \"$STORAGE_CONTAINER_NAME\"
            }
          }")
        echo "Datasource creation result: $DATASOURCE_RESULT"
      fi

      # Create the index if it doesn't exist
      if az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes/$SEARCH_INDEX_NAME?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY" > /dev/null 2>&1; then
        echo "Index $SEARCH_INDEX_NAME already exists - skipping"
      else
        echo "Creating index: $SEARCH_INDEX_NAME"
        INDEX_RESULT=$(az rest --method put \
          --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes/$SEARCH_INDEX_NAME?api-version=2023-11-01" \
          --headers "Content-Type=application/json" "api-key=$SEARCH_API_KEY" \
          --body "{
            \"name\": \"$SEARCH_INDEX_NAME\",
          \"fields\": [
            {
              \"name\": \"id\",
              \"type\": \"Edm.String\",
              \"key\": true,
              \"searchable\": true,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": false
            },
            {
              \"name\": \"content\",
              \"type\": \"Edm.String\",
              \"searchable\": true,
              \"filterable\": false,
              \"sortable\": false,
              \"facetable\": false
            },
            {
              \"name\": \"metadata_storage_name\",
              \"type\": \"Edm.String\",
              \"searchable\": true,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": true
            },
            {
              \"name\": \"metadata_storage_path\",
              \"type\": \"Edm.String\",
              \"searchable\": false,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": false
            },
            {
              \"name\": \"metadata_content_type\",
              \"type\": \"Edm.String\",
              \"searchable\": true,
              \"filterable\": true,
              \"sortable\": true,
              \"facetable\": true
            },
            {
              \"name\": \"contentVector\",
              \"type\": \"Collection(Edm.Single)\",
              \"searchable\": true,
              \"retrievable\": true,
              \"dimensions\": $VECTOR_DIMENSION,
              \"vectorSearchProfile\": \"$VECTOR_PROFILE_NAME\"
            }
          ]
          ,\"vectorSearch\":{\"algorithms\":[{\"name\":\"$VECTOR_ALGORITHM_NAME\",\"kind\":\"hnsw\",\"hnswParameters\":{\"m\":$VECTOR_ALGORITHM_M,\"efConstruction\":$VECTOR_ALGORITHM_EF_CONSTRUCTION,\"efSearch\":$VECTOR_ALGORITHM_EF_SEARCH,\"metric\":\"$VECTOR_ALGORITHM_METRIC\"}}],\"profiles\":[{\"name\":\"$VECTOR_PROFILE_NAME\",\"algorithm\":\"$VECTOR_ALGORITHM_NAME\"}]}}")

      echo "Index creation result: $INDEX_RESULT"
      fi

      # Create the indexer if it doesn't exist
      if az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexers/$SEARCH_INDEXER_NAME?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY" > /dev/null 2>&1; then
        echo "Indexer $SEARCH_INDEXER_NAME already exists - skipping"
      else
        echo "Creating indexer: $SEARCH_INDEXER_NAME"
        INDEXER_RESULT=$(az rest --method put \
          --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexers/$SEARCH_INDEXER_NAME?api-version=2023-11-01" \
          --headers "Content-Type=application/json" "api-key=$SEARCH_API_KEY" \
          --body "{
            \"name\": \"$SEARCH_INDEXER_NAME\",
            \"dataSourceName\": \"$SEARCH_DATASOURCE_NAME\",
            \"targetIndexName\": \"$SEARCH_INDEX_NAME\",
            \"parameters\": {
              \"configuration\": {
                \"parsingMode\": \"default\",
                \"indexStorageMetadataOnlyForOversizedDocuments\": true
              }
            },
            \"schedule\": {
              \"interval\": null
            }
          }")
        echo "Indexer creation result: $INDEXER_RESULT"
      fi

      # Verify resources were created
      echo "Verifying resources were created..."

      # Check datasource
      DATASOURCE_CHECK=$(az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/datasources/$SEARCH_DATASOURCE_NAME?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY")

      if [[ "$DATASOURCE_CHECK" == *"$SEARCH_DATASOURCE_NAME"* ]]; then
        echo "✅ Datasource $SEARCH_DATASOURCE_NAME created successfully"
      else
        echo "❌ Failed to create datasource $SEARCH_DATASOURCE_NAME"
        echo "$DATASOURCE_CHECK"
      fi

      # Check index
      INDEX_CHECK=$(az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexes/$SEARCH_INDEX_NAME?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY")

      if [[ "$INDEX_CHECK" == *"$SEARCH_INDEX_NAME"* ]]; then
        echo "✅ Index $SEARCH_INDEX_NAME created successfully"
      else
        echo "❌ Failed to create index $SEARCH_INDEX_NAME"
        echo "$INDEX_CHECK"
      fi

      # Check indexer
      INDEXER_CHECK=$(az rest --method get \
        --uri "https://$SEARCH_SERVICE_NAME.search.windows.net/indexers/$SEARCH_INDEXER_NAME?api-version=2023-11-01" \
        --headers "api-key=$SEARCH_API_KEY")

      if [[ "$INDEXER_CHECK" == *"$SEARCH_INDEXER_NAME"* ]]; then
        echo "✅ Indexer $SEARCH_INDEXER_NAME created successfully"
      else
        echo "❌ Failed to create indexer $SEARCH_INDEXER_NAME"
        echo "$INDEXER_CHECK"
      fi

      echo "Search resources deployment completed"
    '''
  }
}

// Outputs
output searchServiceName string = searchService.name
output searchServiceId string = searchService.id
output searchApiKey string = searchService.listAdminKeys().primaryKey
output searchIndexName string = searchIndexName
output searchIndexerName string = searchIndexerName
output searchDatasourceName string = searchDatasourceName
