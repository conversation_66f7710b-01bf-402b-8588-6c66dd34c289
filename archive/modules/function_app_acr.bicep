@description('Name of the Azure Function App')
param functionAppName string

@description('App Service Plan name')
param appServicePlanName string

@description('Location for all resources')
param location string = resourceGroup().location

@description('Project ID')
param projectId string

@description('Project name')
param projectName string

@description('Storage connection string')
@secure()
param storageConnectionString string

@description('Project storage container for uploads')
param uploadsContainerName string

@description('Project storage container for input')
param inputContainerName string

@description('Project storage container for output')
param outputContainerName string

@description('Project search service name')
param searchServiceName string

@description('Project search API key')
@secure()
param searchApiKey string

@description('Project search index name')
param searchIndexName string

@description('Project search indexer name')
param searchIndexerName string

@description('Project search datasource name')
param searchDatasourceName string

@description('Container Registry name')
param acrName string = 'functionappaiscope'

@description('Container image name')
param containerImageName string = 'functionapp'

@description('Container image tag')
param containerImageTag string = 'latest'

@description('OpenAI service name')
param openAiServiceName string = 'openai-service'

@description('OpenAI API key')
@secure()
param openAiApiKey string = ''

@description('OpenAI model deployment name')
param openAiModelDeployment string = 'gpt-35-turbo'

@description('Resource tags')
param tags object = {}

// App Service Plan (Linux)
resource appServicePlan 'Microsoft.Web/serverfarms@2022-03-01' = {
  name: appServicePlanName
  location: location
  tags: tags
  kind: 'linux'
  sku: {
    name: 'B1' // Basic tier
    tier: 'Basic'
  }
  properties: {
    reserved: true // Required for Linux
  }
}

// Container Registry
resource acr 'Microsoft.ContainerRegistry/registries@2023-07-01' existing = {
  name: acrName
}

// Function App
resource functionApp 'Microsoft.Web/sites@2022-03-01' = {
  name: functionAppName
  location: location
  tags: tags
  kind: 'functionapp,linux,container'
  properties: {
    serverFarmId: appServicePlan.id
    siteConfig: {
      linuxFxVersion: 'DOCKER|${acrName}.azurecr.io/${containerImageName}:${containerImageTag}'
      alwaysOn: true
      appSettings: [
        {
          name: 'DOCKER_REGISTRY_SERVER_URL'
          value: 'https://${acrName}.azurecr.io'
        }
        {
          name: 'DOCKER_REGISTRY_SERVER_USERNAME'
          value: acr.listCredentials().username
        }
        {
          name: 'DOCKER_REGISTRY_SERVER_PASSWORD'
          value: acr.listCredentials().passwords[0].value
        }
        {
          name: 'AzureWebJobsStorage'
          value: storageConnectionString
        }
        {
          name: 'FUNCTIONS_EXTENSION_VERSION'
          value: '~4'
        }
        {
          name: 'FUNCTIONS_WORKER_RUNTIME'
          value: 'python'
        }
        // Project-specific settings
        {
          name: '__PROJECT_ID__'
          value: projectId
        }
        {
          name: '__PROJECT_NAME__'
          value: projectName
        }
        {
          name: '__PROJECT_UPLOADS_CONTAINER__'
          value: uploadsContainerName
        }
        {
          name: '__PROJECT_INPUT_CONTAINER__'
          value: inputContainerName
        }
        {
          name: '__PROJECT_OUTPUT_CONTAINER__'
          value: outputContainerName
        }
        {
          name: '__PROJECT_SEARCH_INDEX__'
          value: searchIndexName
        }
        {
          name: '__PROJECT_INDEXER_NAME__'
          value: searchIndexerName
        }
        {
          name: '__PROJECT_DATASOURCE_NAME__'
          value: searchDatasourceName
        }
        {
          name: '__PROJECT_FUNCTION_APP_NAME__'
          value: functionAppName
        }
        {
          name: '__SHARED_STORAGE_CONNECTION_STRING__'
          value: storageConnectionString
        }
        {
          name: '__SHARED_SEARCH_SERVICE__'
          value: searchServiceName
        }
        {
          name: '__SHARED_SEARCH_KEY__'
          value: searchApiKey
        }
        {
          name: '__SHARED_OPENAI_SERVICE__'
          value: openAiServiceName
        }
        {
          name: '__SHARED_OPENAI_KEY__'
          value: openAiApiKey
        }
        {
          name: '__SHARED_OPENAI_DEPLOYMENT__'
          value: openAiModelDeployment
        }
      ]
      cors: {
        allowedOrigins: [
          'https://ai-scope-app3.azurewebsites.net'
          'http://localhost:50505'
          'https://portal.azure.com'
        ]
        // supportCredentials: false // Default is false. Set to true if your clients send credentials (e.g., cookies, auth headers) and you need to support them.
      }
    }
    httpsOnly: true
  }
  identity: {
    type: 'SystemAssigned'
  }
}

// Outputs
output functionAppName string = functionApp.name
output functionAppUrl string = 'https://${functionApp.properties.defaultHostName}'
output functionAppResourceId string = functionApp.id
