// Main Bicep file for project resource deployment (without Function App)
// This template deploys storage, search resources, and event grid system topic
// Function App will be deployed separately using ACR-based deployment

@description('Project ID (UUID)')
param projectId string

@description('Project name')
param projectName string

@description('Region ID (UUID)')
param regionId string

@description('Location for all resources')
param location string = resourceGroup().location

@description('Function App resource ID')
param functionAppResourceId string = ''

@description('Storage account resource ID - used for event grid system topic deployment')
param storageAccountId string = ''

@description('Dimension of the vector used for the search index')
param vectorDimensions int = 1536

@description('Name of the vector search profile for the search index')
param vectorProfileName string = 'default-vector-profile'

@description('SKU name for the Azure AI Search service (e.g. standard)')
param searchSkuName string = 'standard'

@description('HNSW algorithm name for vector search')
param vectorAlgorithmName string = 'my-hnsw-config-1'

@description('HNSW parameter m')
param vectorAlgorithmM int = 4

@description('HNSW efConstruction')
param vectorAlgorithmEfConstruction int = 400

@description('HNSW efSearch')
param vectorAlgorithmEfSearch int = 500

@description('HNSW distance metric')
param vectorAlgorithmMetric string = 'cosine'

// Define resource tags
var resourceTags = {
  'project-id': projectId
  'region-id': empty(regionId) ? '********-0000-0000-0000-************' : regionId
  'project-name': projectName
}

// Log the parameters for debugging
output deploymentParameters object = {
  projectId: projectId
  projectName: projectName
  regionId: regionId
  location: location
}

// Resource group name is hardcoded in the deployment script

// Generate sanitized name for Azure resources
var sanitizedName = replace(replace(toLower(projectName), ' ', '-'), '[^a-z0-9-]', '')
var uniqueSuffix = substring(uniqueString(projectId), 0, 4)

// Storage account name needs special handling - no hyphens, max 24 chars
var storageNameBase = replace(sanitizedName, '-', '')
var storageNameTruncated = length(storageNameBase) > 16 ? substring(storageNameBase, 0, 16) : storageNameBase
var storageAccountName = 'st${storageNameTruncated}${uniqueSuffix}'
var searchServiceName = 'search-${sanitizedName}-${uniqueSuffix}'
// Only Event Grid System Topic (no regular Event Grid Topic)
var eventGridSystemTopicName = 'evgt-${sanitizedName}-${uniqueSuffix}'

// Container names - using the same naming convention as the RBAC system
var uploadsContainerName = 'uploads-${sanitizedName}-${uniqueSuffix}'
var inputContainerName = 'input-${sanitizedName}-${uniqueSuffix}'
var outputContainerName = 'output-${sanitizedName}-${uniqueSuffix}'

// Search resource names - using the same naming convention as the RBAC system
var searchIndexName = 'project-${sanitizedName}-index'
var searchIndexerName = 'project-${sanitizedName}-indexer'
var searchDatasourceName = 'project-${sanitizedName}-ds'

// Deploy storage resources
module storage './modules/storage.bicep' = {
  name: 'storage-deployment-${uniqueSuffix}'
  params: {
    storageAccountName: storageAccountName
    location: location
    uploadsContainerName: uploadsContainerName
    inputContainerName: inputContainerName
    outputContainerName: outputContainerName
    tags: resourceTags
  }
}

// Deploy search resources
module search './modules/search.bicep' = {
  name: 'search-deployment-${uniqueSuffix}'
  params: {
    searchServiceName: searchServiceName
    location: location
    searchIndexName: searchIndexName
    searchIndexerName: searchIndexerName
    searchDatasourceName: searchDatasourceName
    storageConnectionString: storage.outputs.storageConnectionString
    uploadsContainerName: uploadsContainerName
    vectorDimensions: vectorDimensions
    vectorProfileName: vectorProfileName
    searchSkuName: searchSkuName
    vectorAlgorithmName: vectorAlgorithmName
    vectorAlgorithmM: vectorAlgorithmM
    vectorAlgorithmEfConstruction: vectorAlgorithmEfConstruction
    vectorAlgorithmEfSearch: vectorAlgorithmEfSearch
    vectorAlgorithmMetric: vectorAlgorithmMetric
    tags: resourceTags
  }
}

// Event Grid System Topic deployment removed - will be deployed after Function App

// Outputs - these will be used by the ACR Function App deployment script
output storageAccountName string = storageAccountName
output storageAccountId string = storage.outputs.storageAccountId
output storageConnectionString string = storage.outputs.storageConnectionString
output uploadsContainerName string = uploadsContainerName
output inputContainerName string = inputContainerName
output outputContainerName string = outputContainerName
output searchServiceName string = searchServiceName
output searchApiKey string = search.outputs.searchApiKey
output searchIndexName string = searchIndexName
output searchIndexerName string = searchIndexerName
output searchDatasourceName string = searchDatasourceName
output resourceGroupName string = resourceGroup().name
output location string = location
output projectId string = projectId
output projectName string = projectName
output regionId string = regionId
// Event Grid outputs removed - will be populated by separate deployment
