#!/bin/bash
# debug.sh - A lightweight script for running app.py locally with minimal setup
# This script is intended for debugging purposes only and skips many of the setup steps in start.sh
#
# Usage:
#   ./debug.sh                          - Run with default settings (skips package installation)
#   INSTALL_PACKAGES=true ./debug.sh    - Install required packages
#   SKIP_AZURE_CLI=true ./debug.sh      - Skip Azure CLI credential checks
#
# You can combine options:
#   INSTALL_PACKAGES=true SKIP_AZURE_CLI=true ./debug.sh
#
# Note: For backward compatibility, SKIP_INSTALL=false will also trigger package installation

# Check Python version
PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo "Error: Python 3.8 or higher is required. Found Python $PYTHON_VERSION"
    exit 1
fi

echo "Using Python $PYTHON_VERSION"

# Set environment variables for local development
export DEPLOYMENT_ENV="development"
export DEBUG="true"  # Enable debug logging

# Check if we should skip Azure CLI credential checks
SKIP_AZURE_CLI=${SKIP_AZURE_CLI:-"false"}
if [ "$SKIP_AZURE_CLI" = "true" ]; then
    echo "Skipping Azure CLI credential checks (SKIP_AZURE_CLI=true)"
    export USE_AZURE_CLI_CREDENTIAL="false"
else
    echo "Using Azure CLI credentials for local development"
    export USE_AZURE_CLI_CREDENTIAL="true"
fi

# Load environment variables from .env.local if it exists
if [ -f .env.local ]; then
    echo "Loading environment variables from .env.local"
    while IFS='=' read -r key value || [ -n "$key" ]; do
        # Skip comments and empty lines
        if [[ $key == \#* ]] || [[ -z $key ]]; then
            continue
        fi
        # Remove quotes from value
        value=$(echo "$value" | sed 's/^"//' | sed 's/"$//')
        export "$key=$value"
    done < .env.local
fi

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating Python virtual environment '.venv'"
    python3 -m venv .venv
fi

# Check if we should install packages (default is to skip)
INSTALL_PACKAGES=${INSTALL_PACKAGES:-"false"}

# For backward compatibility, check for SKIP_INSTALL
if [ "${SKIP_INSTALL:-}" = "false" ]; then
    INSTALL_PACKAGES="true"
fi

if [ "$INSTALL_PACKAGES" = "true" ]; then
    # Install minimal Python dependencies for debugging (quietly)
    echo "Installing minimal Python dependencies for debugging..."
    ./.venv/bin/pip install --quiet quart openai azure-identity python-dotenv httpx fastapi azure-cosmos azure-storage-blob websockets starlette
else
    echo "Skipping package installation (default behavior, use INSTALL_PACKAGES=true to install)"
fi

# Check if app.py exists
if [ ! -f "app.py" ]; then
    echo "Error: app.py not found in the current directory."
    exit 1
fi

# Check if app is already running on port 50506
if lsof -i:50506 > /dev/null 2>&1; then
    echo "Warning: Port 50506 is already in use."
    read -p "Do you want to kill the process using port 50506? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Killing process on port 50506..."
        kill $(lsof -t -i:50506) 2>/dev/null || true
        sleep 1
    fi
fi

echo "Starting app.py in debug mode with console logging"
# Use the correct Quart run command
./.venv/bin/python -m quart --app "app:create_app()" run --port=50506 --host=0.0.0.0 --reload
