{"resource_group": "rg-internal-ai", "cosmos_account": "internal-ai-conversation-history-db", "cosmos_database": "db_conversation_history", "timestamp": "2025-06-03T05:34:34.964610Z", "resource_group_resources": {"accounts": [{"name": "Keyrus-OpenAI-Internal", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CognitiveServices/accounts/Keyrus-OpenAI-Internal"}, {"name": "ai-scope-openai", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CognitiveServices/accounts/ai-scope-openai"}], "searchServices": [{"name": "ai-scope-ai-search", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Search/searchServices/ai-scope-ai-search"}], "serverFarms": [{"name": "app2", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/serverFarms/app2"}, {"name": "webapp3", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/serverFarms/webapp3"}], "sites": [{"name": "ai-scope-app2", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/ai-scope-app2"}, {"name": "ai-scope-app3", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/ai-scope-app3"}, {"name": "ai-scope-app1", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/ai-scope-app1"}, {"name": "werkers-mobile", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/werkers-mobile"}], "storageAccounts": [{"name": "rgin<PERSON><PERSON><PERSON>", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Storage/storageAccounts/rginternalai"}], "systemTopics": [{"name": "LoopA", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.EventGrid/systemTopics/LoopA"}], "vaults": [{"name": "kv-rg-internal-ai", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.KeyVault/vaults/kv-rg-internal-ai"}], "workspaces": [{"name": "azureai-internal-ai", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.MachineLearningServices/workspaces/azureai-internal-ai"}]}, "cosmos_db_resources": {"searchServices": ["ai-scope-ai-search", "search-052105-qydi", "search-052106-ia2y", "search-052107-e6pk", "search-052701-7zsg", "search-052705-6m53", "search-052711-s6zk", "search-052801-7dyx", "search-053002-5brf", "search-053101-tudc", "search-testbrazil-b2mi"], "sites": ["func-0336582f-072a-46c0-a44b-d177ac2e66fc-34ae", "func-050813-fix3-test-515c", "func-2baae374-b419-4c28-bdc6-04879e40b452-813a", "func-343c7ea8-0ca5-473b-960d-4c3d74614bed-833d", "func-35a7db33-b409-4873-b3bc-54e42931bd3d-5a89", "func-585837de-b731-42b5-a8b3-8cc855fff198-3177", "func-84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53-b620", "func-a70782ac-8957-4e59-b778-b74f86549cad-8ffc", "func-bb4c80f7-aebd-4a25-94f6-a5dbe7913819-7412", "func-e5747327-8bcb-4029-8900-990205c91fbc-0dd8", "func-f529b9df-c309-40be-926d-07212d006bfd-daf1"], "storageAccounts": ["rgin<PERSON><PERSON><PERSON>", "st052105qydi", "st052106ia2y", "st052107e6pk", "st0527017zsg", "st0527056m53", "st052711s6zk", "st0528017dyx", "st0530025brf", "st053101tudc", "sttestbrazilb2mi"], "systemTopics": ["evgt-052106-8768", "evgt-052107-cd62", "evgt-052701-fcae", "evgt-052705-af36", "evgt-052711-5400", "evgt-052801-b6f9", "evgt-053002-2b96", "evgt-053101-9907", "evgt-testbrazil-cc83"]}, "untracked_resources": {"sites": [{"name": "ai-scope-app2", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/ai-scope-app2"}, {"name": "ai-scope-app3", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/ai-scope-app3"}, {"name": "ai-scope-app1", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/ai-scope-app1"}, {"name": "werkers-mobile", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/werkers-mobile"}], "systemTopics": [{"name": "LoopA", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.EventGrid/systemTopics/LoopA"}], "accounts": [{"name": "Keyrus-OpenAI-Internal", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CognitiveServices/accounts/Keyrus-OpenAI-Internal"}, {"name": "ai-scope-openai", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CognitiveServices/accounts/ai-scope-openai"}], "vaults": [{"name": "kv-rg-internal-ai", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.KeyVault/vaults/kv-rg-internal-ai"}], "workspaces": [{"name": "azureai-internal-ai", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.MachineLearningServices/workspaces/azureai-internal-ai"}], "serverFarms": [{"name": "app2", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/serverFarms/app2"}, {"name": "webapp3", "location": "francecentral", "id": "/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/serverFarms/webapp3"}]}, "summary": {"total_resources_in_resource_group": 13, "total_unique_resources_in_cosmos_db": 42, "untracked_resource_types": 6, "total_untracked_resources": 11}}