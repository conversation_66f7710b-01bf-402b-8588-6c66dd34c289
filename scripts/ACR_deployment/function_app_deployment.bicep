@description('Name of the Azure Function App')
@minLength(1)
param functionAppName string

@description('Location for all resources')
param location string = resourceGroup().location

@description('App Service Plan name')
param appServicePlanName string = 'plan-${functionAppName}'

@description('Project ID')
@minLength(1)
param projectId string

@description('Project name')
@minLength(1)
param projectName string

@description('Project storage container for uploads')
@minLength(1)
param uploadsContainer string

@description('Project storage container for input')
@minLength(1)
param inputContainer string

@description('Project storage container for output')
@minLength(1)
param outputContainer string

@description('Project search index name')
@minLength(1)
param searchIndexName string

@description('Project search indexer name')
@minLength(1)
param searchIndexerName string

@description('Project search datasource name')
@minLength(1)
param searchDatasourceName string

@description('Azure Search service name')
@minLength(1)
param searchServiceName string

@description('Azure Search API key')
@secure()
param searchApiKey string

@description('Azure OpenAI service name')
param openAiServiceName string = 'openai-service'

@description('Azure OpenAI API key')
@secure()
param openAiApiKey string = ''

@description('Azure OpenAI model deployment name')
param openAiModelDeployment string = 'gpt-35-turbo'

@description('Azure Storage connection string')
@secure()
param storageConnectionString string

@description('Container Registry name')
param acrName string = 'functionappaiscope'

@description('Container image name')
param containerImageName string = 'functionapp'

@description('Container image tag')
param containerImageTag string = 'latest'

// App Service Plan (Linux)
resource appServicePlan 'Microsoft.Web/serverfarms@2022-03-01' = {
  name: appServicePlanName
  location: location
  kind: 'linux'
  sku: {
    name: 'B1' // Basic tier
    tier: 'Basic'
  }
  properties: {
    reserved: true // Required for Linux
  }
}

// Container Registry
resource acr 'Microsoft.ContainerRegistry/registries@2023-07-01' existing = {
  name: acrName
}

// Function App
resource functionApp 'Microsoft.Web/sites@2022-03-01' = {
  name: functionAppName
  location: location
  kind: 'functionapp,linux,container'
  properties: {
    serverFarmId: appServicePlan.id
    siteConfig: {
      linuxFxVersion: 'DOCKER|${acrName}.azurecr.io/${containerImageName}:${containerImageTag}'
      alwaysOn: true
      appSettings: [
        {
          name: 'DOCKER_REGISTRY_SERVER_URL'
          value: 'https://${acrName}.azurecr.io'
        }
        {
          name: 'DOCKER_REGISTRY_SERVER_USERNAME'
          value: acr.listCredentials().username
        }
        {
          name: 'DOCKER_REGISTRY_SERVER_PASSWORD'
          value: acr.listCredentials().passwords[0].value
        }
        {
          name: 'AzureWebJobsStorage'
          value: storageConnectionString
        }
        {
          name: 'FUNCTIONS_EXTENSION_VERSION'
          value: '~4'
        }
        {
          name: 'FUNCTIONS_WORKER_RUNTIME'
          value: 'python'
        }
        {
          name: 'WEBSITES_ENABLE_APP_SERVICE_STORAGE' // Important for container apps
          value: 'false'
        }
        {
          name: 'DOCKER_CUSTOM_IMAGE_NAME' // May be redundant if linuxFxVersion is set, but often expected
          value: '${acrName}.azurecr.io/${containerImageName}:${containerImageTag}'
        }
        // Project-specific settings (double underscore versions)
        {
          name: '__PROJECT_ID__'
          value: projectId
        }
        {
          name: '__PROJECT_NAME__'
          value: projectName
        }
        {
          name: '__PROJECT_UPLOADS_CONTAINER__'
          value: uploadsContainer
        }
        {
          name: '__PROJECT_INPUT_CONTAINER__'
          value: inputContainer
        }
        {
          name: '__PROJECT_OUTPUT_CONTAINER__'
          value: outputContainer
        }
        {
          name: '__PROJECT_SEARCH_INDEX__'
          value: searchIndexName
        }
        {
          name: '__PROJECT_INDEXER_NAME__'
          value: searchIndexerName
        }
        {
          name: '__PROJECT_DATASOURCE_NAME__'
          value: searchDatasourceName
        }
        {
          name: '__PROJECT_FUNCTION_APP_NAME__'
          value: functionAppName
        }
        // Shared/Azure conventional settings (using parameters)
        {
          name: '__SHARED_STORAGE_CONNECTION_STRING__' // Also AzureWebJobsStorage
          value: storageConnectionString
        }
        {
          name: '__SHARED_SEARCH_SERVICE__' // Also SearchServiceName
          value: searchServiceName
        }
        {
          name: 'AZURE_AI_SEARCH_ENDPOINT'
          value: 'https://${searchServiceName}.search.windows.net'
        }
        {
          name: '__SHARED_SEARCH_KEY__' // Also AZURE_AI_SEARCH_API_KEY, AzureSearchApiKey
          value: searchApiKey
        }
        {
          name: '__SHARED_OPENAI_SERVICE__' // Also OPENAI_SERVICE_NAME
          value: openAiServiceName
        }
        {
          name: 'AZURE_OPENAI_ENDPOINT'
          value: 'https://${openAiServiceName}.openai.azure.com/'
        }
        {
          name: '__SHARED_OPENAI_KEY__' // Also AZURE_OPENAI_API_KEY
          value: openAiApiKey
        }
        {
          name: '__SHARED_OPENAI_DEPLOYMENT__' // Also AZURE_OPENAI_DEPLOYMENT_ID, OPENAI_MODEL_DEPLOYMENT
          value: openAiModelDeployment
        }
        // Add alternative names if application code might expect them
        {
          name: 'PROJECT_ID'
          value: projectId
        }
        {
          name: 'PROJECT_NAME'
          value: projectName
        }
        {
          name: 'UPLOADS_CONTAINER'
          value: uploadsContainer
        }
        {
          name: 'INPUT_CONTAINER'
          value: inputContainer
        }
        {
          name: 'OUTPUT_CONTAINER'
          value: outputContainer
        }
        {
          name: 'SEARCH_INDEX_NAME' // Also AZURE_AI_SEARCH_INDEX
          value: searchIndexName
        }
        {
          name: 'SEARCH_INDEXER_NAME' // Also IndexerName
          value: searchIndexerName
        }
        {
          name: 'IndexerName'
          value: searchIndexerName
        }
        {
          name: 'SEARCH_DATASOURCE_NAME'
          value: searchDatasourceName
        }
        {
          name: 'SEARCH_SERVICE_NAME' // Also SearchServiceName
          value: searchServiceName
        }
        {
          name: 'SearchServiceName'
          value: searchServiceName
        }
        {
          name: 'AZURE_AI_SEARCH_API_KEY' // Also AzureSearchApiKey
          value: searchApiKey
        }
        {
          name: 'AzureSearchApiKey'
          value: searchApiKey
        }
        {
          name: 'OPENAI_SERVICE_NAME'
          value: openAiServiceName
        }
        {
          name: 'AZURE_OPENAI_API_KEY'
          value: openAiApiKey
        }
        {
          name: 'AZURE_OPENAI_DEPLOYMENT_ID' // Also OPENAI_MODEL_DEPLOYMENT
          value: openAiModelDeployment
        }
        {
          name: 'EVENT_GRID_TOPIC_ENDPOINT'
          value: ''
        }
        {
          name: 'EVENT_GRID_TOPIC_KEY'
          value: ''
        }
        {
          name: 'AzureFunctionsJobHost__logging__logLevel__default'
          value: 'Information' // A common default
        }
      ]
    }
    httpsOnly: true
  }
  identity: {
    type: 'SystemAssigned'
  }
}

// Outputs
output functionAppName string = functionApp.name
output functionAppUrl string = 'https://${functionApp.properties.defaultHostName}'
output functionAppResourceId string = functionApp.id
