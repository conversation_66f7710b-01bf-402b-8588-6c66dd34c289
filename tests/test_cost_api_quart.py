import sys
import types
import pytest
from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

# Provide a minimal stub for the quart module so cost_api_quart can be imported
class DummyBlueprint:
    def __init__(self, *args, **kwargs):
        pass

    def before_app_serving(self, func):
        return func

    def route(self, *args, **kwargs):
        def decorator(func):
            return func

        return decorator


dummy_quart = types.SimpleNamespace(
    Blueprint=DummyBlueprint,
    jsonify=lambda x: x,
    make_response=lambda x: x,
    request=SimpleNamespace(),
    current_app=SimpleNamespace(),
)
sys.modules.setdefault("quart", dummy_quart)
sys.modules.setdefault("jwt", types.ModuleType("jwt"))
sys.modules.setdefault("requests", types.ModuleType("requests"))
azure_identity = types.ModuleType("azure.identity")
azure_identity.ClientSecretCredential = object
azure_identity.DefaultAzureCredential = object
azure_identity.AzureCliCredential = object
sys.modules.setdefault("azure.identity", azure_identity)
azure_identity_aio = types.ModuleType("azure.identity.aio")
azure_identity_aio.DefaultAzureCredential = object
azure_identity_aio.AzureCliCredential = object
sys.modules.setdefault("azure.identity.aio", azure_identity_aio)
sys.modules.setdefault("azure.storage.blob", types.ModuleType("azure.storage.blob"))
sys.modules["azure.storage.blob"].generate_account_sas = lambda **kwargs: None
sys.modules["azure.storage.blob"].ResourceTypes = object
sys.modules["azure.storage.blob"].AccountSasPermissions = object
sys.modules.setdefault("azure.cosmos.aio", types.ModuleType("azure.cosmos.aio"))
sys.modules["azure.cosmos.aio"].CosmosClient = object
sys.modules.setdefault("azure.cosmos", types.ModuleType("azure.cosmos"))
sys.modules["azure.cosmos"].exceptions = types.SimpleNamespace(
    CosmosResourceNotFoundError=Exception
)
sys.modules["azure.cosmos"].MatchConditions = object
sys.modules.setdefault("azure.core", types.ModuleType("azure.core"))
sys.modules["azure.core"].MatchConditions = object
sys.modules.setdefault("azure.mgmt.web.aio", types.ModuleType("azure.mgmt.web.aio"))
sys.modules.setdefault("azure.mgmt.storage.aio", types.ModuleType("azure.mgmt.storage.aio"))
sys.modules["azure.mgmt.web.aio"].WebSiteManagementClient = object
sys.modules["azure.mgmt.storage.aio"].StorageManagementClient = object

import importlib.util
from pathlib import Path

ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(ROOT))

COST_API_QUART_PATH = ROOT / "backend" / "cost_management" / "cost_api_quart.py"

spec = importlib.util.spec_from_file_location("cost_api_quart", COST_API_QUART_PATH)
cost_api = importlib.util.module_from_spec(spec)
try:
    spec.loader.exec_module(cost_api)
except Exception as exc:  # pragma: no cover - optional deps may be missing
    pytest.skip(f"cost_api_quart import failed: {exc}", allow_module_level=True)


class MockRequest:
    def __init__(self, headers=None, args=None):
        self.headers = headers or {}
        self.args = args or {}

    async def get_json(self):
        return {}


@pytest.mark.asyncio
async def test_overview_with_minimal_token_user():
    user = {"user_principal_id": "u1", "roles": ["SuperAdmin"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[["1", 100]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(),
        get_user=AsyncMock(return_value={"id": "u1", "role": "SuperAdmin"}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api,
        "cost_service",
        SimpleNamespace(query_cost_by_tag=AsyncMock(return_value=mock_rows)),
    ):
        result = await cost_api.get_cost_overview()
        assert result["totalCost"] == 100.0
        assert not rbac_client.get_accessible_projects.awaited


@pytest.mark.asyncio
async def test_resources_with_minimal_token_user():
    user = {"user_principal_id": "u1", "roles": ["SuperAdmin"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[["res1", 42]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(),
        get_user=AsyncMock(return_value={"id": "u1", "role": "SuperAdmin"}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api,
        "cost_service",
        SimpleNamespace(query_cost_by_resource=AsyncMock(return_value=mock_rows)),
    ):
        result = await cost_api.get_resource_costs()
        assert result["totalCost"] == 42
        assert not rbac_client.get_accessible_projects.awaited


@pytest.mark.asyncio
async def test_collect_now_endpoint():
    user = {"user_principal_id": "u1", "roles": ["SuperAdmin"]}
    request = MockRequest(headers={}, args={})
    rbac_client = SimpleNamespace(
        get_user=AsyncMock(return_value={"id": "u1", "role": "SuperAdmin"})
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "run_cost_collection_task", AsyncMock()):
        result = await cost_api.collect_cost_now()
        assert result["message"] == "Cost data collection triggered"


@pytest.mark.asyncio
async def test_role_overridden_from_db():
    token_user = {"user_principal_id": "u1", "roles": ["RegularUser"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[["1", 100]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(),
        get_user=AsyncMock(return_value={"id": "u1", "role": "SuperAdmin"}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=token_user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api,
        "cost_service",
        SimpleNamespace(query_cost_by_tag=AsyncMock(return_value=mock_rows)),
    ):
        result = await cost_api.get_cost_overview()
        assert result["totalCost"] == 100.0
        assert not rbac_client.get_accessible_projects.awaited
        assert rbac_client.get_user.awaited
