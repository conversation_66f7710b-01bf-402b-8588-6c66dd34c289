import os
import asyncio
import pytest
import importlib.util
from pathlib import Path
from unittest.mock import Mock, patch

ROOT = Path(__file__).resolve().parents[1]
CMS_PATH = ROOT / "backend" / "cost_management" / "cost_management_service.py"
CAS_PATH = ROOT / "backend" / "cost_management" / "cost_allocation_service.py"

cms_spec = importlib.util.spec_from_file_location("cost_management_service", CMS_PATH)
cms_module = importlib.util.module_from_spec(cms_spec)
cms_spec.loader.exec_module(cms_module)

cas_spec = importlib.util.spec_from_file_location("cost_allocation_service", CAS_PATH)
cas_module = importlib.util.module_from_spec(cas_spec)
cas_spec.loader.exec_module(cas_module)

CostManagementService = cms_module.CostManagementService
CostAllocationService = cas_module.CostAllocationService


def test_query_cost_by_tag():
    os.environ["AZURE_SUBSCRIPTION_ID"] = "sub"
    mock_client = Mock()
    mock_client.query.usage.return_value = {"result": 1}
    with patch.object(cms_module, "DefaultAzureCredential", return_value=Mock()), patch.object(
        cms_module, "CostManagementClient", return_value=mock_client
    ), patch.object(cms_module, "QueryDataset", lambda **kwargs: kwargs), patch.object(
        cms_module, "QueryAggregation", lambda **kwargs: kwargs
    ), patch.object(cms_module, "QueryDefinition", lambda **kwargs: Mock()):
        
        service = CostManagementService()
        result = asyncio.run(service.query_cost_by_tag("month", "env", "prod"))
        assert result == {"result": 1}
        mock_client.query.usage.assert_called_once()


def test_query_cost_by_resource():
    os.environ["AZURE_SUBSCRIPTION_ID"] = "sub"
    mock_client = Mock()
    mock_client.query.usage.return_value = {"result": 2}
    with patch.object(cms_module, "DefaultAzureCredential", return_value=Mock()), patch.object(
        cms_module, "CostManagementClient", return_value=mock_client
    ), patch.object(cms_module, "QueryDataset", lambda **kwargs: kwargs), patch.object(
        cms_module, "QueryAggregation", lambda **kwargs: kwargs
    ), patch.object(cms_module, "QueryDefinition", lambda **kwargs: Mock()):

        service = CostManagementService()
        result = asyncio.run(
            service.query_cost_by_resource(
                "month", resource_group="rg", resource_type="Storage"
            )
        )
        assert result == {"result": 2}
        mock_client.query.usage.assert_called_once()


def test_query_cost_by_dimension():
    os.environ["AZURE_SUBSCRIPTION_ID"] = "sub"
    mock_client = Mock()
    mock_client.query.usage.return_value = {"result": 3}
    with patch.object(cms_module, "DefaultAzureCredential", return_value=Mock()), patch.object(
        cms_module, "CostManagementClient", return_value=mock_client
    ), patch.object(cms_module, "QueryDataset", lambda **kwargs: kwargs), patch.object(
        cms_module, "QueryAggregation", lambda **kwargs: kwargs
    ), patch.object(cms_module, "QueryDefinition", lambda **kwargs: Mock()):

        service = CostManagementService()
        result = asyncio.run(service.query_cost_by_dimension("month", "ResourceLocation"))
        assert result == {"result": 3}
        mock_client.query.usage.assert_called_once()


def test_get_container_metrics():
    os.environ["AZURE_STORAGE_CONNECTION_STRING"] = "conn"
    mock_container = Mock()
    mock_container.name = "c1"
    mock_blob = Mock(size=1024)
    container_client = Mock()
    container_client.list_blobs.return_value = [mock_blob]
    blob_service = Mock()
    blob_service.list_containers.return_value = [mock_container]
    blob_service.get_container_client.return_value = container_client
    with patch.object(cas_module, "BlobServiceClient", Mock(from_connection_string=Mock(return_value=blob_service))):
        service = CostAllocationService()
        metrics = asyncio.run(service.get_container_metrics("account"))
        assert metrics["c1"]["blob_count"] == 1
        assert metrics["c1"]["size_gb"] > 0


def test_get_indexer_metrics():
    os.environ["AZURE_SEARCH_SERVICE_ENDPOINT"] = "https://example.search.windows.net"
    os.environ["AZURE_SEARCH_API_KEY"] = "key"
    indexer = Mock()
    indexer.name = "idx"
    status = Mock(document_count=10, storage_size=2048, execution_history=[1, 2])
    search_client = Mock()
    search_client.get_indexers.return_value = [indexer]
    search_client.get_indexer_status.return_value = status
    with patch.object(cas_module, "SearchIndexerClient", return_value=search_client), patch.object(
        cas_module, "AzureKeyCredential", return_value=Mock()
    ):
        service = CostAllocationService()
        metrics = asyncio.run(service.get_indexer_metrics("svc"))
        assert metrics["idx"]["document_count"] == 10
        assert metrics["idx"]["operation_count"] == 2


