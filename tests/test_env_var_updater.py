
import sys
import types
import logging
import asyncio
import pytest
from pathlib import Path
from types import SimpleNamespace
from unittest.mock import AsyncMock

# Stub out external dependencies so the module can be imported without Azure libs
sys.modules.setdefault("requests", types.ModuleType("requests"))

azure_storage_blob = types.ModuleType("azure.storage.blob")
azure_storage_blob.generate_account_sas = lambda **kwargs: "sas"
azure_storage_blob.ResourceTypes = object
azure_storage_blob.AccountSasPermissions = object
sys.modules["azure.storage.blob"] = azure_storage_blob
sys.modules.setdefault("azure.storage", types.ModuleType("azure.storage"))

azure_mgmt_storage_aio = types.ModuleType("azure.mgmt.storage.aio")
azure_mgmt_storage_aio.StorageManagementClient = object
sys.modules["azure.mgmt.storage.aio"] = azure_mgmt_storage_aio
sys.modules.setdefault("azure.mgmt.storage", types.ModuleType("azure.mgmt.storage"))

azure_mgmt_web_aio = types.ModuleType("azure.mgmt.web.aio")
azure_mgmt_web_aio.WebSiteManagementClient = object
sys.modules["azure.mgmt.web.aio"] = azure_mgmt_web_aio
sys.modules.setdefault("azure.mgmt.web", types.ModuleType("azure.mgmt.web"))

azure_identity = types.ModuleType("azure.identity")
azure_identity.DefaultAzureCredential = object
azure_identity.AzureCliCredential = object
sys.modules["azure.identity"] = azure_identity
azure_identity_aio = types.ModuleType("azure.identity.aio")
azure_identity_aio.DefaultAzureCredential = object
azure_identity_aio.AzureCliCredential = object
sys.modules["azure.identity.aio"] = azure_identity_aio

ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(ROOT))
import backend.utils.env_var_updater as env_module

_get_function_key = env_module._get_function_key


class BadStr:
    def __str__(self):
        raise ValueError("no str")

    default = "abc"


class BadAccess:
    def keys(self):
        return ["key1"]

    def __getattr__(self, name):
        if name == "key1":
            raise ValueError("attr error")
        raise AttributeError(name)

    def __getitem__(self, key):
        if key == "default":
            raise KeyError("no default")
        raise ValueError("getitem error")


def test_get_function_key_logs_stringify_error(caplog):
    web_client = SimpleNamespace(
        web_apps=SimpleNamespace(list_function_keys=AsyncMock(return_value=BadStr()))
    )
    with caplog.at_level(logging.DEBUG):
        key = asyncio.run(_get_function_key(web_client, "rg", "app", "func"))
    assert key == "abc"
    assert any(
        "Could not stringify function keys result" in r.message for r in caplog.records
    )


def test_get_function_key_logs_access_error(caplog):
    web_client = SimpleNamespace(
        web_apps=SimpleNamespace(list_function_keys=AsyncMock(return_value=BadAccess()))
    )
    with caplog.at_level(logging.DEBUG):
        key = asyncio.run(_get_function_key(web_client, "rg", "app", "func"))
    assert key is None
    assert any(
        "Error retrieving key" in r.message for r in caplog.records
    )

