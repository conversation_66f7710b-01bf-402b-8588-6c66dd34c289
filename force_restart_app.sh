#!/bin/bash

# Force restart script for AI Scope App
# This script deletes and recreates the Azure web app with fresh settings

set -e  # Exit on any error

# Configuration
APP_NAME="ai-scope-app3"
RESOURCE_GROUP="rg-internal-ai"
RUNTIME="PYTHON:3.10"
SKU="B3"
STARTUP_COMMAND="python3 -m hypercorn app:app --bind 0.0.0.0:8000"

echo "🚀 Starting force restart of Azure Web App: $APP_NAME"
echo "=================================================="

# Step 1: Delete the existing web app
echo "🗑️  Step 1: Deleting existing web app..."
az webapp delete --name $APP_NAME --resource-group $RESOURCE_GROUP
if [ $? -eq 0 ]; then
    echo "✅ Web app deleted successfully"
else
    echo "❌ Failed to delete web app"
    exit 1
fi

# Wait a moment for the deletion to complete
echo "⏳ Waiting 30 seconds for deletion to complete..."
sleep 30

# Step 2: Recreate the web app with fresh settings
echo "🔄 Step 2: Recreating web app with fresh settings..."
az webapp up --runtime $RUNTIME --sku $SKU --name $APP_NAME --resource-group $RESOURCE_GROUP
if [ $? -eq 0 ]; then
    echo "✅ Web app recreated successfully"
else
    echo "❌ Failed to recreate web app"
    exit 1
fi

# Step 3: Set environment variables from env.json
echo "⚙️  Step 3: Setting environment variables..."
if [ -f "env.json" ]; then
    az webapp config appsettings set -g $RESOURCE_GROUP -n $APP_NAME --settings "@env.json"
    if [ $? -eq 0 ]; then
        echo "✅ Environment variables set successfully"
    else
        echo "❌ Failed to set environment variables"
        exit 1
    fi
else
    echo "❌ env.json file not found!"
    exit 1
fi

# Step 4: Set the startup command
echo "🚀 Step 4: Setting startup command..."
az webapp config set --startup-file "$STARTUP_COMMAND" --name $APP_NAME --resource-group $RESOURCE_GROUP
if [ $? -eq 0 ]; then
    echo "✅ Startup command set successfully"
else
    echo "❌ Failed to set startup command"
    exit 1
fi

# Step 5: Restart the web app to ensure all settings take effect
echo "🔄 Step 5: Restarting web app..."
az webapp restart --name $APP_NAME --resource-group $RESOURCE_GROUP
if [ $? -eq 0 ]; then
    echo "✅ Web app restarted successfully"
else
    echo "❌ Failed to restart web app"
    exit 1
fi

echo ""
echo "🎉 Force restart completed successfully!"
echo "=================================================="
echo "App URL: https://$APP_NAME.azurewebsites.net"
echo ""
echo "📊 To monitor logs, run:"
echo "az webapp log tail --resource-group $RESOURCE_GROUP --name $APP_NAME"
echo ""
echo "🔍 To check app status, run:"
echo "az webapp show --name $APP_NAME --resource-group $RESOURCE_GROUP --query 'state'"
echo ""
echo "⏳ Note: It may take a few minutes for the app to fully start up."
