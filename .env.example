# Azure Storage Configuration
AZURE_STORAGE_CONTAINER_NAME=uploads-app3

# Entra ID Configuration for Authentication
AZURE_CLIENT_ID=your-client-id-here
AZURE_APPLICATION_ID=your-client-id-here
AZURE_TENANT_ID=your-tenant-id-here
AZURE_APP_SECRET=your-app-secret-here

# Frontend Environment Variables (prefixed with VITE_)
VITE_AZURE_CLIENT_ID=your-client-id-here
VITE_AZURE_TENANT_ID=your-tenant-id-here

# Authentication Configuration
# Set to "true" to use Entra ID authentication for local development
USE_ENTRA_AUTH=false

# Development Mode
# Set to "true" to enable development mode features
DEVELOPMENT_MODE=true

# Azure Credential Selection
# Set to "true" to use Azure CLI credentials for local development
USE_AZURE_CLI_CREDENTIAL=false

# Authentication Configuration
# For production, set this to your app URL + /logout (e.g., https://your-app.azurewebsites.net/logout)
# For development, leave empty to use provider-specific logout URLs
AUTH_LOGOUT_ENDPOINT=
