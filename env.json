[{"name": "DEVELOPMENT_MODE", "value": "false", "slotSetting": false}, {"name": "USE_ENTRA_AUTH", "value": "true", "slotSetting": false}, {"name": "USE_EASY_AUTH_ENDPOINT", "value": "true", "slotSetting": false}, {"name": "USE_AZURE_CLI_CREDENTIAL", "value": "false", "slotSetting": false}, {"name": "ALLOW_CLIENT_CREDENTIALS_FALLBACK", "value": "true", "slotSetting": false}, {"name": "RBAC_LOG_LEVEL", "value": "DEBUG", "slotSetting": false}, {"name": "SESSION_TYPE", "value": "filesystem", "slotSetting": false}, {"name": "VITE_AZURE_REDIRECT_URI", "value": "http://localhost:50505/auth/callback", "slotSetting": false}, {"name": "VITE_AZURE_PRODUCTION_REDIRECT_URI", "value": "https://ai-scope-app3.azurewebsites.net/.auth/login/aad/callback", "slotSetting": false}, {"name": "AZURE_CLIENT_ID", "value": "bb1ebfc1-47d8-4273-9206-3acc107c1e35", "slotSetting": false}, {"name": "AZURE_APPLICATION_ID", "value": "bb1ebfc1-47d8-4273-9206-3acc107c1e35", "slotSetting": false}, {"name": "AZURE_TENANT_ID", "value": "ee78877a-c63a-405d-85d6-8914358aa533", "slotSetting": false}, {"name": "MICROSOFT_PROVIDER_AUTHENTICATION_SECRET", "value": "****************************************", "slotSetting": false}, {"name": "AZURE_APP_SECRET", "value": "****************************************", "slotSetting": false}, {"name": "AZURE_CLIENT_SECRET", "value": "****************************************", "slotSetting": false}, {"name": "AZURE_SUBSCRIPTION_ID", "value": "4c1c14a3-de17-4cda-af60-01610fb493f9", "slotSetting": false}, {"name": "AZURE_RESOURCE_GROUP", "value": "rg-internal-ai", "slotSetting": false}, {"name": "AZURE_LOCATION", "value": "WestEurope", "slotSetting": false}, {"name": "VITE_AZURE_CLIENT_ID", "value": "bb1ebfc1-47d8-4273-9206-3acc107c1e35", "slotSetting": false}, {"name": "VITE_AZURE_TENANT_ID", "value": "ee78877a-c63a-405d-85d6-8914358aa533", "slotSetting": false}, {"name": "QUART_SECRET_KEY", "value": "****************************************", "slotSetting": false}, {"name": "AZURE_FUNCTION_MATURITY_ASSESSMENT_KEY", "value": "l82oi1xb6av7aEP_SEkV8mOH_lwiloe4hdvmekHDQmINAzFug1wq8g==", "slotSetting": false}, {"name": "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL", "value": "https://FunctionWebapp3.azurewebsites.net/api/HttpTriggerAppMaturityAssessment", "slotSetting": false}, {"name": "AZURE_FUNCTION_EXECUTIVE_SUMMARY_KEY", "value": "tEP22s-qj5Ps6rnUp7mRcLAKDeLwrFEhp0zG5oAsGWekAzFuiqvjJQ==", "slotSetting": false}, {"name": "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL", "value": "https://functionwebapp4.azurewebsites.net/api/execSummary?", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME", "value": "uploads-app3", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME_INPUT", "value": "maturity-input-container-app3", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_NAME_OUTPUT", "value": "maturity-output-container-app3", "slotSetting": false}, {"name": "AZURE_STORAGE_ACCOUNT_NAME", "value": "rgin<PERSON><PERSON><PERSON>", "slotSetting": false}, {"name": "AZURE_STORAGE_CONTAINER_SAS_TOKEN", "value": "?sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2026-04-01T15:59:42Z&st=2025-04-01T07:00:00Z&spr=https,http&sig=QX4cDrNGSIp3SlbEVLjwYM2ZNCC%2BF17AG8i8fCnHz3g%3D", "slotSetting": false}, {"name": "AZURE_WEBJOBS_STORAGE", "value": "DefaultEndpointsProtocol=https;AccountName=your_function_app_storage_account_name;AccountKey=your_storage_account_key==;EndpointSuffix=core.windows.net", "slotSetting": false}, {"name": "DEBUG", "value": "false", "slotSetting": false}, {"name": "AZURE_OPENAI_RESOURCE", "value": "ai-scope-openai", "slotSetting": false}, {"name": "AZURE_OPENAI_MODEL", "value": "gpt-4o-ai-scope", "slotSetting": false}, {"name": "AZURE_OPENAI_KEY", "value": "2b84bd8e5c1749789f4ba5cccb84cfdf", "slotSetting": false}, {"name": "AZURE_OPENAI_MODEL_NAME", "value": "gpt-4o", "slotSetting": false}, {"name": "AZURE_OPENAI_TEMPERATURE", "value": "0", "slotSetting": false}, {"name": "AZURE_OPENAI_TOP_P", "value": "1.0", "slotSetting": false}, {"name": "AZURE_OPENAI_MAX_TOKENS", "value": "1000", "slotSetting": false}, {"name": "AZURE_OPENAI_STOP_SEQUENCE", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_SEED", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_CHOICES_COUNT", "value": "1", "slotSetting": false}, {"name": "AZURE_OPENAI_PRESENCE_PENALTY", "value": "0.0", "slotSetting": false}, {"name": "AZURE_OPENAI_FREQUENCY_PENALTY", "value": "0.0", "slotSetting": false}, {"name": "AZURE_OPENAI_LOGIT_BIAS", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_USER", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_TOOLS", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_TOOL_CHOICE", "value": "", "slotSetting": false}, {"name": "AZURE_OPENAI_SYSTEM_MESSAGE", "value": "You are an AI IT sales asssistant, helping keyrus analyse companies", "slotSetting": false}, {"name": "AZURE_OPENAI_PREVIEW_API_VERSION", "value": "2024-05-01-preview", "slotSetting": false}, {"name": "AZURE_OPENAI_STREAM", "value": "True", "slotSetting": false}, {"name": "AZURE_OPENAI_ENDPOINT", "value": "https://ai-scope-openai.openai.azure.com/", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_NAME", "value": "text-embedding-ada-002-ai-scope", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_ENDPOINT", "value": "https://ai-scope-openai.openai.azure.com/openai/deployments/text-embedding-ada-002-ai-scope/embeddings?api-version=2023-05-15", "slotSetting": false}, {"name": "AZURE_OPENAI_EMBEDDING_KEY", "value": "2b84bd8e5c1749789f4ba5cccb84cfdf", "slotSetting": false}, {"name": "UI_TITLE", "value": "AI scoping assistant", "slotSetting": false}, {"name": "UI_LOGO", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "UI_CHAT_LOGO", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "UI_CHAT_TITLE", "value": "Explore Your Data", "slotSetting": false}, {"name": "UI_CHAT_DESCRIPTION", "value": "", "slotSetting": false}, {"name": "UI_FAVICON", "value": "https://images.ctfassets.net/te2janzw7nut/1k38fRLGCdPTDBILanEe0k/3da972954bd6a651b19d0af1b4bc758b/logo-keyrus.svg", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ACCOUNT", "value": "internal-ai-conversation-history-db", "slotSetting": false}, {"name": "AZURE_COSMOSDB_DATABASE", "value": "db_conversation_history", "slotSetting": false}, {"name": "AZURE_COSMOSDB_CONVERSATIONS_CONTAINER", "value": "conversations", "slotSetting": false}, {"name": "AZURE_COSMOSDB_PROJECTS_CONTAINER", "value": "projects", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ACCOUNT_KEY", "value": "pNgnkPf6Sr24BPimwFURmrcPX7dWDlbagPXbdgfvVyv0ms7fFyK3FcLgIhVDa07pslzHK3MBEoHEACDbfEEfNg==", "slotSetting": false}, {"name": "AZURE_COSMOSDB_ENABLE_FEEDBACK", "value": "false", "slotSetting": false}, {"name": "DATASOURCE_TYPE", "value": "AzureCognitiveSearch", "slotSetting": false}, {"name": "SEARCH_TOP_K", "value": "5", "slotSetting": false}, {"name": "SEARCH_STRICTNESS", "value": "3", "slotSetting": false}, {"name": "SEARCH_ENABLE_IN_DOMAIN", "value": "True", "slotSetting": false}, {"name": "AZURE_SEARCH_API_VERSION", "value": "2021-04-30-Preview", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEXER", "value": "ai-scope-app3-indexer", "slotSetting": false}, {"name": "AZURE_SEARCH_SERVICE", "value": "ai-scope-ai-search", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEX", "value": "ai-scope-app3", "slotSetting": false}, {"name": "AZURE_SEARCH_KEY", "value": "****************************************************", "slotSetting": false}, {"name": "AZURE_SEARCH_USE_SEMANTIC_SEARCH", "value": "True", "slotSetting": false}, {"name": "AZURE_SEARCH_SEMANTIC_SEARCH_CONFIG", "value": "ai-scope-app3-semantic-configuration", "slotSetting": false}, {"name": "AZURE_SEARCH_INDEX_IS_PRECHUNKED", "value": "false", "slotSetting": false}, {"name": "AZURE_SEARCH_TOP_K", "value": "5", "slotSetting": false}, {"name": "AZURE_SEARCH_ENABLE_IN_DOMAIN", "value": "false", "slotSetting": false}, {"name": "AZURE_SEARCH_CONTENT_COLUMNS", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_FILENAME_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_TITLE_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_URL_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_VECTOR_COLUMNS", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_QUERY_TYPE", "value": "vectorSemanticHybrid", "slotSetting": false}, {"name": "AZURE_SEARCH_PERMITTED_GROUPS_COLUMN", "value": "", "slotSetting": false}, {"name": "AZURE_SEARCH_STRICTNESS", "value": "3", "slotSetting": false}, {"name": "USE_PROMPTFLOW", "value": "false", "slotSetting": false}, {"name": "PROMPTFLOW_ENDPOINT", "value": "", "slotSetting": false}, {"name": "PROMPTFLOW_API_KEY", "value": "", "slotSetting": false}, {"name": "PROMPTFLOW_RESPONSE_TIMEOUT", "value": "120", "slotSetting": false}, {"name": "PROMPTFLOW_REQUEST_FIELD_NAME", "value": "query", "slotSetting": false}, {"name": "PROMPTFLOW_RESPONSE_FIELD_NAME", "value": "reply", "slotSetting": false}, {"name": "PROMPTFLOW_CITATIONS_FIELD_NAME", "value": "documents", "slotSetting": false}, {"name": "AUTH_LOGOUT_ENDPOINT", "value": "https://ai-scope-app3.azurewebsites.net/logout", "slotSetting": false}, {"name": "ALLOWED_ORIGINS", "value": "http://localhost:50505,http://127.0.0.1:50505,http://localhost:50506,http://127.0.0.1:50506,https://ai-scope-app3.azurewebsites.net,https://ai-scope-app1.azurewebsites.net", "slotSetting": false}, {"name": "API_PORT", "value": "8000", "slotSetting": false}]