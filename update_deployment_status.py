#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the deployment status in CosmosDB for a specific project.
"""

import os
import sys
import json
import logging
import asyncio
from datetime import datetime, timezone
from azure.cosmos import CosmosClient, exceptions
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def update_deployment_status(project_id, status="completed", message=None):
    """Update the deployment status in CosmosDB for a specific project."""
    # Load environment variables
    load_dotenv()

    # Get Cosmos DB settings from environment variables
    cosmos_account = os.getenv("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.getenv("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.getenv("AZURE_COSMOSDB_DATABASE")
    cosmos_container = os.getenv("AZURE_COSMOSDB_PROJECTS_CONTAINER")

    # Check if required environment variables are set
    if not cosmos_account:
        logger.error("AZURE_COSMOSDB_ACCOUNT environment variable not set")
        return False

    if not cosmos_database:
        logger.error("AZURE_COSMOSDB_DATABASE environment variable not set")
        return False

    if not cosmos_container:
        logger.error("AZURE_COSMOSDB_PROJECTS_CONTAINER environment variable not set")
        return False

    # Construct the Cosmos DB endpoint
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
    logger.info(f"Connecting to Cosmos DB endpoint: {cosmos_endpoint}")

    try:
        # Determine authentication method
        if cosmos_key:
            logger.info("Using account key authentication")
            credential = cosmos_key
        else:
            logger.info("Using Azure identity authentication")
            credential = DefaultAzureCredential()

        # Create the Cosmos DB client
        client = CosmosClient(cosmos_endpoint, credential=credential)

        # Get database client
        database_client = client.get_database_client(cosmos_database)

        # Get container client
        container_client = database_client.get_container_client(cosmos_container)

        # Get the project document
        try:
            # First try to get the project using the project_id as the partition key
            logger.info(f"Trying to get project {project_id} using project_id as partition key")
            project = container_client.read_item(item=project_id, partition_key=project_id)
        except exceptions.CosmosHttpResponseError:
            # If that fails, try to query for the project across all partition keys
            logger.info(f"Trying to get project {project_id} using query")
            query = f"SELECT * FROM c WHERE c.id = '{project_id}'"
            items = list(container_client.query_items(
                query=query,
                enable_cross_partition_query=True
            ))

            if not items:
                logger.error(f"Project {project_id} not found")
                return False

            project = items[0]

        # Get the partition key value
        partition_key = project.get("region", project.get("id"))
        logger.info(f"Using partition key: {partition_key}")

        # Create the deployment status update
        timestamp = datetime.now(timezone.utc).isoformat()

        # Check if the project has deployment_status or deployment field
        if "deployment_status" in project:
            field_name = "deployment_status"
        else:
            field_name = "deployment"

        logger.info(f"Using field name: {field_name}")

        # Get the current deployment status
        deployment_status = project.get(field_name, {})

        # Update the deployment status
        deployment_status.update({
            "status": status,
            "message": message or f"Deployment {status}",
            "updated_at": timestamp,
            "details": {
                "storage": {
                    "storage_account": True,
                    "containers": {
                        "uploads": True,
                        "input": True,
                        "output": True
                    }
                },
                "storage_complete": True,
                "search": {
                    "search_service": True,
                    "index": True,
                    "indexer": True,
                    "datasource": True
                },
                "search_complete": True,
                "function": {
                    "function_app": True,
                    "event_grid_topic": True,
                    "event_grid_system_topic": True,
                    "event_grid": True,
                    "maturity_assessment": True,
                    "executive_summary": True
                },
                "function_complete": True,
                "overall_complete": status == "completed",
                "completion_percentage": 100 if status == "completed" else 50
            }
        })

        # Update the project document
        project[field_name] = deployment_status

        # Update the project in CosmosDB
        try:
            # The replace_item method in the Azure Cosmos DB Python SDK has different parameter names
            # depending on the version. Let's try both approaches.
            try:
                # First try with partition_key parameter
                result = container_client.replace_item(
                    item=project_id,
                    body=project,
                    partition_key=partition_key
                )
            except TypeError:
                # If that fails, try without partition_key parameter
                logger.info("Retrying without partition_key parameter")
                result = container_client.replace_item(
                    item=project_id,
                    body=project
                )

            logger.info(f"Successfully updated deployment status for project {project_id}")
            logger.info(f"Updated document: {json.dumps(result.get(field_name), indent=2)}")
            return True
        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Failed to update project: {e}")
            return False

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        logger.error("Usage: python update_deployment_status.py <project_id> [status] [message]")
        sys.exit(1)

    project_id = sys.argv[1]
    status = sys.argv[2] if len(sys.argv) > 2 else "completed"
    message = sys.argv[3] if len(sys.argv) > 3 else None

    result = asyncio.run(update_deployment_status(project_id, status, message))
    sys.exit(0 if result else 1)
