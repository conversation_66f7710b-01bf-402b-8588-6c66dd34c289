# app.py
import copy
import json
import os
import logging
import uuid
import httpx
import asyncio
import mimetypes  # Added for content type guessing
from dotenv import load_dotenv
from datetime import datetime, timezone, timedelta  # Added
import re  # Added
import threading  # Added for background function deployment
import hashlib  # Added for ETag generation
import base64  # Added for debug_roles
import importlib  # Added for dynamic imports
import msal  # For Entra ID authentication

# Apply Quart logging patch before importing Quart
from backend.utils.quart_logging_patch import (
    patch_quart_logging,
    patch_hypercorn_logging,
)

patch_quart_logging()
patch_hypercorn_logging()
load_dotenv()
from quart import (
    Blueprint,
    Quart,
    jsonify,
    make_response,
    request,
    send_from_directory,
    render_template,
    current_app,
    url_for,
    session,
)

from openai import AsyncAzureOpenAI
from azure.identity.aio import DefaultAzureCredential, get_bearer_token_provider

# Added for storage management (container creation)
from azure.mgmt.storage.aio import StorageManagementClient
from azure.mgmt.storage.models import (
    BlobContainer,
)  # Added for container creation params

# Added for blob data operations (upload)
from azure.storage.blob.aio import BlobServiceClient
from azure.core.exceptions import ResourceExistsError, ResourceNotFoundError
from azure.cosmos import exceptions as cosmos_exceptions  # Added for ETag handling
from azure.core.credentials import AzureKeyCredential  # Added for Admin Key Auth

from backend.auth.auth_utils import get_authenticated_user_details, auth_bp
from backend.security.ms_defender_utils import get_msdefender_user_json
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.settings import (
    app_settings,
    MINIMUM_SUPPORTED_AZURE_OPENAI_PREVIEW_API_VERSION,
)
from backend.cache import cache_manager
from backend.utils import (
    format_as_ndjson,
    format_stream_response,
    format_non_streaming_response,
    convert_to_pf_format,
    format_pf_non_streaming_response,
)

# Import PriorityPlot routes
from backend.priorityplot.routes import priorityplot_bp
# Import Cost Management routes
from backend.cost_management.cost_api_quart import cost_bp
# Import search management functions
from backend.aisearch.search_manager import (
    create_project_datasource,
    create_project_index,
    create_project_indexer,
    delete_search_resources,
)

# Import cleanup helper
from backend.utils.project_cleanup import cleanup_project_resources

# Import Azure Web App Management Client for Function App deletion
from azure.mgmt.web.aio import WebSiteManagementClient

# Import Event Grid Management Client for Event Grid Topic creation
try:
    from azure.mgmt.eventgrid.aio import EventGridManagementClient
except ImportError:
    # Fall back to non-async version if async version is not available
    from azure.mgmt.eventgrid import EventGridManagementClient
# Import search SDK clients
from azure.search.documents.indexes.aio import SearchIndexClient, SearchIndexerClient
from azure.search.documents.aio import SearchClient
from azure.search.documents import SearchClient as SyncSearchClient
from backend.aisearch.ai_search_status import (
    AzureAISearchService,
)  # Keep if used elsewhere
import secrets  # For generating keys


# Import the WebSocket FastAPI app and the trigger function
from backend.web_sockets.index_blob_status import app as websocket_app
from backend.web_sockets.index_blob_status import (
    initialize_websocket_service,
    shutdown_websocket_service,
    trigger_project_update,
)

# Import the resource deployment function
from deploy_project_resources import deploy_project_resources

# Import RBAC routes and FastAPI
from backend.rbac.rbac_routes import router as rbac_router, lifespan as rbac_lifespan
from backend.rbac.user_context import router as user_context_router
from fastapi import FastAPI

# Import the deployment blueprint
from backend.deployments import deployment_bp

# Import the projects blueprint
from backend.routes.projects import projects_bp

# Import the deployment status module for background checking
try:
    from backend.deployments.deployment_status import start_background_checker

    deployment_status_available = True
except ImportError:
    logging.warning(
        "backend.deployments.deployment_status module not fully available. Background status checker will not be started."
    )
    deployment_status_available = False

# Create a separate FastAPI app for RBAC
rbac_app = FastAPI(lifespan=rbac_lifespan)
rbac_app.include_router(rbac_router)
rbac_app.include_router(user_context_router)

bp = Blueprint("routes", __name__, static_folder="static", template_folder="static")

cosmos_db_ready = asyncio.Event()


# --- Helper function to sanitize names for Azure resources ---
def sanitize_for_azure(name: str) -> str:
    """Sanitizes a name for Azure resources like containers."""
    # Lowercase, remove non-alphanumeric, replace spaces/underscores with hyphens
    sanitized = name.lower()
    sanitized = re.sub(r"[^a-z0-9-]", "", sanitized)
    sanitized = re.sub(r"[_ ]", "-", sanitized)
    # Ensure it starts/ends with alphanumeric
    sanitized = sanitized.strip("-")
    # Enforce length constraints (e.g., 3-63 for containers)
    if len(sanitized) < 3:
        sanitized = f"proj-{sanitized}"  # Add prefix if too short
    return sanitized[:63]


# --- Azure Storage Client Initialization ---
async def init_storage_client():
    """Initializes the Azure Storage Management Client."""
    subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")  # Get from environment
    if not subscription_id:
        logging.error(
            "AZURE_SUBSCRIPTION_ID environment variable not set. StorageManagementClient initialization failed."
        )
        return None
    logging.info(
        f"Found AZURE_SUBSCRIPTION_ID: {subscription_id[:4]}...{subscription_id[-4:]}"
    )  # Log partial ID for confirmation

    mgmt_credential = None
    try:
        logging.info("Attempting to obtain Azure credential for management clients...")

        # For local development, we'll skip Azure authentication
        if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
            logging.info("Running in development mode, skipping Azure credential check")

            class DummyCredential:
                async def get_token(self, *args, **kwargs):
                    return {"token": "dummy_token"}

                async def __aenter__(self):
                    return self

                async def __aexit__(self, exc_type, exc_val, exc_tb):
                    pass

            mgmt_credential = DummyCredential()
            logging.info("Using DummyCredential for local development")
        else:
            from backend.utils.azure_credentials import get_management_credential_async

            mgmt_credential = get_management_credential_async()

            # Test credential validity
            try:
                async with mgmt_credential:
                    await mgmt_credential.get_token("https://management.azure.com/.default")
                logging.info("Successfully obtained token using Azure credentials.")
            except Exception as cred_e:
                logging.error(
                    f"Failed to obtain token using Azure credentials: {cred_e}. Check Azure login and permissions."
                )
                return None

        logging.info(
            "DefaultAzureCredential obtained. Initializing StorageManagementClient..."
        )
        storage_client = StorageManagementClient(
            credential=mgmt_credential, subscription_id=subscription_id
        )
        logging.info("Storage Management Client initialized successfully.")
        # Note: We don't close the credential here as it's managed externally or by context

        # SAS token check is separate, for BlobServiceClient used elsewhere (e.g., uploads)
        sas_token = os.getenv("AZURE_STORAGE_CONTAINER_SAS_TOKEN")
        # This check is not directly related to StorageManagementClient init, but good practice
        if not sas_token:
            logging.warning(
                "AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail."
            )
            # Don't raise ValueError here, as StorageManagementClient might still be needed

        return storage_client  # Return the initialized client

    except Exception as e:
        logging.error(
            f"Failed during Storage Management Client initialization or credential check: {e}"
        )
        # Attempt to close credential if it was obtained before the error
        # Note: DefaultAzureCredential doesn't have an explicit close method in newer azure-identity versions
        # if mgmt_credential and hasattr(mgmt_credential, "close"):
        #      await mgmt_credential.close() # Close if possible
        return None  # Return None to indicate failure



def create_app():
    app = Quart(__name__)

    # Configure session management
    app.secret_key = os.getenv("QUART_SECRET_KEY", str(uuid.uuid4()))
    app.config["SESSION_TYPE"] = "null"  # Use null session for local development
    app.config["SESSION_PERMANENT"] = True
    app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(days=7)
    logging.info("Using null session backend for local development")

    app.register_blueprint(bp)
    app.config["TEMPLATES_AUTO_RELOAD"] = True
    app.config["PROVIDE_AUTOMATIC_OPTIONS"] = True

    # Set up routing for FastAPI (WebSockets) and Quart
    @app.before_serving
    async def init():
        try:
            # Initialize CosmosDB client (now using CosmosRbacClient for all operations)
            if not hasattr(app, 'cosmos_conversation_client'):
                app.cosmos_conversation_client = await init_cosmosdb_client()
                cosmos_db_ready.set()

            # RBAC is now handled by a separate FastAPI app (rbac_app)

            # Initialize Azure OpenAI client
            try:
                app.azure_openai_client = await init_openai_client()
                logging.info("Azure OpenAI client initialized")
            except Exception as e:
                logging.error(f"Failed to initialize Azure OpenAI client: {e}")
                app.azure_openai_client = None  # Ensure it's None if init fails

            # Initialize Azure Storage client
            try:
                # This client uses DefaultAzureCredential internally via init_storage_client
                app.storage_management_client = await init_storage_client()
            except Exception as e:
                logging.error(f"Failed to initialize Storage Management client: {e}")
                app.storage_management_client = None

            # Initialize WebSocket service if settings are available
            if hasattr(app_settings, "azure_storage") and hasattr(
                app_settings, "azure_search"
            ):
                try:
                    # Initialize the WebSocket service for blob and index updates
                    await initialize_websocket_service(
                        storage_account=app_settings.azure_storage.account_name,
                        container_name=app_settings.azure_storage.container_name,
                        container_name_input=app_settings.azure_storage.container_name_input,
                        container_name_output=app_settings.azure_storage.container_name_output,
                        sas_token=app_settings.azure_storage.container_sas_token,
                        search_service=(
                            app_settings.azure_search.search_service_name
                            if hasattr(app_settings, "azure_search")
                            and app_settings.azure_search.search_service_name
                            else os.environ.get("AZURE_SEARCH_SERVICE", "")
                        ),
                        index_name=(
                            app_settings.azure_search.index_name
                            if hasattr(app_settings, "azure_search")
                            and app_settings.azure_search.index_name
                            else os.environ.get("AZURE_SEARCH_INDEX", "")
                        ),
                        indexer_name=(
                            app_settings.azure_search.indexer_name
                            if hasattr(app_settings, "azure_search")
                            and app_settings.azure_search.indexer_name
                            else os.environ.get("AZURE_SEARCH_INDEXER", "")
                        ),
                        search_api_key=app_settings.azure_search.key,
                        cosmos_db_client=app.cosmos_conversation_client,  # Pass the initialized CosmosRbacClient
                    )
                    logging.info("Blob and index WebSocket service initialized")
                except Exception as e:
                    logging.error(f"Failed to initialize WebSocket service: {e}")
            else:
                logging.warning(
                    "Azure Storage or Search config missing, WebSocket service not initialized"
                )

            # Start the background deployment status checker if available
            if deployment_status_available:
                try:
                    # Get the API URL for status updates
                    api_url = f"http://localhost:{os.environ.get('API_PORT', '50505')}"
                    logging.info(
                        f"Starting background deployment status checker with API URL: {api_url}"
                    )
                    app.background_status_checker = start_background_checker(
                        api_url=api_url
                    )
                    logging.info(
                        "Background deployment status checker started successfully"
                    )
                except Exception as e:
                    logging.error(
                        f"Failed to start background deployment status checker: {e}"
                    )

        except Exception as e:
            logging.exception("Failed during initial service initialization")
            app.cosmos_conversation_client = None
            app.azure_openai_client = None
            app.storage_management_client = None
            # Decide if you want to raise e or allow the app to start partially
            # raise e

    # Clean up tasks on shutdown
    @app.after_serving
    async def cleanup():
        logging.info("Initiating resource cleanup on shutdown")
        cleanup_tasks = []

        # Shutdown the WebSocket service
        logging.info("Shutting down WebSocket service")
        cleanup_tasks.append(
            shutdown_websocket_service()
        )  # This is async, will be awaited by gather

        # Close Cosmos client
        if (
            hasattr(current_app, "cosmos_conversation_client")
            and current_app.cosmos_conversation_client
        ):
            logging.info("Closing Cosmos DB client")
            cleanup_tasks.append(
                current_app.cosmos_conversation_client.close()
            )  # This is async, will be awaited by gather

        # Close Azure OpenAI client
        if (
            hasattr(current_app, "azure_openai_client")
            and current_app.azure_openai_client
        ):
            logging.info("Closing Azure OpenAI client")
            cleanup_tasks.append(
                current_app.azure_openai_client.close()
            )  # Use close() for openai>=1.0.0, will be awaited by gather

        # Close Storage Management client
        if (
            hasattr(current_app, "storage_management_client")
            and current_app.storage_management_client
        ):
            logging.info("Closing Storage Management client")
            # Use await close() for async client
            if hasattr(
                current_app.storage_management_client, "close"
            ) and asyncio.iscoroutinefunction(
                current_app.storage_management_client.close
            ):
                cleanup_tasks.append(current_app.storage_management_client.close())

        # RBAC client is managed by the rbac_app lifespan context

        # Cancel the background status checker task if it exists
        if (
            hasattr(current_app, "background_status_checker")
            and current_app.background_status_checker
        ):
            logging.info("Cancelling background deployment status checker")
            current_app.background_status_checker.cancel()
            cleanup_tasks.append(asyncio.sleep(0.1))  # Give it a moment to clean up

        # Gather async cleanup tasks
        logging.info(f"Gathering {len(cleanup_tasks)} cleanup tasks.")
        results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        for result in results:
            if isinstance(result, Exception):
                logging.error(f"Cleanup error: {str(result)}")

        # Allow pending closes to complete
        await asyncio.sleep(0.250)
        logging.info("Cleanup finished.")

    # Register the auth blueprint
    app.register_blueprint(auth_bp)

    # Register the PriorityPlot blueprint
    app.register_blueprint(priorityplot_bp)
    
    # Register the Cost Management blueprint
    app.register_blueprint(cost_bp)

    # Register the Deployment blueprint
    app.register_blueprint(deployment_bp)

    # Register the Projects blueprint
    app.register_blueprint(projects_bp)

    # Integrate FastAPI with Quart using an ASGI wrapper
    original_asgi_app = app.asgi_app

    async def handle_request(scope, receive, send):
        # Route WebSocket connections to FastAPI
        if scope["type"] == "websocket":
            path = scope.get("path", "")
            logging.debug(f"Routing WebSocket request to FastAPI: {path}")
            return await websocket_app(scope, receive, send)

        # Route RBAC and User Context API endpoints to the RBAC FastAPI app
        if scope["type"] == "http" and (scope["path"].startswith("/api/rbac") or scope["path"].startswith("/api/user-context")):
            logging.debug(
                f"Routing HTTP request to RBAC FastAPI app: {scope['path']}"
            )
            return await rbac_app(scope, receive, send)

        # Route deployment API endpoints to Quart app (handled by deployment_bp)
        if scope["type"] == "http" and scope["path"].startswith(
            "/api/system/deployments"
        ):
            logging.debug(
                f"Routing deployment HTTP request to Quart app: {scope['path']}"
            )
            return await original_asgi_app(scope, receive, send)

        # Route project deployment status API endpoints to Quart app (handled by projects_bp)
        if scope["type"] == "http" and (
            scope["path"].startswith("/api/projects/")
            and (
                scope["path"].endswith("/deployment-status")
                or scope["path"].endswith("/deploy")
            )
        ):
            logging.debug(
                f"Routing project deployment HTTP request to Quart app: {scope['path']}"
            )
            return await original_asgi_app(scope, receive, send)

        # Route API endpoints for WebSocket configuration to WebSocket FastAPI app
        if scope["type"] == "http" and (
            scope["path"].startswith("/api/configure")
            or scope["path"].startswith("/api/refresh")
            or scope["path"].startswith("/api/status")
        ):
            logging.debug(
                f"Routing HTTP request to WebSocket FastAPI app: {scope['path']}"
            )
            return await websocket_app(scope, receive, send)

        # Default to normal Quart handling for everything else
        return await original_asgi_app(scope, receive, send)

    # Replace the ASGI app with our custom router
    app.asgi_app = handle_request

    return app


@bp.route("/")
async def index():
    return await render_template(
        "index.html", title=app_settings.ui.title, favicon=app_settings.ui.favicon
    )


@bp.route("/projects")
async def projects_page():
    return await render_template(
        "index.html", title=app_settings.ui.title, favicon=app_settings.ui.favicon
    )


@bp.route("/new-project")
async def new_project_page():
    return await render_template(
        "index.html", title=app_settings.ui.title, favicon=app_settings.ui.favicon
    )


@bp.route("/project/<project_id>")
async def project_page(project_id):
    return await render_template(
        "index.html", title=app_settings.ui.title, favicon=app_settings.ui.favicon
    )

@bp.route("/.auth/login/aad/callback")
async def auth_callback():
    """
    Handles the Entra ID authentication callback.
    In production, redirects directly to the projects page.
    """
    logging.info("Entra ID callback: Redirecting directly to projects page")

    # Check if we're in development mode
    if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
        logging.info("Development mode: Serving index.html for SPA to handle MSAL redirect")
        return await render_template(
            "index.html",
            title=app_settings.ui.title,
            favicon=app_settings.ui.favicon
        )

    # In production, redirect directly to projects page
    return await make_response('', 302, {'Location': '/#/projects'})

@bp.route("/auth/callback")
async def auth_callback_direct():
    """
    Handles the direct /auth/callback route used by MSAL.js.
    This is the route configured in the frontend MSAL configuration.
    In production, redirects directly to the projects page.
    """
    logging.info("Direct auth callback: Processing authentication callback")

    # Check if we're in development mode
    if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
        logging.info("Development mode: Serving index.html for SPA to handle MSAL redirect")
        return await render_template(
            "index.html",
            title=app_settings.ui.title,
            favicon=app_settings.ui.favicon
        )

    # In production, redirect directly to projects page
    return await make_response('', 302, {'Location': '/#/projects'})

# The /api/auth/exchange-code endpoint was removed as MSAL.js now handles code exchange.

@auth_bp.route("/session-login", methods=["POST"])
async def session_login():
    """
    Establishes a server-side session after frontend MSAL.js has obtained an ID token.
    Validates the ID token and stores user claims in the session.
    """
    try:
        data = await request.get_json()
        id_token_str = data.get("id_token")

        if not id_token_str:
            return jsonify({"error": "ID token is missing"}), 400

        # --- ID Token Validation ---
        # In a real application, this needs to be a robust validation:
        # 1. Decode without verification to get header (kid).
        # 2. Fetch JWKS from Entra ID's .well-known/openid-configuration endpoint.
        # 3. Find the matching key using 'kid'.
        # 4. Verify token signature, audience (client_id), issuer (tenant_id), expiry, nonce (if used).
        # MSAL Python's 'msal.oauth2cli.oidc.decode_id_token' can do some of this,
        # but it might expect a full MSAL flow context.
        # For now, we'll do a conceptual validation and extract claims.

        # Conceptual: Decode and extract claims (replace with actual validation)
        # For demonstration, we'll assume the token is valid if it can be decoded (highly insecure for production)
        try:
            # This is NOT proper validation, just decoding for claims.
            # You would use a library like python-jose and fetch keys for real validation.
            unverified_claims = msal.oauth2cli.oidc.decode_id_token(id_token_str) # MSAL can decode
            if not unverified_claims: # Or if validation fails
                 raise ValueError("ID token could not be decoded or is invalid.")
            logging.info(f"ID token received for user: {unverified_claims.get('preferred_username')}, OID: {unverified_claims.get('oid')}")

        except Exception as e:
            logging.error(f"ID token processing error: {e}")
            return jsonify({"error": "Invalid ID token", "details": str(e)}), 401

        # Store user information in session
        session['user'] = {
            'id_token_claims': unverified_claims, # Store all claims
            'access_token': None, # Access token for Graph would be acquired by frontend MSAL.js if needed by backend later
            'name': unverified_claims.get('name'),
            'email': unverified_claims.get('preferred_username'),
            'oid': unverified_claims.get('oid')
        }
        session.permanent = True

        logging.info(f"Server session created for user {session['user'].get('email')}. OID: {session['user'].get('oid')}")
        return jsonify({"status": "success", "message": "Session established"})

    except Exception as e:
        logging.exception("Exception in /api/auth/session-login")
        return jsonify({"error": str(e)}), 500


@bp.route("/favicon.ico")
async def favicon():
    return await bp.send_static_file("favicon.ico")


@bp.route("/assets/<path:path>")
async def assets(path):
    return await send_from_directory("static/assets", path)


# Search Access Middleware
async def verify_search_access(project_id):
    """Validate user access to project search resources"""
    user_details = await get_authenticated_user_details(request.headers)
    if not user_details or not any(proj.get('id') == project_id for proj in user_details.get('projects', [])):
        return jsonify({"error": f"Access denied to project {project_id}"}), 403
    return None


# Debug settings
DEBUG = os.environ.get("DEBUG", "false")
if DEBUG.lower() == "true":
    logging.basicConfig(level=logging.DEBUG)

# Configure logging based on environment variables
DISABLE_RBAC_LOGS = os.environ.get("RBAC_CONSOLE_LOG_LEVEL", "").upper() == "CRITICAL"
DISABLE_RBAC_HTTP_LOGS = os.environ.get("DISABLE_RBAC_HTTP_LOGS", "").lower() == "true"
DISABLE_HTTP_LOGS = os.environ.get("QUART_ACCESS_LOGGING", "1") == "0"

# Apply logging configuration
try:
    # Import our custom logging filters
    from backend.utils.disable_rbac_logs import (
        disable_rbac_logs,
        RBACFilter,
        HTTPAccessFilter,
    )

    # Disable all HTTP access logs if requested
    if DISABLE_HTTP_LOGS:
        # Create HTTP access filter
        http_filter = HTTPAccessFilter()

        # Apply to root logger
        root_logger = logging.getLogger()
        root_logger.addFilter(http_filter)

        # Apply to all handlers
        for handler in root_logger.handlers:
            handler.addFilter(http_filter)

        # Set Quart and Hypercorn loggers to WARNING level
        logging.getLogger("quart.serving").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.access").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.error").setLevel(logging.WARNING)

        logging.info("HTTP access logs have been disabled")

    # Disable RBAC logs if requested
    if DISABLE_RBAC_LOGS or DISABLE_RBAC_HTTP_LOGS:
        # Apply the full RBAC log disabling
        disable_rbac_logs()
        logging.info("RBAC logs have been disabled")
except Exception as e:
    logging.error(f"Failed to configure logging: {e}")

USER_AGENT = "GitHubSampleWebApp/AsyncAzureOpenAI/1.0.0"

# Frontend Settings via Environment Variables
frontend_settings = {
    "auth_enabled": app_settings.base_settings.auth_enabled,
    "feedback_enabled": (
        app_settings.chat_history and app_settings.chat_history.enable_feedback
    ),
    "ui": {
        "title": app_settings.ui.title,
        "logo": app_settings.ui.logo,
        "chat_logo": app_settings.ui.chat_logo or app_settings.ui.logo,
        "chat_title": app_settings.ui.chat_title,
        "chat_description": app_settings.ui.chat_description,
        "show_share_button": app_settings.ui.show_share_button,
        "show_chat_history_button": app_settings.ui.show_chat_history_button,
    },
    "sanitize_answer": app_settings.base_settings.sanitize_answer,
    "azure_function": {
        "maturity_assessment_key": app_settings.azure_function.maturity_assessment_key,
        "maturity_assessment_url": app_settings.azure_function.maturity_assessment_url,
        "executive_summary_key": app_settings.azure_function.executive_summary_key,
        "executive_summary_url": app_settings.azure_function.executive_summary_url,
    },
    "azure_storage": {
        "container_name": app_settings.azure_storage.container_name,
        "container_name_input": app_settings.azure_storage.container_name_input,
        "container_name_output": app_settings.azure_storage.container_name_output,
        "account_name": app_settings.azure_storage.account_name,
        "container_sas_token": app_settings.azure_storage.container_sas_token,
    },
    "azure_search": {
        "search_service_name": (
            app_settings.azure_search.search_service_name
            if hasattr(app_settings, "azure_search")
            and app_settings.azure_search.search_service_name
            else os.environ.get("AZURE_SEARCH_SERVICE", "")
        ),
        "index_name": (
            app_settings.azure_search.index_name
            if hasattr(app_settings, "azure_search")
            and app_settings.azure_search.index_name
            else os.environ.get("AZURE_SEARCH_INDEX", "")
        ),
        "indexer_name": (
            app_settings.azure_search.indexer_name
            if hasattr(app_settings, "azure_search")
            and app_settings.azure_search.indexer_name
            else os.environ.get("AZURE_SEARCH_INDEXER", "")
        ),
        "api_version": (
            app_settings.azure_search.api_version
            if hasattr(app_settings, "azure_search")
            and app_settings.azure_search.api_version
            else os.environ.get("AZURE_SEARCH_API_VERSION", "2021-04-30-Preview")
        ),
    },
}


# Enable Microsoft Defender for Cloud Integration
MS_DEFENDER_ENABLED = os.environ.get("MS_DEFENDER_ENABLED", "true").lower() == "true"


# Initialize Azure OpenAI Client
async def init_openai_client():
    azure_openai_client = None

    try:
        # API version check
        if (
            app_settings.azure_openai.preview_api_version
            < MINIMUM_SUPPORTED_AZURE_OPENAI_PREVIEW_API_VERSION
        ):
            raise ValueError(
                f"The minimum supported Azure OpenAI preview API version is '{MINIMUM_SUPPORTED_AZURE_OPENAI_PREVIEW_API_VERSION}'"
            )

        # Endpoint
        if (
            not app_settings.azure_openai.endpoint
            and not app_settings.azure_openai.resource
        ):
            raise ValueError(
                "AZURE_OPENAI_ENDPOINT or AZURE_OPENAI_RESOURCE is required"
            )

        endpoint = (
            app_settings.azure_openai.endpoint
            if app_settings.azure_openai.endpoint
            else f"https://{app_settings.azure_openai.resource}.openai.azure.com/"
        )

        # Authentication
        aoai_api_key = app_settings.azure_openai.key
        ad_token_provider = None
        if not aoai_api_key:
            logging.debug("No AZURE_OPENAI_KEY found, using Azure Entra ID auth")

            # For local development, we'll use a dummy token provider
            if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
                logging.info("Running in development mode, using dummy token provider for OpenAI")

                # Create a dummy token provider that doesn't actually authenticate
                async def dummy_token_provider():
                    return "dummy_token"

                ad_token_provider = dummy_token_provider
            else:
                # Check if we're running locally and should use Azure CLI credentials
                use_cli_cred = (
                    os.getenv("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
                )
                if use_cli_cred:
                    logging.info(
                        "Using Azure CLI credentials for OpenAI as specified by USE_AZURE_CLI_CREDENTIAL=true"
                    )
                    from azure.identity.aio import AzureCliCredential

                    async with AzureCliCredential() as credential:
                        ad_token_provider = get_bearer_token_provider(
                            credential, "https://cognitiveservices.azure.com/.default"
                        )
                else:
                    async with DefaultAzureCredential() as credential:
                        ad_token_provider = get_bearer_token_provider(
                            credential, "https://cognitiveservices.azure.com/.default"
                        )

        # Deployment
        deployment = app_settings.azure_openai.model
        if not deployment:
            raise ValueError("AZURE_OPENAI_MODEL is required")

        # Default Headers
        default_headers = {"x-ms-useragent": USER_AGENT}

        azure_openai_client = AsyncAzureOpenAI(
            api_version=app_settings.azure_openai.preview_api_version,
            api_key=aoai_api_key,
            azure_ad_token_provider=ad_token_provider,
            default_headers=default_headers,
            azure_endpoint=endpoint,
        )

        return azure_openai_client
    except Exception as e:
        logging.exception("Exception in Azure OpenAI initialization", e)
        azure_openai_client = None
        raise e


async def init_cosmosdb_client():
    cosmos_client = None
    if app_settings.chat_history:
        try:
            cosmos_endpoint = (
                f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
            )

            # For local development, we'll use a dummy credential
            if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
                logging.info("Running in development mode, using dummy credential for CosmosDB")
                credential = app_settings.chat_history.account_key or "dummy_key"
            elif not app_settings.chat_history.account_key:
                # Check if we're running locally and should use Azure CLI credentials
                use_cli_cred = (
                    os.getenv("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
                )
                if use_cli_cred:
                    logging.info(
                        "Using Azure CLI credentials for CosmosDB as specified by USE_AZURE_CLI_CREDENTIAL=true"
                    )
                    from azure.identity.aio import AzureCliCredential

                    async with AzureCliCredential() as cred:
                        credential = cred
                else:
                    async with DefaultAzureCredential() as cred:
                        credential = cred
            else:
                if not app_settings.chat_history.account_key:
                    # Check if we're running locally and should use Azure CLI credentials
                    use_cli_cred = os.getenv("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
                    if use_cli_cred:
                        logging.info("Using Azure CLI credentials for CosmosDB as specified by USE_AZURE_CLI_CREDENTIAL=true")
                        from azure.identity.aio import AzureCliCredential
                        async with AzureCliCredential() as cred:
                            credential = cred
                    else:
                        async with DefaultAzureCredential() as cred:
                            credential = cred
                else:
                    credential = app_settings.chat_history.account_key

            # Create and initialize the RBAC client which now handles conversations
            cosmos_client = CosmosRbacClient(
                cosmosdb_endpoint=cosmos_endpoint,
                credential=credential,
                database_name=app_settings.chat_history.database,
            )

            # Initialize the client with containers
            init_success = await cosmos_client.initialize(
                enable_message_feedback=app_settings.chat_history.enable_feedback
            )

            if not init_success:
                logging.error("Failed to initialize CosmosRbacClient")
                cosmos_client = None
                raise Exception("Failed to initialize CosmosRbacClient")

        except Exception as e:
            logging.exception("Exception in CosmosDB initialization", e)
            cosmos_client = None
            raise e
    else:
        logging.debug("CosmosDB not configured")

    return cosmos_client


# Modified to accept project_config
async def prepare_model_args(request_body, request_headers, project_config: dict):
    request_messages = request_body.get("messages", [])
    messages = []
    # Check if project_config is provided and contains necessary keys
    use_project_datasource = bool(
        project_config
        and project_config.get("search_index_name")
        and project_config.get("search_datasource_name")
    )

    # Determine if any datasource is being used (project or global)
    using_any_datasource = use_project_datasource or app_settings.datasource

    # Add system message only if NO datasource is being used
    if not using_any_datasource:
        logging.debug(
            "No datasource configured (project or global), adding default system message."
        )
        messages = [
            {"role": "system", "content": app_settings.azure_openai.system_message}
        ]

    for message in request_messages:
        if message:
            messages.append({"role": message["role"], "content": message["content"]})

    user_json = None
    if MS_DEFENDER_ENABLED:
        authenticated_user_details = await get_authenticated_user_details(request_headers)
        conversation_id = request_body.get("conversation_id", None)
        user_json = get_msdefender_user_json(
            authenticated_user_details, request_headers, conversation_id
        ) if authenticated_user_details else None

    model_args = {
        "messages": messages,
        "temperature": app_settings.azure_openai.temperature,
        "max_tokens": app_settings.azure_openai.max_tokens,
        "top_p": app_settings.azure_openai.top_p,
        "stop": app_settings.azure_openai.stop_sequence,
        "stream": app_settings.azure_openai.stream,
        "model": app_settings.azure_openai.model,
        "user": user_json,
    }

    # Construct data_sources payload dynamically based on project_config or global settings
    data_sources_config = None
    if use_project_datasource:
        logging.debug(
            f"Using project-specific datasource for project ID: {project_config.get('id')}"
        )
        # Construct the data source payload using project_config details
        # This assumes a structure similar to how app_settings.datasource might be configured,
        # but uses values from the project document. Adjust keys as necessary based on CosmosDB structure.
        # IMPORTANT: This example assumes API Key authentication for the search service.
        # If using other methods (like RBAC/Managed Identity), the authentication block needs adjustment.

        project_specific_search_key = project_config.get("environment", {}).get(
            "AZURE_SEARCH_KEY"
        )
        search_key_to_use = None

        if project_specific_search_key:
            search_key_to_use = project_specific_search_key
            logging.debug(
                f"Using project-specific Azure Search Key for project ID: {project_config.get('id')}"
            )
        else:
            search_key_to_use = app_settings.azure_search.key
            logging.warning(
                f"Project-specific AZURE_SEARCH_KEY not found for project ID: {project_config.get('id')}. Falling back to global search key."
            )

        if not search_key_to_use:
            logging.error(
                f"Azure Search Key is missing (checked project-specific and global) for project ID: {project_config.get('id')}. Cannot configure project datasource."
            )
            # Handle error appropriately - maybe raise an exception or return None/error indicator
        else:
            data_sources_config = [
                {
                    "type": "azure_search",
                    "parameters": {
                        "endpoint": f"https://{project_config['search_service_name']}.search.windows.net",
                        "index_name": project_config["search_index_name"],
                        # "semantic_configuration": app_settings.datasource.semantic_configuration if app_settings.datasource else "", # Optional: Get from global or project config
                        # "query_type": app_settings.datasource.query_type if app_settings.datasource else "simple", # Optional: Get from global or project config
                        # "fields_mapping": {}, # Optional: Define field mappings if needed
                        # "in_scope": app_settings.datasource.in_scope if app_settings.datasource else True, # Optional
                        # "role_information": app_settings.azure_openai.system_message, # Use global system message or potentially project-specific one
                        "strictness": (
                            app_settings.datasource.strictness
                            if app_settings.datasource
                            else 3
                        ),  # Optional: Get from global or project config
                        "top_n_documents": (
                            app_settings.datasource.top_k
                            if app_settings.datasource
                            and hasattr(app_settings.datasource, "top_k")
                            else 5
                        ),  # Optional: Get from global or project config
                        "authentication": {"type": "api_key", "key": search_key_to_use},
                        # Potentially add embedding dependency config here if needed, fetching details from global or project config
                    },
                }
            ]
    elif app_settings.datasource:
        # Fallback to global datasource if configured and project-specific one isn't used/available
        logging.debug("Using global datasource configuration.")
        data_sources_config = [
            app_settings.datasource.construct_payload_configuration(request=request)
        ]

    if data_sources_config:
        model_args["extra_body"] = {"data_sources": data_sources_config}

    model_args_clean = copy.deepcopy(model_args)
    if model_args_clean.get("extra_body"):
        secret_params = [
            "key",
            "connection_string",
            "embedding_key",
            "encoded_api_key",
            "api_key",
        ]
        for secret_param in secret_params:
            if model_args_clean["extra_body"]["data_sources"][0]["parameters"].get(
                secret_param
            ):
                model_args_clean["extra_body"]["data_sources"][0]["parameters"][
                    secret_param
                ] = "*****"
        authentication = model_args_clean["extra_body"]["data_sources"][0][
            "parameters"
        ].get("authentication", {})
        for field in authentication:
            if field in secret_params:
                model_args_clean["extra_body"]["data_sources"][0]["parameters"][
                    "authentication"
                ][field] = "*****"
        embeddingDependency = model_args_clean["extra_body"]["data_sources"][0][
            "parameters"
        ].get("embedding_dependency", {})
        if "authentication" in embeddingDependency:
            for field in embeddingDependency["authentication"]:
                if field in secret_params:
                    model_args_clean["extra_body"]["data_sources"][0]["parameters"][
                        "embedding_dependency"
                    ]["authentication"][field] = "*****"

    logging.debug(f"REQUEST BODY: {json.dumps(model_args_clean, indent=4)}")

    return model_args


async def promptflow_request(request):
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {app_settings.promptflow.api_key}",
        }
        # Adding timeout for scenarios where response takes longer to come back
        logging.debug(f"Setting timeout to {app_settings.promptflow.response_timeout}")
        async with httpx.AsyncClient(
            timeout=float(app_settings.promptflow.response_timeout)
        ) as client:
            pf_formatted_obj = convert_to_pf_format(
                request,
                app_settings.promptflow.request_field_name,
                app_settings.promptflow.response_field_name,
            )
            # NOTE: This only support question and chat_history parameters
            # If you need to add more parameters, you need to modify the request body
            response = await client.post(
                app_settings.promptflow.endpoint,
                json={
                    app_settings.promptflow.request_field_name: pf_formatted_obj[-1][
                        "inputs"
                    ][app_settings.promptflow.request_field_name],
                    "chat_history": pf_formatted_obj[:-1],
                },
                headers=headers,
            )
        resp = response.json()
        resp["id"] = request["messages"][-1]["id"]
        return resp
    except Exception as e:
        logging.error(f"An error occurred while making promptflow_request: {e}")


# Modified to accept project_config
async def send_chat_request(request_body, request_headers, project_config: dict):  # type: ignore
    filtered_messages = []
    messages = request_body.get("messages", [])
    for message in messages:
        if message.get("role") != "tool":
            filtered_messages.append(message)

    request_body["messages"] = filtered_messages
    # Pass project_config to prepare_model_args (will modify prepare_model_args next)
    model_args = await prepare_model_args(request_body, request_headers, project_config)

    try:
        # Use the client stored in the app context
        if not current_app.azure_openai_client:
            raise Exception("Azure OpenAI client is not initialized.")
        raw_response = await current_app.azure_openai_client.chat.completions.with_raw_response.create(
            **model_args
        )
        response = raw_response.parse()
        apim_request_id = raw_response.headers.get("apim-request-id")
    except Exception as e:
        logging.exception("Exception in send_chat_request")
        raise e

    return response, apim_request_id


# Modified to accept project_config
async def complete_chat_request(request_body, request_headers, project_config: dict):
    if app_settings.base_settings.use_promptflow:
        # NOTE: Promptflow integration currently doesn't support project-specific context easily.
        # This might need significant changes if promptflow is used with multi-project.
        # For now, it proceeds without project_config. Consider adding a warning or error.
        logging.warning(
            "Promptflow is enabled but project-specific context is not fully integrated."
        )
        response = await promptflow_request(request_body)
        history_metadata = request_body.get("history_metadata", {})
        return format_pf_non_streaming_response(
            response,
            history_metadata,
            app_settings.promptflow.response_field_name,
            app_settings.promptflow.citations_field_name,
        )
    else:
        # Pass project_config to send_chat_request (will modify send_chat_request next)
        response, apim_request_id = await send_chat_request(
            request_body, request_headers, project_config
        )
        history_metadata = request_body.get("history_metadata", {})
        return format_non_streaming_response(
            response, history_metadata, apim_request_id
        )


# Modified to accept project_config
async def stream_chat_request(request_body, request_headers, project_config: dict):
    # Pass project_config to send_chat_request (will modify send_chat_request next)
    response, apim_request_id = await send_chat_request(
        request_body, request_headers, project_config
    )
    history_metadata = request_body.get("history_metadata", {})

    async def generate():
        async for completionChunk in response:
            yield format_stream_response(
                completionChunk, history_metadata, apim_request_id
            )

    return generate()


# Modified to accept project_id
async def conversation_internal(request_body, request_headers, project_id: str):
    await cosmos_db_ready.wait()  # Ensure Cosmos client is ready
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id") if authenticated_user else None

    if not user_id:
        return jsonify({"error": "Authentication required."}), 401
    if not project_id:
        return jsonify({"error": "Project ID is required."}), 400
    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for conversation_internal")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        # Get the project's region from the RBAC system
        # First, find the project in the projects container
        projects = await current_app.cosmos_conversation_client.get_projects()
        project_config = None
        region_id = None

        # Find the project and get its region
        for project in projects:
            if project["id"] == project_id:
                project_config = project
                region_id = project.get("region")
                break

        if not project_config:
            logging.warning(
                f"Project config not found or access denied for ID: {project_id}, user: {user_id}"
            )
            return (
                jsonify({"error": "Project configuration not found or access denied."}),
                404,
            )

        # Validate required config for chat
        if (
            not project_config
            or not project_config.get("search_index_name")
            or not project_config.get("search_datasource_name")
        ):
            logging.error(
                f"Project {project_id} is missing required search configuration for chat."
            )
            return (
                jsonify(
                    {
                        "error": "Project configuration is incomplete for chat functionality."
                    }
                ),
                500,
            )

        # Proceed with chat request, passing project_config
        if (
            app_settings.azure_openai.stream
            and not app_settings.base_settings.use_promptflow
        ):
            # Pass project_config to stream_chat_request
            result = await stream_chat_request(
                request_body, request_headers, project_config
            )
            response = await make_response(format_as_ndjson(result))
            response.timeout = None
            response.mimetype = "application/json-lines"
            return response
        else:
            # Pass project_config to complete_chat_request
            result = await complete_chat_request(
                request_body, request_headers, project_config
            )
            return jsonify(result)

    except Exception as ex:
        logging.exception(ex)
        if hasattr(ex, "status_code"):
            return jsonify({"error": str(ex)}), ex.status_code
        else:
            return jsonify({"error": str(ex)}), 500


# Modified route to include project_id
@bp.route("/api/projects/<project_id>/conversation", methods=["POST"])
async def conversation(project_id: str):
    if not request.is_json:
        return jsonify({"error": "request must be json"}), 415
    request_json = await request.get_json()

    # Pass project_id to the internal handler (will modify conversation_internal next)
    return await conversation_internal(request_json, request.headers, project_id)


@bp.route("/frontend_settings", methods=["GET"])
def get_frontend_settings():
    try:
        return jsonify(frontend_settings), 200
    except Exception as e:
        logging.exception("Exception in /frontend_settings")
        return jsonify({"error": str(e)}), 500


## Conversation History API ##
@bp.route("/history/generate", methods=["POST"])
async def add_conversation():
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for conversation_id
    request_json = await request.get_json()
    conversation_id = request_json.get("conversation_id", None)
    project_id = request_json.get("project_id")

    if not project_id:
        logging.error("Missing project_id in /history/generate request body.")
        return jsonify({"error": "project_id is required in the request body"}), 400

    try:
        # make sure cosmos is configured
        if not current_app.cosmos_conversation_client:
            raise Exception("CosmosDB is not configured or not working")

        # Get the project's region from the RBAC system
        projects = await current_app.cosmos_conversation_client.get_projects()
        region_id = None

        # Find the project and get its region
        for project in projects:
            if project["id"] == project_id:
                region_id = project.get("region")
                break

        if not region_id:
            logging.error(f"Could not find region for project {project_id}")
            return (
                jsonify({"error": f"Could not find region for project {project_id}"}),
                404,
            )

        # check for the conversation_id, if the conversation is not set, we will create a new one
        history_metadata = {}
        if not conversation_id:
            title = await generate_title(request_json["messages"])
            conversation_dict = (
                await current_app.cosmos_conversation_client.create_conversation(
                    user_id=user_id,
                    project_id=project_id,
                    region_id=region_id,
                    title=title,
                )
            )
            conversation_id = conversation_dict["id"]
            history_metadata["title"] = title
            history_metadata["date"] = conversation_dict["createdAt"]

        ## Format the incoming message object in the "chat/completions" messages format
        ## then write it to the conversation history in cosmos
        messages = request_json["messages"]
        if len(messages) > 0 and messages[-1]["role"] == "user":
            createdMessageValue = (
                await current_app.cosmos_conversation_client.create_message(
                    uuid_str=str(uuid.uuid4()),
                    conversation_id=conversation_id,
                    user_id=user_id,
                    input_message=messages[-1],
                )
            )
            if createdMessageValue == "Conversation not found":
                raise Exception(
                    "Conversation not found for the given conversation ID: "
                    + conversation_id
                    + "."
                )
        else:
            raise Exception("No user message found")

        # Submit request to Chat Completions for response
        request_body = await request.get_json()
        history_metadata["conversation_id"] = conversation_id
        request_body["history_metadata"] = history_metadata

        # Pass project_id to conversation_internal
        return await conversation_internal(request_body, request.headers, project_id)

    except Exception as e:
        logging.exception("Exception in /history/generate")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/update", methods=["POST"])
async def update_conversation():
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for conversation_id
    request_json = await request.get_json()
    conversation_id = request_json.get("conversation_id", None)

    try:
        # make sure cosmos is configured
        if not current_app.cosmos_conversation_client:
            raise Exception("CosmosDB is not configured or not working")

        # check for the conversation_id, if the conversation is not set, we will create a new one
        if not conversation_id:
            raise Exception("No conversation_id found")

        ## Format the incoming message object in the "chat/completions" messages format
        ## then write it to the conversation history in cosmos
        messages = request_json["messages"]
        if len(messages) > 0 and messages[-1]["role"] == "assistant":
            if len(messages) > 1 and messages[-2].get("role", None) == "tool":
                # write the tool message first
                await current_app.cosmos_conversation_client.create_message(
                    uuid_str=str(uuid.uuid4()),
                    conversation_id=conversation_id,
                    user_id=user_id,
                    input_message=messages[-2],
                )
            # write the assistant message
            await current_app.cosmos_conversation_client.create_message(
                uuid_str=messages[-1]["id"],
                conversation_id=conversation_id,
                user_id=user_id,
                input_message=messages[-1],
            )
        else:
            raise Exception("No bot messages found")

        # Submit request to Chat Completions for response
        response = {"success": True}
        return jsonify(response), 200

    except Exception as e:
        logging.exception("Exception in /history/update")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/message_feedback", methods=["POST"])
async def update_message():
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for message_id
    request_json = await request.get_json()
    message_id = request_json.get("message_id", None)
    message_feedback = request_json.get("message_feedback", None)
    try:
        if not message_id:
            return jsonify({"error": "message_id is required"}), 400

        if not message_feedback:
            return jsonify({"error": "message_feedback is required"}), 400

        ## update the message in cosmos
        updated_message = (
            await current_app.cosmos_conversation_client.update_message_feedback(
                user_id, message_id, message_feedback
            )
        )
        if updated_message:
            return (
                jsonify(
                    {
                        "message": f"Successfully updated message with feedback {message_feedback}",
                        "message_id": message_id,
                    }
                ),
                200,
            )
        else:
            return (
                jsonify(
                    {
                        "error": f"Unable to update message {message_id}. It either does not exist or the user does not have access to it."
                    }
                ),
                404,
            )

    except Exception as e:
        logging.exception("Exception in /history/message_feedback")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/delete", methods=["DELETE"])
async def delete_conversation():
    await cosmos_db_ready.wait()
    ## get the user id from the request headers
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for conversation_id
    request_json = await request.get_json()
    conversation_id = request_json.get("conversation_id", None)

    try:
        if not conversation_id:
            return jsonify({"error": "conversation_id is required"}), 400

        ## make sure cosmos is configured
        if not current_app.cosmos_conversation_client:
            raise Exception("CosmosDB is not configured or not working")

        ## delete the conversation messages from cosmos first
        await current_app.cosmos_conversation_client.delete_messages(
            conversation_id, user_id
        )

        ## Now delete the conversation
        await current_app.cosmos_conversation_client.delete_conversation(
            user_id, conversation_id
        )

        return (
            jsonify(
                {
                    "message": "Successfully deleted conversation and messages",
                    "conversation_id": conversation_id,
                }
            ),
            200,
        )
    except Exception as e:
        logging.exception("Exception in /history/delete")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/list", methods=["GET"])
async def list_conversations():
    try:
        await cosmos_db_ready.wait()
        offset = request.args.get("offset", 0)
        project_id = request.args.get("project_id", None)
        region_id = request.args.get("region_id", None)
        authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
        user_id = authenticated_user["user_principal_id"]

        ## make sure cosmos is configured
        if not current_app.cosmos_conversation_client:
            raise Exception("CosmosDB is not configured or not working")

        ## get the conversations from cosmos with optional project and region filtering
        conversations = await current_app.cosmos_conversation_client.get_conversations(
            user_id, project_id=project_id, region_id=region_id, offset=offset, limit=25
        )
        if not isinstance(conversations, list):
            return jsonify({"error": f"No conversations for {user_id} were found"}), 404

        ## return the conversation ids
        return jsonify(conversations), 200
    except Exception as e:
        logging.exception("Exception in /history/list")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/read", methods=["POST"])
async def get_conversation():
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for conversation_id
    request_json = await request.get_json()
    conversation_id = request_json.get("conversation_id", None)

    if not conversation_id:
        return jsonify({"error": "conversation_id is required"}), 400

    ## make sure cosmos is configured
    if not current_app.cosmos_conversation_client:
        raise Exception("CosmosDB is not configured or not working")

    ## get the conversation object and the related messages from cosmos
    conversation = await current_app.cosmos_conversation_client.get_conversation(
        user_id, conversation_id
    )
    ## return the conversation id and the messages in the bot frontend format
    if not conversation:
        return (
            jsonify(
                {
                    "error": f"Conversation {conversation_id} was not found. It either does not exist or the logged in user does not have access to it."
                }
            ),
            404,
        )

    # get the messages for the conversation from cosmos
    conversation_messages = await current_app.cosmos_conversation_client.get_messages(
        user_id, conversation_id
    )

    ## format the messages in the bot frontend format
    messages = [
        {
            "id": msg["id"],
            "role": msg["role"],
            "content": msg["content"],
            "createdAt": msg["createdAt"],
            "feedback": msg.get("feedback"),
        }
        for msg in conversation_messages
    ]

    return jsonify({"conversation_id": conversation_id, "messages": messages}), 200


@bp.route("/history/rename", methods=["POST"])
async def rename_conversation():
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for conversation_id
    request_json = await request.get_json()
    conversation_id = request_json.get("conversation_id", None)

    if not conversation_id:
        return jsonify({"error": "conversation_id is required"}), 400

    ## make sure cosmos is configured
    if not current_app.cosmos_conversation_client:
        raise Exception("CosmosDB is not configured or not working")

    ## get the conversation from cosmos
    conversation = await current_app.cosmos_conversation_client.get_conversation(
        user_id, conversation_id
    )
    if not conversation:
        return (
            jsonify(
                {
                    "error": f"Conversation {conversation_id} was not found. It either does not exist or the logged in user does not have access to it."
                }
            ),
            404,
        )

    ## update the title
    title = request_json.get("title", None)
    if not title:
        return jsonify({"error": "title is required"}), 400
    conversation["title"] = title
    updated_conversation = (
        await current_app.cosmos_conversation_client.upsert_conversation(conversation)
    )

    return jsonify(updated_conversation), 200


@bp.route("/history/delete_all", methods=["DELETE"])
async def delete_all_conversations():
    await cosmos_db_ready.wait()
    ## get the user id from the request headers
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    # get conversations for user
    try:
        ## make sure cosmos is configured
        if not current_app.cosmos_conversation_client:
            raise Exception("CosmosDB is not configured or not working")

        conversations = await current_app.cosmos_conversation_client.get_conversations(
            user_id, offset=0, limit=None
        )
        if not conversations:
            return jsonify({"error": f"No conversations for {user_id} were found"}), 404

        # delete each conversation
        for conversation in conversations:
            ## delete the conversation messages from cosmos first
            await current_app.cosmos_conversation_client.delete_messages(
                conversation["id"], user_id
            )

            ## Now delete the conversation
            await current_app.cosmos_conversation_client.delete_conversation(
                user_id, conversation["id"]
            )
        return (
            jsonify(
                {
                    "message": f"Successfully deleted conversation and messages for user {user_id}"
                }
            ),
            200,
        )

    except Exception as e:
        logging.exception("Exception in /history/delete_all")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/clear", methods=["POST"])
async def clear_messages():
    await cosmos_db_ready.wait()
    ## get the user id from the request headers
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user["user_principal_id"]

    ## check request for conversation_id
    request_json = await request.get_json()
    conversation_id = request_json.get("conversation_id", None)

    try:
        if not conversation_id:
            return jsonify({"error": "conversation_id is required"}), 400

        ## make sure cosmos is configured
        if not current_app.cosmos_conversation_client:
            raise Exception("CosmosDB is not configured or not working")

        ## delete the conversation messages from cosmos
        await current_app.cosmos_conversation_client.delete_messages(
            conversation_id, user_id
        )

        return (
            jsonify(
                {
                    "message": "Successfully deleted messages in conversation",
                    "conversation_id": conversation_id,
                }
            ),
            200,
        )
    except Exception as e:
        logging.exception("Exception in /history/clear_messages")
        return jsonify({"error": str(e)}), 500


@bp.route("/history/ensure", methods=["GET"])
async def ensure_cosmos():
    await cosmos_db_ready.wait()
    if not app_settings.chat_history:
        return jsonify({"error": "CosmosDB is not configured"}), 404

    try:
        success, err = await current_app.cosmos_conversation_client.ensure()
        if not current_app.cosmos_conversation_client or not success:
            if err:
                return jsonify({"error": err}), 422
            return jsonify({"error": "CosmosDB is not configured or not working"}), 500

        return jsonify({"message": "CosmosDB is configured and working"}), 200
    except Exception as e:
        logging.exception("Exception in /history/ensure")
        cosmos_exception = str(e)
        if "Invalid credentials" in cosmos_exception:
            return jsonify({"error": cosmos_exception}), 401
        elif "Invalid CosmosDB database name" in cosmos_exception:
            return (
                jsonify(
                    {
                        "error": f"{cosmos_exception} {app_settings.chat_history.database} for account {app_settings.chat_history.account}"
                    }
                ),
                422,
            )
        elif "Invalid CosmosDB container name" in cosmos_exception:
            return (
                jsonify({"error": f"{cosmos_exception}: conversations_project"}),
                422,
            )
        else:
            return jsonify({"error": "CosmosDB is not working"}), 500


async def generate_title(conversation_messages) -> str:
    ## make sure the messages are sorted by _ts descending
    title_prompt = "Summarize the conversation so far into a 4-word or less title. Do not use any quotation marks or punctuation. Do not include any other commentary or description."

    messages = [
        {"role": msg["role"], "content": msg["content"]}
        for msg in conversation_messages
    ]
    messages.append({"role": "user", "content": title_prompt})

    try:
        # Use the client stored in the app context
        if not current_app.azure_openai_client:
            raise Exception("Azure OpenAI client is not initialized.")
        response = await current_app.azure_openai_client.chat.completions.create(
            model=app_settings.azure_openai.model,
            messages=messages,
            temperature=1,
            max_tokens=64,
        )

        title = response.choices[0].message.content
        return title
    except Exception as e:
        logging.exception("Exception while generating title", e)
        return messages[-2]["content"]


@bp.route("/api/search/<path:search_path>", methods=["GET", "POST"])
async def proxy_ai_search_request(search_path):
    """
    Proxy endpoint for AI Search service. Instantiates the service only when needed.
    This avoids running the AI Search service continuously.

    The search_path can be in two formats:
    1. service_name/operation/params - For service-specific operations (e.g., search-test080512-azj2/list-indexed-files/project-test080512-index)
    2. operation/params - For global service operations (legacy format)
    """
    try:
        # Check if the path includes a service name
        path_parts = search_path.split("/")
        search_service_name = None

        # Extract project ID from query parameters if available
        project_id = request.args.get("projectId")
        if project_id:
            logging.info(f"Project ID from query parameters: {project_id}")

        # If the path has at least 2 parts and doesn't start with a known operation,
        # assume the first part is the service name
        known_operations = ["list-indexed-files", "reindex-file", "config"]
        if len(path_parts) >= 2 and path_parts[0] not in known_operations:
            search_service_name = path_parts[0]
            # Remove the service name from the path for the FastAPI routing
            search_path = "/".join(path_parts[1:])
            logging.info(
                f"Using service-specific search: {search_service_name} for path: {search_path}"
            )
        else:
            # Try to extract project ID from the path for index operations
            # Format: list-indexed-files/project-{name}-index
            if path_parts[0] == "list-indexed-files" and len(path_parts) > 1:
                index_name = path_parts[1]
                logging.info(
                    f"Trying to determine search service for index: {index_name}"
                )

                project_search_service_name = None

                # If project ID is available, try to get the project directly
                if project_id and current_app.cosmos_conversation_client:
                    try:
                        logging.info(f"Looking up project with ID: {project_id}")
                        # Get authenticated user ID
                        authenticated_user = await get_authenticated_user_details(
                            request_headers=request.headers
                        )
                        user_id = authenticated_user.get(
                            "user_principal_id", "anonymous"
                        )

                        # Get the project using both user_id and project_id
                        project = (
                            await current_app.cosmos_conversation_client.get_project(
                                user_id, project_id
                            )
                        )
                        if project:
                            project_search_service_name = project.get(
                                "search_service_name"
                            )
                            if project_search_service_name:
                                search_service_name = project_search_service_name
                                logging.info(
                                    f"Found search service {search_service_name} for project {project_id}"
                                )
                    except Exception as e:
                        logging.error(f"Error looking up project {project_id}: {e}")

                # If we still don't have a search service name, try to extract from the index name
                if not project_search_service_name and index_name.startswith(
                    "project-"
                ):
                    # Try to extract project name from index name (project-name-index)
                    project_name_part = (
                        index_name.split("-")[1]
                        if len(index_name.split("-")) > 2
                        else None
                    )
                    if project_name_part:
                        logging.info(
                            f"Extracted project name part: {project_name_part} from index name"
                        )

                        # Try to find the project in CosmosDB
                        if current_app.cosmos_conversation_client:
                            try:
                                # Query for projects with this name part
                                parameters = [
                                    {
                                        "name": "@namePattern",
                                        "value": f"%{project_name_part}%",
                                    }
                                ]
                                query = "SELECT * FROM c WHERE c.type = 'project' AND CONTAINS(c.search_index_name, @namePattern)"
                                projects = []

                                async for (
                                    item
                                ) in current_app.cosmos_conversation_client.container_client.query_items(
                                    query=query,
                                    parameters=parameters,
                                    partition_key=None,  # Query across all partition keys
                                ):
                                    projects.append(item)

                                if projects:
                                    # Use the first matching project's search service
                                    project = projects[0]
                                    search_service_name = project.get(
                                        "search_service_name"
                                    )

                                    # Get search API key from project environment
                                    search_api_key = None
                                    if (
                                        "environment" in project
                                        and project["environment"]
                                        and "AZURE_SEARCH_KEY" in project["environment"]
                                    ):
                                        search_api_key = project["environment"][
                                            "AZURE_SEARCH_KEY"
                                        ]

                                    if search_service_name:
                                        logging.info(
                                            f"Found search service {search_service_name} for index {index_name}"
                                        )
                                    if search_api_key:
                                        logging.info(
                                            f"Found search API key for index {index_name}"
                                        )
                            except Exception as e:
                                logging.error(
                                    f"Error looking up project for index {index_name}: {e}"
                                )

        # Create search service instance on-demand with the specified service name and API key
        search_api_key = None
        if project_id and current_app.cosmos_conversation_client:
            try:
                # Get authenticated user ID
                authenticated_user = await get_authenticated_user_details(
                    request_headers=request.headers
                )
                user_id = authenticated_user.get("user_principal_id", "anonymous")

                # Get the project using both user_id and project_id
                project = await current_app.cosmos_conversation_client.get_project(
                    user_id, project_id
                )
                if project:
                    # Get search API key from project environment
                    if (
                        "environment" in project
                        and project["environment"]
                        and "AZURE_SEARCH_KEY" in project["environment"]
                    ):
                        search_api_key = project["environment"]["AZURE_SEARCH_KEY"]
                        logging.info(
                            f"Using search API key from project {project_id} environment"
                        )
            except Exception as e:
                logging.error(
                    f"Error getting search API key for project {project_id}: {e}"
                )

        search_service = AzureAISearchService(search_service_name, search_api_key)
        app = search_service.get_app()

        # Extract parameters from the request
        request_json = await request.get_json() if request.method == "POST" else None
        query_params = request.args
        headers = {
            k: v
            for k, v in request.headers.items()
            if k.lower() not in ("host", "connection")
        }

        # Construct the FastAPI path for the search service
        fastapi_path = f"/api/search/{search_path}"

        # Use starlette for request handling
        from starlette.testclient import TestClient

        # Create a TestClient instance
        client = TestClient(app)

        try:
            # Log the request details
            logging.info(f"Proxying search request: {request.method} {fastapi_path}")
            if search_service_name:
                logging.info(f"Using search service: {search_service_name}")

            # Make the request to the FastAPI app
            # Ensure projectId is passed to the FastAPI app if available
            params = dict(query_params)
            if "projectId" in params:
                logging.info(
                    f"Passing projectId parameter to FastAPI app: {params['projectId']}"
                )

            if request.method == "GET":
                response = client.get(fastapi_path, params=params, headers=headers)
            else:  # POST
                response = client.post(
                    fastapi_path, json=request_json, params=params, headers=headers
                )

            # Log the response status
            logging.info(
                f"Search proxy response: {response.status_code} for {fastapi_path}"
            )

            # For list-indexed-files endpoint, limit the number of files returned to prevent large responses
            if "list-indexed-files" in fastapi_path and response.status_code == 200:
                try:
                    # Try to parse the JSON response with better error handling
                    try:
                        data = response.json()
                    except json.JSONDecodeError as json_err:
                        logging.error(
                            f"JSON parsing error in list-indexed-files response: {json_err}. Response content: {response.content[:200]}..."
                        )
                        # Return a fallback empty response instead of failing
                        return (
                            jsonify(
                                {
                                    "files": [],
                                    "count": 0,
                                    "error": "Failed to parse search response",
                                }
                            ),
                            200,
                        )

                    # Validate the response structure
                    if not isinstance(data, dict):
                        logging.error(
                            f"Unexpected response type from search API: {type(data)}"
                        )
                        return (
                            jsonify(
                                {
                                    "files": [],
                                    "count": 0,
                                    "error": "Invalid response format from search service",
                                }
                            ),
                            200,
                        )

                    # Check if 'files' field exists and is a list
                    if "files" not in data or not isinstance(data["files"], list):
                        logging.error(
                            f"'files' field missing or not a list in search response: {data}"
                        )
                        return (
                            jsonify(
                                {
                                    "files": [],
                                    "count": 0,
                                    "error": "Invalid 'files' field in search response",
                                }
                            ),
                            200,
                        )

                    # Log the total number of documents vs unique titles
                    if "total_docs" in data:
                        logging.info(
                            f"Index contains {data['total_docs']} total documents with {len(data['files'])} unique titles"
                        )

                    # Only keep the first 500 files if there are more
                    if len(data["files"]) > 500:
                        logging.warning(
                            f"Limiting indexed files response from {len(data['files'])} to 500 files"
                        )
                        data["files"] = data["files"][:500]
                        data["count"] = 500
                        data["truncated"] = True

                    # Return the modified JSON response
                    return jsonify(data), 200
                except Exception as e:
                    logging.error(
                        f"Error processing list-indexed-files response: {type(e).__name__}: {e}"
                    )
                    # Return a fallback empty response instead of failing
                    return (
                        jsonify(
                            {
                                "files": [],
                                "count": 0,
                                "error": f"Error processing search response: {str(e)}",
                            }
                        ),
                        200,
                    )

            # Create a direct response with the raw content
            try:
                raw_response = await make_response(response.content)
                raw_response.status_code = response.status_code

                # Set content type explicitly
                raw_response.headers["Content-Type"] = response.headers.get(
                    "Content-Type", "application/json"
                )

                # Copy other relevant headers
                for key, value in response.headers.items():
                    if key.lower() not in (
                        "content-length",
                        "transfer-encoding",
                        "content-type",
                    ):
                        raw_response.headers[key] = value

                return raw_response
            except Exception as resp_err:
                logging.error(
                    f"Error creating response from search service content: {type(resp_err).__name__}: {resp_err}"
                )
                # Try to return a basic response with the content
                return (
                    response.content,
                    response.status_code,
                    {"Content-Type": "application/json"},
                )

        except Exception as e:
            logging.error(f"Error in TestClient request: {type(e).__name__}: {e}")
            # Return a more user-friendly error message
            return (
                jsonify(
                    {
                        "error": "Failed to process search request",
                        "details": str(e),
                        "files": [],
                        "count": 0,
                    }
                ),
                500,
            )

    except Exception as e:
        logging.exception(f"Error proxying AI Search request: {e}")
        return jsonify({"error": str(e)}), 500


# --- Project API Endpoints ---
@bp.route("/api/projects", methods=["GET", "POST"])
async def handle_projects():
    # TODO: Add user authentication/authorization check here
    # For now, assume user_id is available or handle anonymous access
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get(
        "user_principal_id", "anonymous"
    )  # Example: default to anonymous

    if request.method == "GET":
        logging.info(f"GET /api/projects called by user {user_id}")
        if not current_app.cosmos_conversation_client:
            logging.warning(
                "CosmosDB client not ready yet, returning empty projects list"
            )
            # Return empty array instead of error to allow frontend to show the page
            return jsonify([])

        # Try to get from cache first
        cache_key = f"projects:{user_id}"  # Simplified cache key
        cached_projects = cache_manager.get(cache_key)
        cached_etag = cache_manager.get(f"{cache_key}:etag")

        # Check if client has a valid cached version using ETag
        if cached_etag and request.headers.get("If-None-Match") == cached_etag:
            return "", 304  # Not Modified

        if cached_projects is not None:
            response = jsonify(cached_projects)
            if cached_etag:
                response.headers["ETag"] = cached_etag
            # Set consistent cache control header
            response.headers["Cache-Control"] = "public, max-age=300"  # 5 minutes
            logging.info(
                f"Returning {len(cached_projects)} projects from cache for user {user_id}"
            )
            return response

        try:
            # Get projects from database
            projects = await current_app.cosmos_conversation_client.get_projects(
                user_id
            )

            # Ensure we always return a list, even if the backend returns None or something else
            if not isinstance(projects, list):
                logging.warning(
                    f"get_projects returned non-list value: {type(projects)}, returning empty list"
                )
                projects = []

            # Generate ETag (simple hash of the response, ensure consistent order for hashing)
            etag = hashlib.md5(json.dumps(projects, sort_keys=True).encode()).hexdigest()

            # Cache the ETag
            cache_manager.set(
                f"{cache_key}:etag", etag, 300
            )  # Cache ETag for 5 minutes

            # Cache the projects with a consistent TTL
            ttl = 300  # 5 minutes
            cache_manager.set(cache_key, projects, ttl)

            # Create response with caching headers
            response = jsonify(projects)
            response.headers["ETag"] = etag
            response.headers["Cache-Control"] = "public, max-age=300"  # 5 minutes

            logging.info(f"Returning {len(projects)} projects for user {user_id}")
            return response
        except Exception as e:
            logging.error(f"Error fetching projects for user {user_id}: {e}")
            # Return empty array instead of error to allow frontend to show the page
            return jsonify([])

    elif request.method == "POST":
        logging.info(f"POST /api/projects called by user {user_id}")
        if not current_app.cosmos_conversation_client:
            return (
                jsonify({"error": "Project storage (CosmosDB) is not configured."}),
                503,
            )

        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 415

        # Authorization: only SUPER_ADMIN or REGIONAL_ADMIN may create projects
        user_details = await get_authenticated_user_details(request_headers=request.headers)
        
        # Get user role from RBAC database
        role = None
        user_region = None
        user_principal_id = user_details.get("user_principal_id")
        
        if user_principal_id and current_app.cosmos_conversation_client:
            try:
                # Get user from RBAC database
                user_in_db = await current_app.cosmos_conversation_client.get_user(user_principal_id)
                if user_in_db:
                    role = user_in_db.get("role", "REGULAR_USER")
                    user_region = user_in_db.get("region")
                    logging.info(f"Found user {user_principal_id} in RBAC database with role {role}")
                else:
                    # User not in database, default to no permissions
                    role = "REGULAR_USER"
                    logging.warning(f"User {user_principal_id} not found in RBAC database, defaulting to REGULAR_USER")
            except Exception as e:
                logging.error(f"Error fetching user from RBAC database: {e}")
                role = "REGULAR_USER"
        else:
            # No user ID or database, default to no permissions
            role = "REGULAR_USER"
            logging.warning("No user principal ID or RBAC database available")

        if role not in ["SUPER_ADMIN", "REGIONAL_ADMIN"]:
            logging.warning(
                f"Unauthorized project creation attempt by user {user_id} with role {role}"
            )
            return jsonify({"error": "Not authorized to create projects"}), 403

        request_json = await request.get_json()

        if role == "REGIONAL_ADMIN":
            req_region = request_json.get("region")
            if req_region != user_region:
                logging.warning(
                    f"Regional admin {user_id} attempted to create project in region {req_region} but is restricted to region {user_region}"
                )
                return jsonify({"error": "Cannot create project in this region"}), 403
        project_name = request_json.get("name")

        if not project_name:
            return jsonify({"error": "Project name is required"}), 400

        # Generate project details
        project_id = str(uuid.uuid4())
        sanitized_name = sanitize_for_azure(project_name)
        # Generate a unique suffix for all resources
        unique_suffix = uuid.uuid4().hex[:4]
        # Generate names for the three required containers
        base_container_name = (
            f"project-{sanitized_name}-{unique_suffix}"  # Add unique suffix
        )
        container_names = {
            "uploads": f"{base_container_name}-uploads",
            "input": f"{base_container_name}-input",
            "output": f"{base_container_name}-output",
        }
        # Generate function app name (must be globally unique as it becomes a *.azurewebsites.net URL)
        function_app_name = f"func-{sanitized_name}-{unique_suffix}"

        # Function app name will be added to new_project_data below
        created_at = datetime.now(timezone.utc).isoformat()

        # Generate a random business icon from a curated list
        business_icons = [
            "📊",
            "📈",
            "📉",
            "📋",
            "📝",
            "📑",
            "📚",
            "📁",
            "📂",
            "🗂️",
            "📒",
            "📓",
            "📔",
            "📕",
            "📗",
            "📘",
            "📙",
            "💼",
            "🗄️",
            "🖥️",
            "💻",
            "🖨️",
            "📱",
            "📞",
            "📧",
            "🔍",
            "🔎",
            "🔬",
            "🔭",
            "📡",
            "💡",
            "⚙️",
            "🔧",
            "🔨",
            "🛠️",
            "📌",
            "📍",
            "✂️",
            "📏",
            "📐",
            "🧮",
            "🌐",
            "🏢",
            "🏭",
            "🏗️",
            "🚀",
            "⏱️",
            "📅",
            "📆",
            "🗓️",
            "⏰",
            "🧠",
            "👥",
            "🤝",
            "🔐",
            "🔑",
        ]
        import random

        project_icon = random.choice(business_icons)

        # Generate storage account name (special handling - no hyphens, max 24 chars)
        storage_name_base = sanitized_name.replace("-", "")
        storage_name_truncated = (
            storage_name_base[:16] if len(storage_name_base) > 16 else storage_name_base
        )
        storage_account_name = f"st{storage_name_truncated}{unique_suffix}"

        # Generate search service name
        search_service_name = f"search-{sanitized_name}-{unique_suffix}"

        # Define function names
        function_names = ["maturity_assessment", "executive_summary"]

        # Log the generated resource names
        logging.info(f"Generated storage account name: {storage_account_name}")
        logging.info(f"Generated search service name: {search_service_name}")

        new_project_data = {
            "id": project_id,
            "name": project_name,
            "description": request_json.get("description", ""),
            "region": request_json.get(
                "region", "westeurope"
            ),  # Get region from request or default to westeurope
            "created_at": created_at,
            "updated_at": created_at,
            # Storage resources
            "storage_account_name": storage_account_name,
            "storage_container_uploads": container_names["uploads"],
            "storage_container_input": container_names["input"],
            "storage_container_output": container_names["output"],
            # Search resources
            "search_service_name": search_service_name,
            "search_index_name": f"project-{sanitized_name}-index",
            "search_datasource_name": f"project-{sanitized_name}-ds",
            "search_indexer_name": f"project-{sanitized_name}-indexer",
            # Function app resources
            "function_app_name": function_app_name,
            "function_names": function_names,
            # UI and metadata
            "icon": project_icon,  # Add the randomly generated business icon
            "role": "owner",  # Example role
            "environment": {},  # Example environment vars - could store shared keys here if needed securely
        }

        # Store project metadata in Cosmos DB
        try:
            # Ensure the client is ready before attempting DB operation
            await cosmos_db_ready.wait()
            if not current_app.cosmos_conversation_client:
                raise Exception("CosmosDB client is not available.")

            # Add owner field to project data
            new_project_data["owner"] = user_id

            # Store initial project document in Cosmos DB
            created_project_doc = (
                await current_app.cosmos_conversation_client.create_project(
                    new_project_data
                )
            )
            logging.info(
                f"Project '{project_name}' (ID: {project_id}) metadata saved successfully in Cosmos DB."
            )

            # Only use background deployment method (removing API deployment call)

            # Start Azure resources deployment in a background thread
            # Use the project name and region for the deployment
            project_name = new_project_data.get("name")
            region_id = new_project_data.get("region")
            if project_name:
                logging.info(
                    f"Starting Azure resources deployment for project {project_id} (name: {project_name}, region: {region_id})"
                )

                # Extract the base URL from the request before passing to the thread
                # We need to construct it manually since we can't access request in the background thread
                host = request.host
                scheme = request.scheme
                base_url = f"{scheme}://{host}"

                # Define a wrapper function to call the async deploy_project_resources function
                def deploy_project_resources_wrapper(
                    proj_id, proj_name, region_id, api_base_url
                ):
                    try:
                        logging.info(
                            f"Deploying Azure resources with Bicep for project {proj_id}"
                        )
                        # Import the async function
                        from deploy_project_resources import (
                            deploy_project_resources as deploy_async_func,
                        )
                    except Exception as e:
                        logging.error(f"Error importing deploy_project_resources: {e}")
                        raise

                # Invalidate cache for this user's projects
                try:
                    cache_manager.invalidate_by_prefix(
                        f"projects:{user_id}"
                    )  # Simplified cache key prefix
                    logging.info(
                        f"Invalidated projects cache for user {user_id} after project creation initiation"
                    )
                except Exception as e:
                    logging.error(f"Failed to invalidate cache for user {user_id}: {e}")
                    # Continue with project creation even if cache invalidation fails
                    pass

            return jsonify(created_project_doc), 201  # Return 201 Created

        except Exception as e:
            logging.error(f"Failed during project initiation for {project_id}: {e}")
            # If DB save failed, just return error. No Azure resources to clean up from this handler.
            return (
                jsonify({"error": f"Failed to initiate project creation: {str(e)}"}),
                500,
            )

    else:
        # Method Not Allowed
        return jsonify({"error": "Method not allowed"}), 405


@bp.route("/api/projects/<project_id>/function-config", methods=["GET"])
async def get_project_function_config(project_id):
    """
    Securely retrieves necessary configuration details for deploying
    Azure Functions for a specific project.
    Requires authentication and authorization.
    """
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id")

    if not user_id:
        return jsonify({"error": "Authentication required."}), 401

    logging.info(
        f"GET /api/projects/{project_id}/function-config called by user {user_id}"
    )

    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for get_project_function_config")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )

        if not project_doc:
            logging.warning(
                f"Project config not found for ID: {project_id} and user: {user_id} in function-config request"
            )
            return (
                jsonify({"error": "Project configuration not found or access denied."}),
                404,
            )

        # Extract only the necessary fields for function deployment script
        # Avoid exposing sensitive data unnecessarily
        # Get the function app name from the project document
        function_app_name = project_doc.get("function_app_name")

        # Construct the function app URL if we have a name
        function_app_url = None
        if function_app_name:
            function_app_url = f"https://{function_app_name}.azurewebsites.net"

        config_data = {
            "project_id": project_doc.get("id"),
            "project_name": project_doc.get("name"),
            # Storage resources
            "storage_account_name": project_doc.get("storage_account_name"),
            "storage_container_uploads": project_doc.get("storage_container_uploads"),
            "storage_container_input": project_doc.get("storage_container_input"),
            "storage_container_output": project_doc.get("storage_container_output"),
            # Search resources
            "search_service_name": project_doc.get("search_service_name"),
            "search_index_name": project_doc.get("search_index_name"),
            "search_datasource_name": project_doc.get("search_datasource_name"),
            "search_indexer_name": project_doc.get("search_indexer_name"),
            # Function app resources
            "function_app_name": function_app_name,
            "function_app_url": function_app_url,
            "function_names": project_doc.get(
                "function_names", ["maturity_assessment", "executive_summary"]
            ),
            # Add other necessary non-sensitive fields here
        }
        logging.info(f"Returning function config for project {project_id}")
        return jsonify(config_data), 200

    except Exception as e:
        logging.error(f"Error fetching project function config for {project_id}: {e}")
        return (
            jsonify({"error": "Failed to fetch project function configuration."}),
            500,
        )


@bp.route("/api/projects/<project_id>", methods=["DELETE"])
async def delete_project(project_id):
    """Deletes a project and all its associated Azure resources."""
    await cosmos_db_ready.wait()  # Ensure Cosmos client is ready
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get(
        "user_principal_id", "anonymous"
    )  # Use authenticated user ID

    logging.info(f"DELETE /api/projects/{project_id} called by user {user_id}")
    logging.info(f"[DELETE PROJECT] Starting deletion process for project {project_id}")

    if not current_app.cosmos_conversation_client:
        logging.error(
            "[DELETE PROJECT] CosmosDB client not available for delete_project"
        )
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    # 1. Get project details from Cosmos DB
    try:
        logging.info(
            f"[DELETE PROJECT] Step 1: Retrieving project details from Cosmos DB for project {project_id}"
        )

        # First try to get the project from the RBAC client
        rbac_client = None
        try:
            # Import RBAC client dynamically to avoid circular imports
            rbac_routes = importlib.import_module("backend.rbac.rbac_routes")
            rbac_client = rbac_routes.rbac_client
        except (ImportError, AttributeError) as e:
            logging.warning(f"Could not import RBAC client: {e}")

        project = None
        if rbac_client:
            # Try to get the project from the RBAC client
            try:
                # First try to get all projects from RBAC
                all_projects = await rbac_client.get_projects()
                project = next(
                    (p for p in all_projects if p.get("id") == project_id), None
                )

                if project:
                    logging.info(
                        f"[DELETE PROJECT] Found project {project_id} in RBAC client"
                    )
                else:
                    # If not found, try with the get_project method
                    project = await rbac_client.get_project(project_id=project_id)
                    if project:
                        logging.info(
                            f"[DELETE PROJECT] Found project {project_id} using get_project method"
                        )
            except Exception as e:
                logging.warning(f"Error getting project from RBAC client: {e}")

        # If not found in RBAC, try the old method
        if not project:
            project = await current_app.cosmos_conversation_client.get_project(
                user_id, project_id
            )

        if not project:
            logging.error(
                f"[DELETE PROJECT] Project {project_id} not found or user {user_id} doesn't have permission to delete it"
            )
            return (
                jsonify(
                    {
                        "error": f"Project {project_id} not found or you don't have permission to delete it."
                    }
                ),
                404,
            )

        logging.info(
            f"[DELETE PROJECT] Successfully retrieved project details for {project_id}"
        )

        # Extract resource names from project document
        storage_containers = [
            project.get("storage_container_uploads"),
            project.get("storage_container_input"),
            project.get("storage_container_output"),
        ]
        search_resources = {
            "index": project.get("search_index_name"),
            "datasource": project.get("search_datasource_name"),
            "indexer": project.get("search_indexer_name"),
        }
        function_app_name = project.get("function_app_name")

        # Log the resources that will be deleted
        logging.info(f"[DELETE PROJECT] Resources to delete for project {project_id}:")
        logging.info(
            f"[DELETE PROJECT] - Storage containers: {[c for c in storage_containers if c]}"
        )
        logging.info(
            f"[DELETE PROJECT] - Search resources: index={search_resources['index']}, datasource={search_resources['datasource']}, indexer={search_resources['indexer']}"
        )
        logging.info(f"[DELETE PROJECT] - Function app: {function_app_name}")

        # Delete Azure resources via helper
        logging.info("[DELETE PROJECT] Invoking cleanup_project_resources helper")
        deletion_results = {"cosmos_db": False}
        try:
            cleanup_res = await cleanup_project_resources(project_id, user_id)
            deletion_results.update(cleanup_res)
        except Exception as cleanup_err:
            logging.error(f"[DELETE PROJECT] Error during Azure cleanup: {cleanup_err}")

        # 6. Delete project document from Cosmos DB and RBAC
        logging.info(
            f"[DELETE PROJECT] Step 6: Deleting project document from Cosmos DB and RBAC"
        )
        try:
            logging.info(
                f"[DELETE PROJECT] Attempting to delete project document for project {project_id}"
            )

            # First try to delete from RBAC if we found it there
            if rbac_client:
                try:
                    logging.info(
                        f"[DELETE PROJECT] Deleting project {project_id} from RBAC client"
                    )

                    # Get all projects from RBAC to find the one we want to delete
                    all_projects = await rbac_client.get_projects()
                    rbac_project = next(
                        (p for p in all_projects if p.get("id") == project_id), None
                    )

                    if rbac_project:
                        region = rbac_project.get("region")
                        if region:
                            logging.info(
                                f"[DELETE PROJECT] Found project {project_id} in RBAC client with region {region}"
                            )
                            success = await rbac_client.delete_project(
                                project_id, region
                            )
                            if success:
                                logging.info(
                                    f"[DELETE PROJECT] Successfully deleted project {project_id} from RBAC client"
                                )
                                deletion_results["cosmos_db"] = True
                            else:
                                logging.warning(
                                    f"[DELETE PROJECT] Failed to delete project {project_id} from RBAC client"
                                )
                        else:
                            logging.warning(
                                f"[DELETE PROJECT] Project {project_id} found in RBAC client but has no region"
                            )
                    else:
                        logging.warning(
                            f"[DELETE PROJECT] Project {project_id} not found in RBAC client"
                        )
                except Exception as rbac_e:
                    logging.error(
                        f"[DELETE PROJECT] Error deleting project {project_id} from RBAC client: {rbac_e}"
                    )

            # Also try to delete from the old system as a fallback
            try:
                await current_app.cosmos_conversation_client.delete_project(
                    user_id, project_id
                )
                deletion_results["cosmos_db"] = True
                logging.info(
                    f"[DELETE PROJECT] Successfully deleted project document for project {project_id} from old system"
                )
            except Exception as old_e:
                logging.warning(
                    f"[DELETE PROJECT] Error deleting project document from old system: {old_e}"
                )

            # If we've successfully deleted from either system, consider it a success
            if deletion_results["cosmos_db"]:
                logging.info(
                    f"[DELETE PROJECT] Successfully deleted project document for project {project_id}"
                )
            else:
                raise Exception(
                    "Failed to delete project from both RBAC and old system"
                )

        except Exception as e:
            logging.error(
                f"[DELETE PROJECT] Error deleting project document for project {project_id}: {e}"
            )
            logging.exception(
                "[DELETE PROJECT] Full exception details for project document deletion:"
            )
            return (
                jsonify(
                    {
                        "error": f"Failed to delete project document: {str(e)}",
                        "partial_results": deletion_results,
                    }
                ),
                500,
            )

        # Log summary of deletion results
        logging.info(f"[DELETE PROJECT] Deletion summary for project {project_id}:")
        logging.info(
            f"[DELETE PROJECT] - Cosmos DB document deleted: {deletion_results['cosmos_db']}"
        )
        logging.info(
            f"[DELETE PROJECT] - Storage containers deleted: {deletion_results['storage_containers']}"
        )
        logging.info(
            f"[DELETE PROJECT] - Search resources deleted: {deletion_results['search_resources']}"
        )
        logging.info(
            f"[DELETE PROJECT] - Function app deleted: {deletion_results['function_app']}"
        )
        logging.info(
            f"[DELETE PROJECT] - Event Grid topic deleted: {deletion_results['event_grid_topic']}"
        )
        logging.info(
            f"[DELETE PROJECT] - Event Grid system topic deleted: {deletion_results.get('event_grid_system_topic')}"
        )

        # Invalidate cache for this user's projects
        cache_manager.invalidate_by_prefix(f"projects:{user_id}:")
        logging.info(
            f"[DELETE PROJECT] Invalidated projects cache for user {user_id} after project deletion"
        )

        # Return success response with deletion results
        return (
            jsonify(
                {
                    "message": f"Project {project_id} deleted successfully",
                    "results": deletion_results,
                }
            ),
            200,
        )

    except Exception as e:
        logging.error(
            f"[DELETE PROJECT] Unhandled exception in delete_project for project {project_id}: {e}"
        )
        logging.exception("[DELETE PROJECT] Full exception details:")
        return jsonify({"error": str(e)}), 500


@bp.route("/api/projects/<project_id>/config", methods=["GET"])
async def get_project_config(project_id):
    """Fetches configuration details for a specific project."""
    await cosmos_db_ready.wait()  # Ensure Cosmos client is ready
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get(
        "user_principal_id", "anonymous"
    )  # Use authenticated user ID

    logging.info(f"GET /api/projects/{project_id}/config called by user {user_id}")

    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for get_project_config")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )

        if not project_doc:
            logging.warning(
                f"Project config not found for ID: {project_id} and user: {user_id}"
            )
            return (
                jsonify({"error": "Project configuration not found or access denied."}),
                404,
            )

        # Extract relevant config fields to return to the frontend
        # Adjust these fields based on what the frontend actually needs
        config_data = {
            "id": project_doc.get("id"),
            "name": project_doc.get("name"),
            "description": project_doc.get("description"),
            # Storage resources
            "storage_account_name": project_doc.get("storage_account_name"),
            "storage_container_uploads": project_doc.get("storage_container_uploads"),
            "storage_container_input": project_doc.get("storage_container_input"),
            "storage_container_output": project_doc.get("storage_container_output"),
            # Search resources
            "search_service_name": project_doc.get("search_service_name"),
            "search_index": project_doc.get(
                "search_index_name"
            ),  # Renamed key to match frontend expectation
            "search_datasource_name": project_doc.get("search_datasource_name"),
            "search_indexer_name": project_doc.get("search_indexer_name"),
            # Function app resources
            "function_app_name": project_doc.get("function_app_name"),
            "function_names": project_doc.get(
                "function_names", ["maturity_assessment", "executive_summary"]
            ),
            # Add any other necessary config fields here, e.g., from 'environment' field
            # "some_env_var": project_doc.get("environment", {}).get("SOME_VAR"),
        }
        logging.info(f"Returning config for project {project_id}")
        return jsonify(config_data), 200

    except Exception as e:
        logging.error(f"Error fetching project config for {project_id}: {e}")
        return jsonify({"error": "Failed to fetch project configuration."}), 500


@bp.route("/api/projects/<project_id>/files", methods=["GET"])
async def get_project_files(project_id):
    """Get files for a specific project from Azure Storage."""
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id", "anonymous")

    logging.info(f"GET /api/projects/{project_id}/files called by user {user_id}")

    # Check if the request is coming from a process using Azure CLI credentials
    using_azure_cli = (
        os.environ.get("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
        or request.headers.get("X-Azure-CLI-Credentials") == "true"
    )

    # Get container type from query params (uploads, input, output)
    container_type = request.args.get("container", "uploads")

    # --- Authorization & Project Config Fetch ---
    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for get_project_files")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        # Get the project document
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )

        # If not found with user_id and using Azure CLI, try to find without user_id restriction
        if not project_doc and using_azure_cli:
            logging.info(
                f"Running with Azure CLI credentials, trying to find project without user_id restriction"
            )
            try:
                # Try to get the project directly by ID without user_id restriction
                if hasattr(current_app.cosmos_conversation_client, "get_project_by_id"):
                    project_doc = (
                        await current_app.cosmos_conversation_client.get_project_by_id(
                            project_id
                        )
                    )
                else:
                    # Fall back to a direct query if the method doesn't exist
                    logging.info(
                        "get_project_by_id method not found, using direct query"
                    )
                    # Try to get all projects for all users
                    all_projects = (
                        await current_app.cosmos_conversation_client.get_projects(
                            user_id
                        )
                    )
                    project_doc = next(
                        (p for p in all_projects if p.get("id") == project_id), None
                    )
            except Exception as e:
                logging.error(f"Error finding project without user_id restriction: {e}")

        if not project_doc:
            logging.warning(
                f"Project not found for ID: {project_id} and user: {user_id}"
            )
            return jsonify({"error": "Project not found or access denied."}), 404

        # Get the appropriate container name based on the container_type
        container_name = None
        if container_type == "uploads":
            container_name = project_doc.get("storage_container_uploads")
        elif container_type == "input":
            container_name = project_doc.get("storage_container_input")
        elif container_type == "output":
            container_name = project_doc.get("storage_container_output")
        else:
            return jsonify({"error": f"Invalid container type: {container_type}"}), 400

        if not container_name:
            logging.error(
                f"Container name not found for type {container_type} in project {project_id}"
            )
            return (
                jsonify(
                    {"error": f"Container configuration missing for {container_type}"}
                ),
                500,
            )

        # Get the storage account name
        storage_account_name = project_doc.get("storage_account_name")
        if not storage_account_name:
            logging.error(f"Storage account name not found in project {project_id}")
            return jsonify({"error": "Storage account configuration missing"}), 500

        # Get the project-specific SAS token from the environment object
        environment = project_doc.get("environment", {})
        storage_account_sas_token = environment.get("STORAGE_ACCOUNT_SAS_TOKEN")
        if not storage_account_sas_token:
            logging.error(
                f"Storage account SAS token not found in project {project_id} environment"
            )
            return jsonify({"error": "Storage account SAS token missing"}), 500

        # Initialize BlobServiceClient with project-specific SAS token
        account_url = f"https://{storage_account_name}.blob.core.windows.net"
        try:
            # Make sure SAS token starts with '?'
            sas_token = storage_account_sas_token
            if not sas_token.startswith("?"):
                sas_token = f"?{sas_token}"

            logging.info(
                f"Accessing storage account {storage_account_name} for project {project_id}"
            )

            async with BlobServiceClient(
                account_url=account_url, credential=sas_token
            ) as blob_service_client:
                # Get container client
                container_client = blob_service_client.get_container_client(
                    container_name
                )

                # List blobs
                blobs = []
                async for blob in container_client.list_blobs():
                    # Convert blob to dict with relevant properties
                    blob_dict = {
                        "name": blob.name,
                        "size": blob.size,
                        "content_type": (
                            blob.content_settings.content_type
                            if blob.content_settings
                            else "application/octet-stream"
                        ),
                        "last_modified": (
                            blob.last_modified.isoformat()
                            if blob.last_modified
                            else None
                        ),
                        "indexed": False,  # Default to not indexed, will be updated later
                    }
                    blobs.append(blob_dict)

                # Try to get indexed files information if available
                try:
                    # Get search service name and index name from project
                    search_service_name = project_doc.get("search_service_name")
                    search_index_name = project_doc.get("search_index_name")

                    if search_service_name and search_index_name:
                        # Get search API key from settings
                        search_api_key = app_settings.azure_search.key

                        if search_api_key:
                            # Initialize SearchClient
                            from azure.core.credentials import AzureKeyCredential

                            # Create a SearchClient
                            search_client = SyncSearchClient(
                                endpoint=f"https://{search_service_name}.search.windows.net",
                                index_name=search_index_name,
                                credential=AzureKeyCredential(search_api_key),
                            )

                            # Get indexed files (just IDs)
                            indexed_files = []
                            try:
                                # Search for all documents, but only retrieve IDs
                                results = search_client.search(
                                    "*", select="id", top=1000
                                )
                                for result in results:
                                    # Extract filename from ID (assuming ID is the filename)
                                    filename = result.get("id")
                                    if filename:
                                        indexed_files.append(filename)

                                # Update indexed status for each blob
                                for blob in blobs:
                                    if blob["name"] in indexed_files:
                                        blob["indexed"] = True
                            except Exception as search_error:
                                logging.error(
                                    f"Error getting indexed files: {search_error}"
                                )
                except Exception as index_error:
                    logging.error(f"Error checking indexed status: {index_error}")

                return jsonify(blobs), 200
        except KeyError:
            logging.error(
                "AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable not set"
            )
            return (
                jsonify({"error": "Storage authentication configuration missing"}),
                500,
            )
        except Exception as e:
            logging.error(f"Error listing blobs for project {project_id}: {e}")
            return jsonify({"error": f"Failed to list files: {str(e)}"}), 500
    except Exception as e:
        logging.error(f"Error in get_project_files for project {project_id}: {e}")
        return jsonify({"error": f"Failed to get project files: {str(e)}"}), 500


@bp.route("/api/projects/<project_id>/files/<path:filename>", methods=["GET"])
async def download_project_file(project_id, filename):
    """Download a specific file from a project's storage container."""
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id", "anonymous")

    logging.info(
        f"GET /api/projects/{project_id}/files/{filename} called by user {user_id}"
    )

    # Check if the request is coming from a process using Azure CLI credentials
    using_azure_cli = (
        os.environ.get("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
        or request.headers.get("X-Azure-CLI-Credentials") == "true"
    )

    # Get container type from query params (uploads, input, output)
    container_type = request.args.get("container", "uploads")

    # --- Authorization & Project Config Fetch ---
    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for download_project_file")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        # Get the project document
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )

        # If not found with user_id and using Azure CLI, try to find without user_id restriction
        if not project_doc and using_azure_cli:
            logging.info(
                f"Running with Azure CLI credentials, trying to find project without user_id restriction"
            )
            try:
                # Try to get the project directly by ID without user_id restriction
                if hasattr(current_app.cosmos_conversation_client, "get_project_by_id"):
                    project_doc = (
                        await current_app.cosmos_conversation_client.get_project_by_id(
                            project_id
                        )
                    )
                else:
                    # Fall back to a direct query if the method doesn't exist
                    logging.info(
                        "get_project_by_id method not found, using direct query"
                    )
                    # Try to get all projects for all users
                    all_projects = (
                        await current_app.cosmos_conversation_client.get_projects(
                            user_id
                        )
                    )
                    project_doc = next(
                        (p for p in all_projects if p.get("id") == project_id), None
                    )
            except Exception as e:
                logging.error(f"Error finding project without user_id restriction: {e}")

        if not project_doc:
            logging.warning(
                f"Project not found for ID: {project_id} and user: {user_id}"
            )
            return jsonify({"error": "Project not found or access denied."}), 404

        # Get the appropriate container name based on the container_type
        container_name = None
        if container_type == "uploads":
            container_name = project_doc.get("storage_container_uploads")
        elif container_type == "input":
            container_name = project_doc.get("storage_container_input")
        elif container_type == "output":
            container_name = project_doc.get("storage_container_output")
        else:
            return jsonify({"error": f"Invalid container type: {container_type}"}), 400

        if not container_name:
            logging.error(
                f"Container name not found for type {container_type} in project {project_id}"
            )
            return (
                jsonify(
                    {"error": f"Container configuration missing for {container_type}"}
                ),
                500,
            )

        # Get the storage account name
        storage_account_name = project_doc.get("storage_account_name")
        if not storage_account_name:
            logging.error(f"Storage account name not found in project {project_id}")
            return jsonify({"error": "Storage account configuration missing"}), 500

        # Get the project-specific SAS token from the environment object
        environment = project_doc.get("environment", {})
        storage_account_sas_token = environment.get("STORAGE_ACCOUNT_SAS_TOKEN")
        if not storage_account_sas_token:
            logging.error(
                f"Storage account SAS token not found in project {project_id} environment"
            )
            return jsonify({"error": "Storage account SAS token missing"}), 500

        # Initialize BlobServiceClient with project-specific SAS token
        account_url = f"https://{storage_account_name}.blob.core.windows.net"
        try:
            # Make sure SAS token starts with '?'
            sas_token = storage_account_sas_token
            if not sas_token.startswith("?"):
                sas_token = f"?{sas_token}"

            logging.info(
                f"Accessing storage account {storage_account_name} for project {project_id} to download {filename}"
            )

            async with BlobServiceClient(
                account_url=account_url, credential=sas_token
            ) as blob_service_client:
                # Get container client
                container_client = blob_service_client.get_container_client(
                    container_name
                )

                # Get blob client for the specific file
                blob_client = container_client.get_blob_client(filename)

                # Check if the blob exists
                try:
                    properties = await blob_client.get_blob_properties()
                except Exception as e:
                    logging.error(f"Error getting blob properties for {filename}: {e}")
                    return jsonify({"error": f"File not found: {filename}"}), 404

                # Download the blob
                download_stream = await blob_client.download_blob()
                blob_data = await download_stream.readall()

                # Create a response with the blob data
                response = await make_response(blob_data)

                # Set content type if available
                content_type = properties.content_settings.content_type
                if content_type:
                    response.headers["Content-Type"] = content_type
                else:
                    # Try to guess content type from filename
                    import mimetypes

                    guessed_type = mimetypes.guess_type(filename)[0]
                    if guessed_type:
                        response.headers["Content-Type"] = guessed_type
                    else:
                        response.headers["Content-Type"] = "application/octet-stream"

                # Set content disposition to attachment with the filename
                response.headers["Content-Disposition"] = (
                    f"attachment; filename={filename}"
                )

                # Set content length
                response.headers["Content-Length"] = str(properties.size)

                return response

        except KeyError:
            logging.error(
                "AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable not set"
            )
            return (
                jsonify({"error": "Storage authentication configuration missing"}),
                500,
            )
        except Exception as e:
            logging.error(f"Error downloading blob for project {project_id}: {e}")
            return jsonify({"error": f"Failed to download file: {str(e)}"}), 500
    except Exception as e:
        logging.error(f"Error in download_project_file for project {project_id}: {e}")
        return jsonify({"error": f"Failed to download project file: {str(e)}"}), 500


@bp.route("/api/projects/<project_id>/upload", methods=["POST"])
async def upload_project_files(project_id):
    """Handles file uploads for a specific project."""
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id", "anonymous")

    logging.info(f"POST /api/projects/{project_id}/upload called by user {user_id}")

    # --- Authorization & Project Config Fetch ---
    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for upload_project_files")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )
        if not project_doc:
            logging.warning(
                f"Project not found or access denied for ID: {project_id}, user: {user_id}"
            )
            return jsonify({"error": "Project not found or access denied."}), 404

        upload_container_name = project_doc.get("storage_container_uploads")
        if not upload_container_name:
            logging.error(
                f"Project {project_id} is missing 'storage_container_uploads' configuration."
            )
            return (
                jsonify(
                    {
                        "error": "Project configuration is incomplete (missing upload container)."
                    }
                ),
                500,
            )

    except Exception as e:
        logging.error(
            f"Error fetching project config during upload for {project_id}: {e}"
        )
        return jsonify({"error": "Failed to retrieve project configuration."}), 500

    # --- File Handling ---
    files = await request.files
    if not files:
        return jsonify({"error": "No files provided in the request."}), 400

    uploaded_files = []
    errors = []

    # --- Blob Service Client Initialization ---
    # Use SAS token from .env for authentication
    account_url = (
        f"https://{app_settings.azure_storage.account_name}.blob.core.windows.net"
    )
    try:
        sas_token = os.environ["AZURE_STORAGE_CONTAINER_SAS_TOKEN"]
        async with BlobServiceClient(
            account_url=account_url, credential=sas_token
        ) as blob_service_client:
            # --- Upload Each File ---
            for file_key in files:
                file = files[file_key]
                filename = file.filename
                if not filename:
                    logging.warning("Skipping file with no filename.")
                    continue

                logging.info(
                    f"Attempting to upload '{filename}' to container '{upload_container_name}' for project {project_id}"
                )
                try:
                    blob_client = blob_service_client.get_blob_client(
                        container=upload_container_name, blob=filename
                    )
                    content = (
                        file.read()
                    )  # Removed await, as file.read() is likely synchronous
                    await blob_client.upload_blob(content, overwrite=True)
                    uploaded_files.append(filename)
                    logging.info(
                        f"Successfully uploaded '{filename}' to '{upload_container_name}'."
                    )
                except Exception as e:
                    logging.error(
                        f"Failed to upload '{filename}' to container '{upload_container_name}': {e}"
                    )
                    errors.append({"filename": filename, "error": str(e)})

    except Exception as e:
        logging.error(
            f"Failed to initialize BlobServiceClient or DefaultAzureCredential: {e}"
        )
        return (
            jsonify({"error": f"Failed to connect to storage service: {str(e)}"}),
            500,
        )

    # --- Trigger WebSocket Update ---
    if uploaded_files:
        try:
            # Trigger update for the 'uploads' container (used by FileManagement.tsx)
            await trigger_project_update(project_id, "uploads")
            logging.info(
                f"Triggered WebSocket update for 'uploads' container, project {project_id}"
            )
        except Exception as trigger_e:
            # Log the error but don't fail the entire request if the trigger fails
            logging.error(
                f"Failed to trigger WebSocket update for project {project_id} after upload: {trigger_e}"
            )

    # --- Response ---
    if errors:
        if not uploaded_files:  # All failed
            return (
                jsonify({"error": "Failed to upload any files.", "details": errors}),
                500,
            )
        else:  # Partial success
            return (
                jsonify(
                    {
                        "message": "Completed upload process with some errors.",
                        "uploaded": uploaded_files,
                        "errors": errors,
                    }
                ),
                207,
            )  # Multi-Status
    else:
        return (
            jsonify(
                {
                    "message": "All files uploaded successfully.",
                    "uploaded": uploaded_files,
                }
            ),
            200,
        )


@bp.route("/api/projects/<project_id>/resources", methods=["POST"])
async def update_project_resources(project_id):
    """Updates the project in CosmosDB with the actual resource names from Azure deployment."""
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id", "anonymous")

    # Check if the request is coming from a process using Azure CLI credentials
    using_azure_cli = (
        os.environ.get("USE_AZURE_CLI_CREDENTIAL", "false").lower() == "true"
        or request.headers.get("X-Azure-CLI-Credentials") == "true"
    )

    logging.info(f"POST /api/projects/{project_id}/resources called by user {user_id}")

    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for update_project_resources")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        # Get the project document
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )

        # If not found with user_id and using Azure CLI, try to find without user_id restriction
        if not project_doc and using_azure_cli:
            logging.info(
                f"Running with Azure CLI credentials, trying to find project without user_id restriction"
            )
            try:
                # Try to get the project directly by ID without user_id restriction
                if hasattr(current_app.cosmos_conversation_client, "get_project_by_id"):
                    project_doc = (
                        await current_app.cosmos_conversation_client.get_project_by_id(
                            project_id
                        )
                    )
                else:
                    # Fall back to a direct query if the method doesn't exist
                    parameters = [{"name": "@projectId", "value": project_id}]
                    query = (
                        "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
                    )
                    projects = []
                    async for (
                        item
                    ) in current_app.cosmos_conversation_client.container_client.query_items(
                        query=query, parameters=parameters
                    ):
                        projects.append(item)

                    if projects:
                        project_doc = projects[0]
            except Exception as e:
                logging.error(f"Error finding project without user_id restriction: {e}")

        if not project_doc:
            logging.warning(
                f"Project not found for ID: {project_id} and user: {user_id}"
            )
            return jsonify({"error": "Project not found or access denied."}), 404

        # Get the resource data from the request
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 415

        request_json = await request.get_json()

        # Update the project document with the resource names
        updated = False

        # Log the incoming resource data
        logging.info(
            f"Received resource data for project {project_id}: {json.dumps(request_json, indent=2)}"
        )

        # Storage account name
        if (
            "storage_account_name" in request_json
            and request_json["storage_account_name"]
        ):
            project_doc["storage_account_name"] = request_json["storage_account_name"]
            updated = True
            logging.info(
                f"Updated storage_account_name to {request_json['storage_account_name']} for project {project_id}"
            )

        # Container names - these are critical for project functionality
        if "uploads_container" in request_json and request_json["uploads_container"]:
            project_doc["storage_container_uploads"] = request_json["uploads_container"]
            updated = True
            logging.info(
                f"Updated storage_container_uploads to {request_json['uploads_container']} for project {project_id}"
            )

        if "input_container" in request_json and request_json["input_container"]:
            project_doc["storage_container_input"] = request_json["input_container"]
            updated = True
            logging.info(
                f"Updated storage_container_input to {request_json['input_container']} for project {project_id}"
            )

        if "output_container" in request_json and request_json["output_container"]:
            project_doc["storage_container_output"] = request_json["output_container"]
            updated = True
            logging.info(
                f"Updated storage_container_output to {request_json['output_container']} for project {project_id}"
            )

        # Search service name
        if (
            "search_service_name" in request_json
            and request_json["search_service_name"]
        ):
            project_doc["search_service_name"] = request_json["search_service_name"]
            updated = True
            logging.info(
                f"Updated search_service_name to {request_json['search_service_name']} for project {project_id}"
            )

        # Function app name
        if "function_app_name" in request_json and request_json["function_app_name"]:
            project_doc["function_app_name"] = request_json["function_app_name"]
            updated = True
            logging.info(
                f"Updated function_app_name to {request_json['function_app_name']} for project {project_id}"
            )

        # Function names
        if "function_names" in request_json and request_json["function_names"]:
            project_doc["function_names"] = request_json["function_names"]
            updated = True
            logging.info(
                f"Updated function_names to {request_json['function_names']} for project {project_id}"
            )

        # Update the project document if any changes were made
        if updated:
            # Update the timestamp
            project_doc["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update the project document in CosmosDB
            await current_app.cosmos_conversation_client.update_project(
                user_id, project_id, project_doc
            )

            # Invalidate cache for this user's projects
            cache_manager.invalidate_by_prefix(
                f"projects:{user_id}"
            )  # Simplified cache key prefix
            logging.info(
                f"Invalidated projects cache for user {user_id} after resource update"
            )

            return (
                jsonify(
                    {
                        "message": "Project resources updated successfully",
                        "project_id": project_id,
                        "updated_fields": [
                            k for k in request_json.keys() if k in project_doc
                        ],
                    }
                ),
                200,
            )
        else:
            return (
                jsonify(
                    {
                        "message": "No changes made to project resources",
                        "project_id": project_id,
                    }
                ),
                200,
            )

    except Exception as e:
        logging.error(f"Error updating project resources for {project_id}: {e}")
        return jsonify({"error": f"Failed to update project resources: {str(e)}"}), 500


async def create_support_ticket(project_id):
    """Creates a support ticket for deployment issues."""
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id", "anonymous")

    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for create_support_ticket")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        # Get the project document
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )

        if not project_doc:
            logging.warning(
                f"Project not found for ID: {project_id} and user: {user_id}"
            )
            return jsonify({"error": "Project not found or access denied."}), 404

        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 415

        request_json = await request.get_json()
        email = request_json.get("email")
        error_details = request_json.get("error")
        resource_statuses = request_json.get("resourceStatuses", [])

        if not email or not error_details:
            return jsonify({"error": "Email and error details are required"}), 400

        # Get deployment status from project document
        deployment_status = project_doc.get("deployment_status", {})

        # Create ticket in Cosmos DB
        ticket_id = str(uuid.uuid4())
        ticket_data = {
            "id": ticket_id,
            "type": "support_ticket",
            "project_id": project_id,
            "project_name": project_doc.get("name", "Unknown Project"),
            "user_id": user_id,
            "email": email,
            "error_details": error_details,
            "resource_statuses": resource_statuses,
            "deployment_status": deployment_status,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "status": "open",
        }

        # Save ticket to Cosmos DB
        await current_app.cosmos_conversation_client.create_document(ticket_data)

        # Send email notification (this would be implemented with a proper email service)
        try:
            # Log the ticket details for now - in production, this would send an email
            logging.info(
                f"SUPPORT TICKET CREATED: ID={ticket_id}, Project={project_id}, Email={email}"
            )
            logging.info(f"ERROR DETAILS: {error_details}")
            logging.info(f"RESOURCE STATUSES: {resource_statuses}")
            logging.info(f"DEPLOYMENT STATUS: {deployment_status}")

            # In a production environment, you would send an email here
            # Example: await send_support_email(email, ticket_id, error_details, project_doc)
        except Exception as email_error:
            logging.error(f"Failed to send support ticket notification: {email_error}")
            # Continue processing even if email fails

        # Update project with ticket reference
        if "support_tickets" not in project_doc:
            project_doc["support_tickets"] = []

        project_doc["support_tickets"].append(
            {
                "ticket_id": ticket_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "status": "open",
            }
        )

        await current_app.cosmos_conversation_client.update_project(
            user_id, project_id, project_doc
        )

        return (
            jsonify(
                {
                    "message": "Support ticket created successfully",
                    "ticket_id": ticket_id,
                }
            ),
            201,
        )

    except Exception as e:
        logging.error(f"Error creating support ticket for project {project_id}: {e}")
        return jsonify({"error": "Failed to create support ticket."}), 500


@bp.route("/api/projects/<project_id>/upload-template", methods=["POST"])
async def upload_project_template_files(project_id):
    """Handles template file uploads (e.g., .xlsx, .docx) for a specific project to the INPUT container."""
    await cosmos_db_ready.wait()
    authenticated_user = await get_authenticated_user_details(request_headers=request.headers)
    user_id = authenticated_user.get("user_principal_id", "anonymous")

    logging.info(
        f"POST /api/projects/{project_id}/upload-template called by user {user_id}"
    )

    # --- Authorization & Project Config Fetch ---
    if not current_app.cosmos_conversation_client:
        logging.error("CosmosDB client not available for upload_project_template_files")
        return (
            jsonify({"error": "Configuration service (CosmosDB) is not available."}),
            503,
        )

    try:
        project_doc = await current_app.cosmos_conversation_client.get_project(
            user_id, project_id
        )
        if not project_doc:
            logging.warning(
                f"Project not found or access denied for ID: {project_id}, user: {user_id}"
            )
            return jsonify({"error": "Project not found or access denied."}), 404

        # *** Get the INPUT container name ***
        input_container_name = project_doc.get("storage_container_input")
        if not input_container_name:
            logging.error(
                f"Project {project_id} is missing 'storage_container_input' configuration."
            )
            return (
                jsonify(
                    {
                        "error": "Project configuration is incomplete (missing input container)."
                    }
                ),
                500,
            )

    except Exception as e:
        logging.error(
            f"Error fetching project config during template upload for {project_id}: {e}"
        )
        return jsonify({"error": "Failed to retrieve project configuration."}), 500

    # --- File Handling ---
    files = await request.files
    if not files:
        return jsonify({"error": "No files provided in the request."}), 400

    uploaded_files = []
    errors = []

    # --- Blob Service Client Initialization ---
    account_url = (
        f"https://{app_settings.azure_storage.account_name}.blob.core.windows.net"
    )
    try:
        sas_token = os.environ["AZURE_STORAGE_CONTAINER_SAS_TOKEN"]
        async with BlobServiceClient(
            account_url=account_url, credential=sas_token
        ) as blob_service_client:
            # --- Upload Each File ---
            for file_key in files:
                file = files[file_key]
                filename = file.filename
                if not filename:
                    logging.warning("Skipping template file with no filename.")
                    continue

                # *** Upload to the INPUT container ***
                logging.info(
                    f"Attempting to upload template '{filename}' to INPUT container '{input_container_name}' for project {project_id}"
                )
                try:
                    blob_client = blob_service_client.get_blob_client(
                        container=input_container_name, blob=filename
                    )
                    content = (
                        file.read()
                    )  # Removed await, as file.read() is likely synchronous
                    await blob_client.upload_blob(content, overwrite=True)
                    uploaded_files.append(filename)
                    logging.info(
                        f"Successfully uploaded template '{filename}' to '{input_container_name}'."
                    )
                except Exception as e:
                    logging.error(
                        f"Failed to upload template '{filename}' to container '{input_container_name}': {e}"
                    )
                    errors.append({"filename": filename, "error": str(e)})

    except Exception as e:
        logging.error(
            f"Failed to initialize BlobServiceClient or DefaultAzureCredential for template upload: {e}"
        )
        return (
            jsonify({"error": f"Failed to connect to storage service: {str(e)}"}),
            500,
        )

    # --- Trigger WebSocket Update for INPUT container ---
    if uploaded_files:
        try:
            # Trigger update for the 'input' container (used by RightFileManagement.tsx)
            await trigger_project_update(project_id, "input")
            logging.info(
                f"Triggered WebSocket update for 'input' container, project {project_id}"
            )
        except Exception as trigger_e:
            # Log the error but don't fail the entire request if the trigger fails
            logging.error(
                f"Failed to trigger WebSocket update for input container, project {project_id} after template upload: {trigger_e}"
            )

    # --- Response ---
    if errors:
        if not uploaded_files:  # All failed
            return (
                jsonify(
                    {"error": "Failed to upload any template files.", "details": errors}
                ),
                500,
            )
        else:  # Partial success
            return (
                jsonify(
                    {
                        "message": "Completed template upload process with some errors.",
                        "uploaded": uploaded_files,
                        "errors": errors,
                    }
                ),
                207,
            )  # Multi-Status
    else:
        return (
            jsonify(
                {
                    "message": "All template files uploaded successfully.",
                    "uploaded": uploaded_files,
                }
            ),
            200,
        )


@bp.route("/debug/roles")
def debug_roles():
    header = request.headers.get("X-MS-CLIENT-PRINCIPAL")
    if not header:
        return jsonify({"error": "no X-MS-CLIENT-PRINCIPAL header"}), 401

    try:
        decoded = base64.b64decode(header)
        principal = json.loads(decoded)
    except Exception as e:
        return jsonify({"error": "malformed header", "details": str(e)}), 400

    # extract all the roles
    roles = [
        claim["val"]
        for claim in principal.get("claims", [])
        if claim.get("typ", "").endswith("/roles")
    ]
    return jsonify(
        {"all_claims": principal.get("claims", []), "extracted_roles": roles}
    )

@bp.route("/debug/auth-headers")
def debug_auth_headers():
    """Debug endpoint to see all authentication-related headers"""
    auth_headers = {}

    # Get all headers that might be related to authentication
    for header_name, header_value in request.headers:
        if any(keyword in header_name.lower() for keyword in ['auth', 'principal', 'token', 'user', 'client']):
            auth_headers[header_name] = header_value

    # Also check for specific Easy Auth headers
    easy_auth_headers = {
        'X-MS-CLIENT-PRINCIPAL': request.headers.get('X-MS-CLIENT-PRINCIPAL'),
        'X-MS-CLIENT-PRINCIPAL-IDP': request.headers.get('X-MS-CLIENT-PRINCIPAL-IDP'),
        'X-MS-CLIENT-PRINCIPAL-NAME': request.headers.get('X-MS-CLIENT-PRINCIPAL-NAME'),
        'X-MS-TOKEN-AAD-ID-TOKEN': request.headers.get('X-MS-TOKEN-AAD-ID-TOKEN'),
        'Authorization': request.headers.get('Authorization'),
    }

    return jsonify({
        "all_auth_headers": auth_headers,
        "easy_auth_headers": easy_auth_headers,
        "development_mode": os.getenv("DEVELOPMENT_MODE", "false"),
        "use_entra_auth": os.getenv("USE_ENTRA_AUTH", "false")
    })


app = create_app()
