#!/usr/bin/env python3
"""
Direct Cosmos DB update utility for deployment resources.
This script provides functions to update project resources directly in Cosmos DB
without going through the API endpoint.
"""

import os
import json
import logging
import uuid
from datetime import datetime, timezone
from azure.cosmos import CosmosClient, exceptions
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Cosmos DB connection details from environment variables
COSMOS_ENDPOINT = os.environ.get("COSMOS_ENDPOINT")
COSMOS_KEY = os.environ.get("COSMOS_KEY")
COSMOS_DATABASE = os.environ.get("COSMOS_DATABASE")
COSMOS_CONTAINER = os.environ.get("COSMOS_CONTAINER")

# Fallback to standard AZURE_COSMOSDB_* variables if the direct variables are missing
if not COSMOS_ENDPOINT:
    account = os.environ.get("AZURE_COSMOSDB_ACCOUNT", "")
    if account:
        COSMOS_ENDPOINT = f"https://{account}.documents.azure.com:443/"

if not COSMOS_KEY:
    COSMOS_KEY = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY", "")

if not COSMOS_DATABASE:
    COSMOS_DATABASE = os.environ.get("AZURE_COSMOSDB_DATABASE", "ai-scope")

if not COSMOS_CONTAINER:
    COSMOS_CONTAINER = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "conversations")

def init_cosmos_client():
    """Initialize the Cosmos DB client."""
    try:
        if not COSMOS_ENDPOINT or not COSMOS_KEY:
            logger.error("Cosmos DB endpoint or key not set in environment variables")
            return None

        client = CosmosClient(COSMOS_ENDPOINT, credential=COSMOS_KEY)
        logger.info(f"Successfully created CosmosDB client for endpoint {COSMOS_ENDPOINT}")

        database = client.get_database_client(COSMOS_DATABASE)
        logger.info(f"Successfully connected to database {COSMOS_DATABASE}")

        container = database.get_container_client(COSMOS_CONTAINER)
        logger.info(f"Successfully connected to container {COSMOS_CONTAINER}")

        return container
    except Exception as e:
        logger.error(f"Error initializing Cosmos DB client: {e}")
        return None

def update_project_resources_direct(project_id, resource_data):
    """
    Update project resources directly in Cosmos DB.

    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names

    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        # Validate project_id
        if not project_id or project_id == "11111111-1111-1111-1111-111111111111":
            logger.error("Invalid project ID for update")
            return False

        # Initialize Cosmos DB client
        container = init_cosmos_client()
        if not container:
            logger.error("Failed to initialize Cosmos DB client")
            return False

        # Query for the project
        query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
        parameters = [{"name": "@projectId", "value": project_id}]

        projects = list(container.query_items(
            query=query,
            parameters=parameters,
            enable_cross_partition_query=True
        ))

        if not projects:
            logger.error(f"Project {project_id} not found in database")
            return False

        project = projects[0]
        logger.info(f"Found project {project_id}")

        # Get the region from the project document
        region = project.get("region")
        if not region:
            logger.warning(f"No region found in project {project_id}, using default 'westeurope'")
            region = "westeurope"  # Default region if not found

        # Ensure region is not being used as project_id
        if region == project_id:
            logger.warning(f"Region parameter ({region}) matches project_id, this is likely an error. Using 'westeurope' as default.")
            region = "westeurope"

        # Update the project with resource data
        updated = False
        for key, value in resource_data.items():
            if key in project and project[key] != value:
                project[key] = value
                updated = True
            elif key not in project:
                project[key] = value
                updated = True

        if not updated:
            logger.info(f"No changes needed for project {project_id}")
            return True

        # Update timestamp
        project["updated_at"] = datetime.now(timezone.utc).isoformat()

        # Update the project in Cosmos DB with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # Update without partition key (since partition_key parameter is not supported in this SDK version)
                logger.info(f"Attempt {attempt+1}/{max_retries}: Updating project {project_id}")
                container.replace_item(
                    item=project_id,
                    body=project
                )
                logger.info(f"Successfully updated project {project_id}")
                return True
            except Exception as e:
                logger.error(f"Error on attempt {attempt+1}/{max_retries}: {e}")

                # Wait before retrying
                if attempt < max_retries - 1:
                    import time
                    time.sleep(1)  # 1 second delay between retries
                else:
                    logger.error(f"Failed to update project {project_id} after {max_retries} attempts")
                    import traceback
                    logger.error(f"Stack trace: {traceback.format_exc()}")
                    return False

        return False

    except Exception as e:
        logger.error(f"Error updating project resources: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    # Test the function
    import sys
    if len(sys.argv) < 3:
        print("Usage: python direct_cosmos_update.py <project_id> <resource_data_json>")
        sys.exit(1)

    project_id = sys.argv[1]
    resource_data = json.loads(sys.argv[2])

    result = update_project_resources_direct(project_id, resource_data)
    print(f"Update result: {result}")
