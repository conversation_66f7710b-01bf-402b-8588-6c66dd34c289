#!/usr/bin/env python3

import asyncio
import json
import logging
import os
import sys
import aiohttp
import argparse
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


async def update_project_resources(
    project_id, resource_data, api_url="http://localhost:50505"
):
    """
    Update the project in CosmosDB with the actual resource names from Azure deployment.

    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names
        api_url (str): The base URL of the API

    Returns:
        bool: True if update was successful, False otherwise
    """
    logger.info(f"Updating project {project_id} with resource information")
    logger.info(f"Resource data: {json.dumps(resource_data, indent=2)}")

    # Endpoint for updating project resources
    update_url = f"{api_url}/api/projects/{project_id}/resources"

    try:
        async with aiohttp.ClientSession() as session:
            # Send the update
            logger.info(f"Sending resource update to {update_url}")
            async with session.post(
                update_url,
                json=resource_data,
                headers={
                    "X-Azure-CLI-Credentials": "true",
                    "Content-Type": "application/json",
                },
            ) as response:
                if response.status != 200:
                    response_text = await response.text()
                    logger.error(
                        f"Error updating project resources: {response.status} - {response_text}"
                    )
                    return False
                else:
                    response_json = await response.json()
                    logger.info(
                        f"Successfully updated project resources: {json.dumps(response_json, indent=2)}"
                    )
                    return True
    except Exception as e:
        logger.error(f"Error updating project resources: {e}")
        return False


async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Update project resources in CosmosDB")
    parser.add_argument("project_id", help="The UUID of the project")
    parser.add_argument(
        "--api-url", default="http://localhost:50505", help="Base URL of the API"
    )
    parser.add_argument("--storage-account", help="Storage account name")
    parser.add_argument("--search-service", help="Search service name")
    parser.add_argument("--function-app", help="Function app name")
    parser.add_argument(
        "--function-names", help="Comma-separated list of function names"
    )

    args = parser.parse_args()

    # Build resource data dictionary from arguments
    resource_data = {}
    if args.storage_account:
        resource_data["storage_account_name"] = args.storage_account
    if args.search_service:
        resource_data["search_service_name"] = args.search_service
    if args.function_app:
        resource_data["function_app_name"] = args.function_app
    if args.function_names:
        resource_data["function_names"] = args.function_names.split(",")

    success = await update_project_resources(
        args.project_id, resource_data, args.api_url
    )

    if success:
        logger.info(
            f"Successfully updated project {args.project_id} with resource information"
        )
        sys.exit(0)
    else:
        logger.error(
            f"Failed to update project {args.project_id} with resource information"
        )
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
