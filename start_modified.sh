#!/bin/bash

# Silent mode for installation steps
echo "Starting installation process (silent mode)..."

# Restore frontend npm packages silently
cd frontend
npm install > /dev/null 2>&1
npm install uuid @types/uuid chart.js react-chartjs-2 @azure/storage-blob xlsx > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Failed to restore frontend npm packages"
    exit $?
fi

# Build frontend silently
npm run build > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Failed to build frontend"
    exit $?
fi

cd ..
. ./scripts/loadenv.sh > /dev/null 2>&1

# Set environment variables for local development
export DEPLOYMENT_ENV="development"

# Configure logging levels
# Completely disable RBAC logs in terminal
export RBAC_CONSOLE_LOG_LEVEL="CRITICAL"
export DISABLE_RBAC_HTTP_LOGS="true"
# Disable HTTP server logs completely
export QUART_ACCESS_LOGGING="0"
export QUART_DEBUG="0"
# Show CosmosDB logs in terminal (INFO level)
export COSMOS_CONSOLE_LOG_LEVEL="INFO"
# Set default log level for other components
export DEFAULT_CONSOLE_LOG_LEVEL="INFO"

# Install Python dependencies silently
echo "Installing Python dependencies (silent mode)..."
./.venv/bin/pip install -r requirements.txt > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Failed to install Python dependencies"
    exit $?
fi

# Create a custom logging configuration file
cat > quart_logging.ini << EOF
[loggers]
keys=root,quart,hypercorn

[handlers]
keys=console

[formatters]
keys=generic

[logger_root]
level=INFO
handlers=console
qualname=

[logger_quart]
level=WARNING
handlers=console
qualname=quart
propagate=0

[logger_hypercorn]
level=WARNING
handlers=console
qualname=hypercorn
propagate=0

[handler_console]
class=StreamHandler
formatter=generic
args=(sys.stdout,)

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S %z
EOF

echo "Starting main backend with WebSocket support"
echo "Logging configuration:"
echo "- RBAC console logs: DISABLED"
echo "- RBAC HTTP logs: DISABLED"
echo "- HTTP server logs: DISABLED"
echo "- CosmosDB console logs: ${COSMOS_CONSOLE_LOG_LEVEL}"
echo "- Default console logs: ${DEFAULT_CONSOLE_LOG_LEVEL}"

# Set API_PORT environment variable for consistency
export API_PORT=50506

# Start the server with custom logging configuration
PYTHONPATH=. ./.venv/bin/python -m quart run --port=${API_PORT} --host=0.0.0.0 --reload