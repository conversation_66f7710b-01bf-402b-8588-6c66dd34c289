# Multi-Project Architecture

This document describes the multi-project architecture of the AI Scope application, focusing on how it supports multiple projects with isolated resources.

## Multi-Tenancy Model

The AI Scope application implements a hybrid multi-tenancy model:

1. **Shared Infrastructure**
   - Single application instance serving all users
   - Shared Azure services (Storage Account, Search Service, Cosmos DB)
   - Common authentication and authorization system

2. **Isolated Resources**
   - Project-specific blob containers
   - Project-specific search indexes
   - Project-specific function apps
   - Isolated data and processing

This approach balances resource efficiency with strong data isolation.

## project Isolation

### Data Isolation

Each project (project) has its own isolated data resources:

1. **Storage Isolation**
   - Dedicated blob containers per project
   - Access controlled via Azure RBAC
   - No cross-project data access

2. **Search Isolation**
   - Dedicated search indexes per project
   - Separate datasources and indexers
   - Query results limited to project data

3. **Processing Isolation**
   - Dedicated function apps per project
   - Project-specific configuration
   - Isolated compute resources

### Configuration Isolation

Project configurations are stored in Cosmos DB with isolation:

1. **Project Metadata**
   - Each project has its own configuration document
   - Access controlled via application logic
   - Contains all project-specific resource names

2. **User Access Control**
   - User-project relationships defined in Cosmos DB
   - Role-based permissions per project
   - Access checks on all API endpoints

## Resource Naming Conventions

To ensure isolation and organization, the application uses consistent naming conventions:

1. **Blob Containers**
   - `project-{sanitized-name}-{unique-suffix}-uploads`
   - `project-{sanitized-name}-{unique-suffix}-input`
   - `project-{sanitized-name}-{unique-suffix}-output`

2. **Search Resources**
   - `project-{sanitized-name}-index`
   - `project-{sanitized-name}-ds`
   - `project-{sanitized-name}-indexer`

3. **Function Apps**
   - `func-{sanitized-name}-{unique-suffix}`

The unique suffix ensures global uniqueness and prevents naming conflicts.

## User and Role Management

The application implements a hierarchical role system:

1. **Super Admin**
   - Global access to all projects and users
   - Can create and manage regions
   - Full system administration

2. **Regional Admin**
   - Access to all projects in assigned region
   - Can manage users in their region
   - Can create projects in their region

3. **Regular Owner**
   - Access to specific projects they own
   - Can upload and manage content
   - Can use chat functionality
   - Cannot change project settings

> **Access Requirement**: Project creation and the deployment of project
> resources can only be performed by a **Super Admin** or a
> **Regional Admin** for the target region.


## API Isolation

All API endpoints implement project isolation:

1. **Project-Specific Endpoints**
   - `/api/projects/{projectId}/...` pattern
   - Project ID validation on every request
   - User permission checks for the specific project

2. **Resource Context**
   - Backend fetches project configuration from Cosmos DB
   - Uses project-specific resource names for operations
   - Ensures operations only affect the correct resources

## Cost Management

The multi-project architecture includes cost management features:

1. **Resource Allocation**
   - Each project has dedicated resources
   - Resource usage can be tracked per project

2. **Cost Limits**
   - Administrators can set cost limits per project
   - Usage monitoring prevents exceeding limits

3. **Resource Optimization**
   - Shared infrastructure reduces overall costs
   - Function apps can scale based on usage

## Scaling Considerations

The architecture is designed to scale efficiently:

1. **Horizontal Scaling**
   - Application can be deployed across multiple instances
   - Load balancing distributes requests

2. **Resource Scaling**
   - Storage accounts can handle thousands of containers
   - Search service can manage hundreds of indexes
   - Cosmos DB scales to handle large numbers of documents

3. **Limitations**
   - Azure Search has service limits on indexes per service
   - Storage accounts have transaction limits
   - Function apps have execution limits

## Security Boundaries

The multi-project architecture implements several security boundaries:

1. **Authentication Boundary**
   - Azure AD (Entra ID) authentication for all users
   - Token validation on all requests

2. **Authorization Boundary**
   - Role-based access checks on all operations
   - Project-specific permission validation

3. **Resource Boundary**
   - Azure RBAC controls access to Azure resources
   - Managed Identities for secure service access

4. **Network Boundary**
   - Private endpoints for sensitive services (optional)
   - Network security groups for traffic control
