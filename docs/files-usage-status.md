# Files Usage Status in AI Scope App

This document provides an overview of all files in the project, categorized by their usage status.

## Actively Used Files

### Core Application Files
- `app.py` - Main application entry point
- `run.py` - Script to run the application
- `requirements.txt` - Python dependencies
- `requirements-dev.txt` - Development Python dependencies
- `logging_config.py` - Logging configuration

### Frontend Files
- `frontend/index.html` - Main HTML template
- `frontend/src/index.tsx` - Main React entry point
- `frontend/src/App.tsx` - Main React component
- `frontend/src/main.tsx` - Alternative entry point
- `frontend/src/api/` - API client code
- `frontend/src/components/` - React components
- `frontend/src/pages/` - React pages
- `frontend/src/state/` - State management
- `frontend/src/hooks/` - React hooks
- `frontend/src/contexts/` - React contexts
- `frontend/src/utils/` - Utility functions
- `frontend/src/models/` - TypeScript models
- `frontend/src/constants/` - Constants
- `frontend/src/services/` - Service classes
- `frontend/package.json` - Frontend dependencies
- `frontend/tsconfig.json` - TypeScript configuration
- `frontend/vite.config.ts` - Vite configuration

### Backend Files
- `backend/aisearch/` - AI Search functionality
- `backend/auth/` - Authentication functionality
- `backend/azure/` - Azure integration
- `backend/cache/` - Caching functionality
- `backend/history/` - History management
- `backend/models/` - Data models
- `backend/rbac/` - Role-based access control
- `backend/routes/` - API routes
- `backend/security/` - Security functionality
- `backend/services/` - Service classes
- `backend/utils/` - Utility functions
- `backend/web_sockets/` - WebSocket functionality
- `backend/settings.py` - Backend settings

### Deployment Files
- `deploy_project_resources.py` - Project resource deployment
- `project_resources.bicep` - Legacy Bicep template (replaced by SDK-based deployment)
- `modules/` - Legacy Bicep modules for deployment
- `infra/` - Infrastructure as code files
- `azure.yaml` - Azure deployment configuration

### Startup Scripts
- `start.sh` - Shell script to start the application
- `start.cmd` - Windows command script to start the application
- `start_deep.sh` - Deep cleanup and start script
- `start_with_azure_cli.sh` - Start script with Azure CLI

### PriorityPlot Application
- `priorityplot-main - Copy/server.py` - PriorityPlot server
- `priorityplot-main - Copy/src/` - PriorityPlot frontend
- `priorityplot-main - Copy/requirements.txt` - PriorityPlot dependencies
- `priorityplot-main - Copy/start.cmd` - PriorityPlot startup script

### Documentation
- `docs/` - Documentation files
- `README.md` - Main readme file

## Occasionally Used Files

### Scripts
- `scripts/` - Various utility scripts
- `scripts/data_preparation.py` - Data preparation script
- `scripts/deploy_project_functions.py` - Function deployment script
- `scripts/run_cost_analysis.sh` - Cost analysis script
- `scripts/run_batch_create_index.py` - Batch index creation
- `scripts/auth_init.sh` - Authentication initialization
- `scripts/auth_update.sh` - Authentication update
- `scripts/loadenv.sh` - Environment loading script

### Deployment Utilities
- `deploy_conversations_project_container.sh` - Container deployment
- `create_conversations_project_container.py` - Container creation
- `setup_rbac_containers.py` - RBAC container setup
- `check_rbac_data.py` - RBAC data check
- `fix_rbac.py` - RBAC fix script
- `fix_project_deployment_status.py` - Deployment status fix
- `fix_project_retrieval.py` - Project retrieval fix

## Additional Files

### Occasionally Used Deployment Files
- `auto_deploy_functions.py` - Used for deploying Azure Functions for projects
- `function_app.py` - Used for Azure Functions implementation
- `function_app_deployment.bicep` - Legacy template for Function App deployment
- `function_app_deployment_infra.bicep` - Legacy infrastructure template
- `function_app_params.json` - Parameters for Function App deployment

### Configuration Files
- `env.json` - Used for deploying app settings to Azure App Service
- `host.json` - Used for Azure Functions configuration
- `local.settings.json` - Used for local development of Azure Functions


### Additional Documentation
- `README_azd.md` - Azure Developer CLI deployment instructions

### Build Artifacts
- `static/` - Build artifacts, generated files
- `__pycache__/` - Python cache directories
- `*.pyc` - Compiled Python files
- `*.log` - Log files

### Temporary or Test Files
- `deployment_*.log` - Deployment log files
- `project_creation_test.log` - Test log file

## Notes

1. This classification is based on code analysis and may not be 100% accurate.
2. Some files may be used indirectly or through imports not explicitly detected.
3. Configuration files may be used at runtime even if not directly referenced in code.
4. The PriorityPlot application appears to be a separate component that is integrated with the main application.
5. Some scripts in the `scripts/` directory may be used for development, testing, or occasional maintenance tasks.

