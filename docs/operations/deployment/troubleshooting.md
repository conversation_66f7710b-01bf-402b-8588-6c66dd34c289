# troubleshooting.md (New)

Goal: Guide for diagnosing common deployment issues.
Content:
Log Locations: `logs/` directory (deployment), Azure Portal (Activity Log, Deployments), Function App Logs.
Common Issues & Steps:
- Permissions: Check roles (`az role assignment list`).
- Deployment Errors: Use `az deployment group validate`, check Azure Portal deployment errors.
- <PERSON><PERSON>t Errors (exit code 127, etc.): Verify dependencies (az, jq, bash), check script permissions (`chmod +x`).
- Hangs: Check Azure Portal deployment status, run `az` commands manually with `--debug`.
- API Errors (Connection refused): Ensure backend `app.py` is running.
- ACR Issues: Check ACR existence/permissions (`az acr show`), check image tag (`az acr repository show-tags`).
Rollback: Explain that automatic rollback is limited; failed deployments often require manual cleanup (using Azure Portal or `az resource delete`). The `failed/partial_success` status indicates issues.
Recovery: Explain use of `force_update=true` on status GET API or recovery scripts (`force_update_*.py`) for stuck statuses.
