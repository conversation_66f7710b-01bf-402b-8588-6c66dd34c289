# README.md (Updated)

Goal: Quick entry point for deployment operations.
Content:
Briefly describe the automated, multi-stage project deployment process (API trigger -> Background Task -> Python SDK/ACR -> Status Updates).
> **Access Requirement**: Deployment operations, including creating projects, require a **Super Admin** or a **Regional Admin** for the relevant region.
Key Links:
- [Deployment Process Flow](./deployment-process.md)
- [Deployment Configuration](./deployment-configuration.md)
- [Function App Deployment (ACR)](./function-app-deployment.md)
- [Deployment Status & Monitoring](./deployment-status.md)
- [Deployment Scripts & IaC](./scripts-and-iac.md)
- [Troubleshooting Guide](./troubleshooting.md)
- [Test Scenarios](./test-scenarios.md)
