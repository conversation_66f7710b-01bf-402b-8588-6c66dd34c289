# function-app-deployment.md (New)

Goal: Explain the ACR-based Function App part of the deployment.
Content:
Approach: Deploying the Function App via container image from ACR (functionappaiscope/functionapp:latest).
Trigger: The deployment logic in `deploy_project_resources.py` runs after the main infrastructure is created.
Implementation: `WebSiteManagementClient` creates the Function App using the Azure Python SDK.
Parameters: A parameters file generated by the Python code passes project ID, ACR details, and service names to the template.
Functions Included: Briefly list the key functions included in the container image (e.g., Event Grid trigger, HTTP triggers).
