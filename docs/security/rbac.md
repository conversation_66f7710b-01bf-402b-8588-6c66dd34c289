# Role-Based Access Control (RBAC)

This document provides a comprehensive overview of the Role-Based Access Control (RBAC) system implemented in the AI Scope application.

## Table of Contents

- [Overview](#overview)
- [Role Definitions](#role-definitions)
- [Authentication Methods](#authentication-methods)
- [RBAC Architecture](#rbac-architecture)
- [Permission Enforcement](#permission-enforcement)
- [Data Access Control](#data-access-control)
- [API Endpoints](#api-endpoints)
- [Hierarchical Structure](#hierarchical-structure)
- [Implementation Details](#implementation-details)

## Overview

The AI Scope application implements a comprehensive Role-Based Access Control (RBAC) system that manages user permissions based on roles. The system is integrated with Azure Cosmos DB for data storage and uses FastAPI for the RBAC API endpoints.

The RBAC system ensures that users can only access resources they are authorized to use, based on their role and explicit assignments. This provides a secure multi-tenant environment where different organizations and teams can use the application without accessing each other's data.

## Role Definitions

The system defines three main user roles:

| Role | Description | Access Level |
|------|-------------|--------------|
| **SUPER_ADMIN** | Global administrator | Access to all resources across all regions |
| **REGIONAL_ADMIN** | Regional administrator | Access to resources within their assigned region |
| **REGULAR_USER** | Standard user | Access only to specific projects they're assigned to |

Only users in the **SUPER_ADMIN** role or a **REGIONAL_ADMIN** for the target region can create projects and deploy their resources.

Additionally, users can have specific roles within projects and teams:

**Project Roles:**
- **OWNER**: Full control over the project
- **CONTRIBUTOR**: Can contribute to the project but cannot manage access
- **VIEWER**: Read-only access to the project

**Team Roles:**
- **LEADER**: Manages the team and its members
- **MEMBER**: Belongs to the team but cannot manage it

## Authentication Methods

The application supports two authentication methods:

### Development Authentication

For development and testing, the application uses a mock authentication system implemented in `backend/auth/mock_auth.py`:
- Predefined mock users with different roles
- Simple JWT-based tokens issued locally
- No external dependencies

### Production Authentication

In production, the application uses Azure AD (Entra ID) authentication:
- Users authenticate through Azure AD using MSAL in the frontend
- Tokens are sent to the backend in the `Authorization` header
- `get_authenticated_user` in `backend/rbac/rbac_routes.py` extracts the token and validates it via Microsoft Graph
- EasyAuth headers can also be used when the app service provides them
- The frontend keeps the access token in `localStorage` under `accessToken`
- `ALLOW_CLIENT_CREDENTIALS_FALLBACK` controls whether the backend accepts requests without a bearer token


## RBAC Architecture

The RBAC system consists of several key components:

### 1. RBAC Models

Defined in `backend/models/rbac.py`, these Pydantic models define the structure of:
- Users
- Regions
- Teams
- Projects
- Role assignments

### 2. RBAC Client

The `CosmosRbacClient` class in `backend/rbac/cosmosdb_rbac_service.py` handles:
- RBAC data storage and retrieval
- Permission checks
- Data filtering based on user roles

### 3. RBAC API

A separate FastAPI application in `backend/rbac/rbac_routes.py` provides:
- User management endpoints
- Region management endpoints
- Team management endpoints
- Project management endpoints
- Role assignment endpoints

### 4. Integration with Main Application

The RBAC system is integrated with the main application in `app.py`:
- RBAC API requests are routed to the RBAC FastAPI app
- The RBAC client is initialized during application startup
- Authentication middleware validates user credentials

## Permission Enforcement

Permissions are enforced at multiple levels:

### 1. API Level

Each API endpoint checks if the user has the required permissions:

```python
# Example: Only super admins can create regions
if current_user["role"] != UserRole.SUPER_ADMIN.value:
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Only super admins can create regions"
    )
```

### 2. Data Access Level

The `CosmosRbacClient` filters data based on user permissions:

```python
# Example: Get projects based on user role
if current_user['role'] == UserRole.SUPER_ADMIN.value:
    # Super admins can see all projects
    return await self.get_projects()
elif current_user['role'] == UserRole.REGIONAL_ADMIN.value:
    # Regional admins can see all projects in their region
    return await self.get_projects(region)
```

### 3. Frontend Level

The frontend application conditionally renders UI elements based on user permissions:

```jsx
{/* Example: Only show admin features to admins */}
{isAdmin && <AdminPanel />}
```

## Data Access Control

The RBAC system implements fine-grained data access control:

### Super Admin Access

Super admins can access:
- All users across all regions
- All regions
- All teams across all regions
- All projects across all regions

### Regional Admin Access

Regional admins can access:
- Users in their region (except Super Admins)
- Their assigned region
- Teams in their region
- Projects in their region

### Regular User Access

Regular users can access:
- Users in their teams
- Teams they are members of
- Projects they are directly assigned to
- Projects their teams are assigned to

## API Endpoints

The RBAC API provides the following endpoints:

### User Management

- `GET /api/rbac/users`: Get accessible users
- `POST /api/rbac/users`: Create a new user
- `GET /api/rbac/users/{user_id}`: Get a specific user
- `PUT /api/rbac/users/{user_id}`: Update a user
- `DELETE /api/rbac/users/{user_id}`: Delete a user

### Region Management

- `GET /api/rbac/regions`: Get accessible regions
- `POST /api/rbac/regions`: Create a new region
- `GET /api/rbac/regions/{region_id}`: Get a specific region
- `PUT /api/rbac/regions/{region_id}`: Update a region
- `DELETE /api/rbac/regions/{region_id}`: Delete a region

### Team Management

- `GET /api/rbac/teams`: Get accessible teams
- `POST /api/rbac/teams`: Create a new team
- `GET /api/rbac/teams/{team_id}`: Get a specific team
- `PUT /api/rbac/teams/{team_id}`: Update a team
- `DELETE /api/rbac/teams/{team_id}`: Delete a team
- `GET /api/rbac/teams/{team_id}/members`: Get team members
- `POST /api/rbac/teams/{team_id}/members`: Add a member to a team
- `DELETE /api/rbac/teams/{team_id}/members/{user_id}`: Remove a member from a team

### Project Management

- `GET /api/rbac/projects`: Get accessible projects
- `POST /api/rbac/projects`: Create a new project
- `GET /api/rbac/projects/{project_id}`: Get a specific project
- `PUT /api/rbac/projects/{project_id}`: Update a project
- `DELETE /api/rbac/projects/{project_id}`: Delete a project
- `GET /api/rbac/projects/{project_id}/users`: Get project users
- `POST /api/rbac/projects/{project_id}/users`: Add a user to a project
- `DELETE /api/rbac/projects/{project_id}/users/{user_id}`: Remove a user from a project
- `GET /api/rbac/projects/{project_id}/teams`: Get project teams
- `POST /api/rbac/projects/{project_id}/teams`: Add a team to a project
- `DELETE /api/rbac/projects/{project_id}/teams/{team_id}`: Remove a team from a project

## Hierarchical Structure

The RBAC system implements a hierarchical structure:

```
Region
├── Teams
│   ├── Team Members (Users)
│   └── Team Leaders (Users)
└── Projects
    ├── Project Teams
    └── Project Users
```

This hierarchy allows for:
- Organizational separation through regions
- Team-based collaboration
- Project-specific access control

## Implementation Details

### Database Schema

The RBAC data is stored in Cosmos DB with the following containers:

- **users**: Stores user information
- **regions**: Stores region information
- **teams**: Stores team information
- **projects**: Stores project information
- **role_assignments**: Stores role assignments (user-team, user-project, team-project)

### Key Files

- `backend/models/rbac.py`: RBAC data models
- `backend/rbac/cosmosdb_rbac_service.py`: RBAC data access and permission logic
- `backend/rbac/rbac_routes.py`: RBAC API endpoints
- `backend/auth/mock_auth.py`: Development authentication
- `app.py`: RBAC integration with main application

### Security Considerations

- All RBAC API endpoints require authentication
- Permission checks are performed on every operation
- Data is filtered based on user permissions before being returned
- Azure AD integration provides secure authentication in production
- Cosmos DB access is secured with appropriate credentials

## Current Shortcomings

While the RBAC implementation covers most permission scenarios it has a few limitations:

1. Permission checks are scattered across route functions which can lead to maintenance overhead.

2. Optional fallback to mock authentication can bypass RBAC if `ALLOW_CLIENT_CREDENTIALS_FALLBACK` is enabled.

3. The frontend sometimes assumes a Super Admin role when it cannot reach the backend, which can confuse users.

## Conclusion

The RBAC system in the AI Scope application provides a comprehensive and flexible permission model that ensures users can only access resources they are authorized to use. By combining role-based permissions with explicit assignments, the system supports complex organizational structures while maintaining security and data isolation.
