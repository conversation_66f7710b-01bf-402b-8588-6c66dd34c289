import React from 'react';
import {
  IconButton,
  IContextualMenuProps,
  ContextualMenu,
  DirectionalHint,
  IIconProps
} from '@fluentui/react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../state/UserProvider';
import { UserRole, getRolePermissions } from '../models/roles';

interface ProjectActionsMenuProps {
  projectId: string;
  projectName: string;
  onEdit?: () => void;
  onDelete?: () => void;
  onAssignUsers?: () => void;
  onSetCostLimit?: () => void;
}

const moreIcon: IIconProps = { iconName: 'MoreVertical' };

const ProjectActionsMenu: React.FC<ProjectActionsMenuProps> = ({
  projectId,
  projectName,
  onEdit,
  onDelete,
  onAssignUsers,
  onSetCostLimit
}) => {
  const [showContextualMenu, setShowContextualMenu] = React.useState(false);
  const [contextualMenuTarget, setContextualMenuTarget] = React.useState<HTMLElement | null>(null);
  const navigate = useNavigate();
  const { currentUser } = useUser();

  // Get permissions based on user role
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);

  const onShowContextualMenu = (e: React.MouseEvent<HTMLElement>): void => {
    setContextualMenuTarget(e.currentTarget);
    setShowContextualMenu(true);
  };

  const onHideContextualMenu = (): void => {
    setShowContextualMenu(false);
  };

  const handleViewProject = (): void => {
    navigate(`/project/${projectId}`);
    onHideContextualMenu();
  };

  const handleEditProject = (): void => {
    if (onEdit) {
      onEdit();
    }
    onHideContextualMenu();
  };

  const handleDeleteProject = (): void => {
    if (onDelete) {
      onDelete();
    }
    onHideContextualMenu();
  };

  const handleAssignUsers = (): void => {
    if (onAssignUsers) {
      onAssignUsers();
    }
    onHideContextualMenu();
  };

  const handleSetCostLimit = (): void => {
    if (onSetCostLimit) {
      onSetCostLimit();
    }
    onHideContextualMenu();
  };

  // Build menu items based on user role
  const buildMenuItems = () => {
    const items = [];

    // Always show "Open Project" for all users
    items.push({
      key: 'viewProject',
      text: 'Open Project',
      iconProps: { iconName: 'OpenFile' },
      onClick: handleViewProject
    });

    // For regular users, only show "Open Project"
    if (currentUser?.role === UserRole.REGULAR_USER) {
      return items;
    }

    // For admin users (Super Admin and Regional Admin), show all options
    items.push(
      {
        key: 'divider1',
        itemType: 1 // Divider
      },
      {
        key: 'editProject',
        text: 'Edit Project',
        iconProps: { iconName: 'Edit' },
        onClick: handleEditProject,
        disabled: !permissions.canEditProject
      },
      {
        key: 'assignUsers',
        text: 'Assign Users',
        iconProps: { iconName: 'People' },
        onClick: handleAssignUsers,
        disabled: !permissions.canAssignUsers
      },
      {
        key: 'setCostLimit',
        text: 'Set Cost Limit',
        iconProps: { iconName: 'Money' },
        onClick: handleSetCostLimit,
        disabled: !permissions.canSetCostLimits
      },
      {
        key: 'divider2',
        itemType: 1 // Divider
      },
      {
        key: 'deleteProject',
        text: 'Delete Project',
        iconProps: { iconName: 'Delete' },
        onClick: handleDeleteProject,
        disabled: !permissions.canDeleteProject
      }
    );

    return items;
  };

  const menuProps: IContextualMenuProps = {
    items: buildMenuItems(),
    directionalHint: DirectionalHint.bottomRightEdge,
    onDismiss: onHideContextualMenu
  };

  return (
    <>
      <IconButton
        iconProps={moreIcon}
        title="Project Actions"
        ariaLabel="Project Actions"
        onClick={onShowContextualMenu}
        styles={{ root: { height: 32, width: 32 } }}
      />
      {showContextualMenu && contextualMenuTarget && (
        <ContextualMenu
          target={contextualMenuTarget}
          onDismiss={onHideContextualMenu}
          items={menuProps.items}
          directionalHint={menuProps.directionalHint}
        />
      )}
    </>
  );
};

export default ProjectActionsMenu;
