import React, { useContext, useEffect, useState, useRef, createContext } from 'react';
import { BlobServiceClient } from '@azure/storage-blob';
import { Link, Outlet, useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>alog,
  Stack,
  TextField,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Text
} from '@fluentui/react';
import { CopyRegular } from '@fluentui/react-icons';

import { CosmosDBStatus } from '../../api';
import CompanyLogo from '../../assets/keyrus-2.svg';
import { HistoryButton, LogoutButton } from '../../components/common/Button';
import { AppStateContext } from '../../state/AppProvider';
import styles from './Layout.module.css'; // Reuse the same styles
import { getStorageConfig, StorageConfig } from '../../services/configService';
import * as WebSocketManager from '../../services/websocketManager';
import { performLogout } from '../../services/authService';
import { LanguageSelector, LanguageOption } from '../../components/LanguageSelector';

// Project context to store project-specific environment variables
export const ProjectContext = createContext<{
  projectId: string | null;
  projectName: string | null;
  projectEnv: Record<string, string>;
  configVersion: string;
  region: string;
  searchIndex: string;
  searchServiceName: string;
  searchApiKey: string;
  storageAccountName: string;
  storageAccountSasToken: string;
  storageContainerUploads: string;
  storageContainerInput: string;
  storageContainerOutput: string;
  functionAppName: string;
  functionAppUrl: string;
  functionKeyMaturity: string;
  functionKeyExecutiveSummary: string;
  functionKeyPowerPoint: string;
  selectedLanguage: string;
  systemPrompt: string;
  setSelectedLanguage: (language: string, systemPrompt: string) => void;
}>({
  projectId: null,
  projectName: null,
  projectEnv: {},
  configVersion: '1.0.0',
  region: '',
  searchIndex: '',
  searchServiceName: '',
  searchApiKey: '',
  storageAccountName: '',
  storageAccountSasToken: '',
  storageContainerUploads: '',
  storageContainerInput: '',
  storageContainerOutput: '',
  functionAppName: '',
  functionAppUrl: '',
  functionKeyMaturity: '',
  functionKeyExecutiveSummary: '',
  functionKeyPowerPoint: '',
  selectedLanguage: 'en',
  systemPrompt: 'You are an AI IT sales assistant, helping keyrus analyse companies. Always respond in English only.',
  setSelectedLanguage: () => {}
});

const ProjectLayout: React.FC = () => {
  // Get project ID from route params
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();

  // Convert projectId from string | undefined to string | null for the context
  const contextProjectId = projectId || null;

  // State Hooks
  const [isSharePanelOpen, setIsSharePanelOpen] = useState<boolean>(false);
  const [copyClicked, setCopyClicked] = useState<boolean>(false);
  const [copyText, setCopyText] = useState<string>('Copy URL');
  const [shareLabel, setShareLabel] = useState<string | undefined>('Share');
  const [hideHistoryLabel, setHideHistoryLabel] = useState<string>('Hide chat history');
  const [showHistoryLabel, setShowHistoryLabel] = useState<string>('Show chat history');
  const [logo, setLogo] = useState<string>(CompanyLogo);
  const [storageConfig, setStorageConfig] = useState<StorageConfig>({
    account_name: '',
    container_name: '',
    container_sas_token: ''
  });
  const [uploadStatus, setUploadStatus] = useState<{ success: boolean; message?: string }>({
    success: false,
  });
  const [isBannerVisible, setIsBannerVisible] = useState<boolean>(true);

  // Project-specific states
  const [projectName, setProjectName] = useState<string | null>(null);
  const [projectEnv, setProjectEnv] = useState<Record<string, string>>({});
  const [projectLoaded, setProjectLoaded] = useState<boolean>(false);
  const [projectError, setProjectError] = useState<string | null>(null);

  // Language selector states
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [systemPrompt, setSystemPrompt] = useState<string>('You are an AI IT sales assistant, helping keyrus analyse companies. Always respond in English only.');

  // Available languages
  const languages: LanguageOption[] = [
    {
      key: 'en',
      text: 'English',
      systemPrompt: 'You are an AI IT sales assistant, helping keyrus analyse companies. Always respond in English only.'
    },
    {
      key: 'fr',
      text: 'French',
      systemPrompt: 'Vous êtes un assistant commercial informatique IA, aidant keyrus à analyser les entreprises. Répondez toujours uniquement en français.'
    },
    {
      key: 'es',
      text: 'Spanish',
      systemPrompt: 'Eres un asistente de ventas de TI con IA, ayudando a keyrus a analizar empresas. Responde siempre solo en español.'
    },
    {
      key: 'nl',
      text: 'Dutch',
      systemPrompt: 'U bent een AI IT-verkoopmedewerker die keyrus helpt bij het analyseren van bedrijven. Antwoord altijd alleen in het Nederlands.'
    }
  ];

  // Handle language change
  const handleLanguageChange = (language: string, prompt: string) => {
    setSelectedLanguage(language);
    setSystemPrompt(prompt);

    // Update the environment variables with the new system prompt
    setProjectEnv(prev => ({
      ...prev,
      AZURE_OPENAI_SYSTEM_MESSAGE: prompt
    }));
  };

  // Context and Refs
  const appStateContext = useContext(AppStateContext);
  const ui = appStateContext?.state.frontendSettings?.ui;
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Set the current project ID in the WebSocket manager
  useEffect(() => {
    if (projectId) {
      console.log(`Setting current project in WebSocket manager: ${projectId}`);
      WebSocketManager.setCurrentProject(projectId);
    }

    return () => {
      // Clear the current project when unmounting
      WebSocketManager.setCurrentProject(null);
    };
  }, [projectId]);

  // Load project config from backend
  useEffect(() => {
    const loadProjectConfig = async () => {
      if (!projectId) {
        setProjectError('Project ID is missing');
        return;
      }

      try {
        // Import the getAuthHeaders function
        const { getAuthHeaders } = await import('../../services/authHeaderService');

        // Get authentication headers
        const authHeaders = await getAuthHeaders(['User.Read', 'openid', 'offline_access']);

        console.log('Fetching project with auth headers:', authHeaders);

        // Use the RBAC API endpoint for project configuration with auth headers
        const response = await fetch(`/api/rbac/projects/${projectId}`, {
          headers: {
            ...authHeaders
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const config = await response.json();

        // Log the received config for debugging
        console.log("Received project config:", config);

        // Validate the name field
        if (!config.name) {
          console.error("Invalid config received:", config); // Log the invalid config
          throw new Error('Invalid project configuration - missing name');
        }

        // Set project name
        setProjectName(config.name);

        // Create a comprehensive environment object with all variables from the project document
        const envVars = {
          // Project metadata
          PROJECT_ID: config.id || '',
          PROJECT_NAME: config.name || '',
          PROJECT_DESCRIPTION: config.description || '',
          REGION: config.region || '',

          // Storage-related variables
          STORAGE_ACCOUNT_NAME: config.storage_account_name || '',
          STORAGE_CONTAINER_UPLOADS: config.storage_container_uploads || '',
          STORAGE_CONTAINER_INPUT: config.storage_container_input || '',
          STORAGE_CONTAINER_OUTPUT: config.storage_container_output || '',

          // Search-related variables
          SEARCH_SERVICE_NAME: config.search_service_name || '',
          SEARCH_INDEX: config.search_index_name || '',
          SEARCH_DATASOURCE_NAME: config.search_datasource_name || '',
          SEARCH_INDEXER_NAME: config.search_indexer_name || '',

          // Function-related variables
          FUNCTION_APP_NAME: config.function_app_name || '',
          FUNCTION_APP_URL: config.function_app_url || '',

          // Include all environment variables from the environment object
          ...(config.environment || {})
        };

        // Log the environment variables for debugging
        console.log('Project environment variables:', {
          projectId: config.id,
          projectName: config.name,
          storageAccountName: config.storage_account_name,
          storageContainers: {
            uploads: config.storage_container_uploads,
            input: config.storage_container_input,
            output: config.storage_container_output
          },
          searchService: {
            name: config.search_service_name,
            index: config.search_index_name,
            apiKey: envVars.AZURE_SEARCH_KEY ? 'Present' : 'Missing'
          },
          sasToken: envVars.STORAGE_ACCOUNT_SAS_TOKEN ? 'Present' : 'Missing'
        });

        setProjectEnv(envVars);

        setProjectLoaded(true);
        setProjectError(null);
      } catch (error) {
        console.error('Error loading project config:', error);
        let errorMessage = 'Failed to load project configuration';
        try {
          if (error instanceof Error) {
            if (error.message.includes('Invalid project configuration')) {
              // This is a newly created project that hasn't been fully deployed yet
              errorMessage = 'Project is being deployed - Please wait a few minutes and refresh';
            } else if (error.message.includes('HTTP')) {
              const status = error.message.match(/status: (\d+)/)?.[1] || 'unknown';
              if (status === '404') {
                errorMessage = 'Project not found - It may have been deleted or you may not have access';
              } else {
                errorMessage = `Configuration service error (HTTP ${status}) - Verify Cosmos DB connection`;
              }
            } else if (error.message.includes('project ID is missing')) {
              errorMessage = 'Missing project ID in URL - Return to project selector';
            }
          }
        } catch (e) {
          console.error('Error handling error:', e);
        }
        setProjectError(errorMessage);
        setProjectLoaded(false);
      }
    };

    loadProjectConfig();
  }, [projectId]);

  const handleLogoutClick = async () => {
    try {
      if (projectId) {
        WebSocketManager.closeProjectWebSockets(projectId);
        WebSocketManager.setCurrentProject(null);
      }

      sessionStorage.removeItem('currentProjectId');
      sessionStorage.removeItem('currentProjectName');

      await performLogout();
    } catch (error) {
      console.error('Logout failed:', error);
      window.location.href = '/';
    }
  };

  const handleBackToProjects = () => {
    // Close all WebSocket connections for this project before navigating away
    if (projectId) {
      console.log(`Closing WebSocket connections for project ${projectId} before navigation`);
      WebSocketManager.closeProjectWebSockets(projectId);

      // Clear the current project in the WebSocket manager
      WebSocketManager.setCurrentProject(null);
    }

    // Navigate to projects page
    navigate('/projects');
  };

  // Reuse the rest of the functionality from Layout.tsx
  // ... (copy other methods from Layout.tsx as needed)

  const handleShareClick = () => {
    setIsSharePanelOpen(true);
  };

  const handleSharePanelDismiss = () => {
    setIsSharePanelOpen(false);
    setCopyClicked(false);
    setCopyText('Copy URL');
  };

  const handleCopyClick = () => {
    navigator.clipboard.writeText(window.location.href);
    setCopyClicked(true);
    setCopyText('Copied!');
  };

  const handleHistoryClick = () => {
    // Match the original Layout.tsx implementation
    appStateContext?.dispatch({ type: 'TOGGLE_CHAT_HISTORY' });
  };

  // Effect: Set Logo on Load
  useEffect(() => {
    if (!appStateContext?.state.isLoading) {
      setLogo(ui?.logo || CompanyLogo);
    }
  }, [appStateContext?.state.isLoading, ui?.logo]);

  // Effect: Update Copy Text
  useEffect(() => {
    if (copyClicked) {
      setCopyText('Copied URL');
    }
  }, [copyClicked]);

  // Effect: Clean up WebSocket connections when component unmounts
  useEffect(() => {
    // Return cleanup function
    return () => {
      // Close all WebSocket connections for this project when component unmounts
      if (projectId) {
        console.log(`Closing WebSocket connections for project ${projectId} on component unmount`);
        WebSocketManager.closeProjectWebSockets(projectId);

        // Clear the current project in the WebSocket manager
        WebSocketManager.setCurrentProject(null);
      }
    };
  }, [projectId]);

  // Render the layout with project context
  return (
    <ProjectContext.Provider value={{
      projectId: contextProjectId,
      projectName,
      projectEnv,
      configVersion: '1.0.0',
      region: projectEnv.REGION || '',
      searchIndex: projectEnv.SEARCH_INDEX || '',
      searchServiceName: projectEnv.SEARCH_SERVICE_NAME || '',
      searchApiKey: projectEnv.AZURE_SEARCH_KEY || '',
      storageAccountName: projectEnv.STORAGE_ACCOUNT_NAME || '',
      storageAccountSasToken: projectEnv.STORAGE_ACCOUNT_SAS_TOKEN || '',
      storageContainerUploads: projectEnv.STORAGE_CONTAINER_UPLOADS || '',
      storageContainerInput: projectEnv.STORAGE_CONTAINER_INPUT || '',
      storageContainerOutput: projectEnv.STORAGE_CONTAINER_OUTPUT || '',
      functionAppName: projectEnv.FUNCTION_APP_NAME || '',
      functionAppUrl: projectEnv.FUNCTION_APP_URL || '',
      functionKeyMaturity: projectEnv.FUNCTION_KEY_MATURITY || '',
      functionKeyExecutiveSummary: projectEnv.FUNCTION_KEY_EXECUTIVE_SUMMARY || '',
      functionKeyPowerPoint: projectEnv.FUNCTION_KEY_POWERPOINT || '',
      selectedLanguage,
      systemPrompt,
      setSelectedLanguage: handleLanguageChange
    }}>
      <div className={styles.layout}>
        {projectError ? (
          <Stack className={styles.errorContainer}>
            <MessageBar
              messageBarType={MessageBarType.error}
              isMultiline={true}
              dismissButtonAriaLabel="Close"
            >
              {projectError}
            </MessageBar>
            <DefaultButton
              text="Back to Projects"
              onClick={handleBackToProjects}
              className={styles.backButton}
            />
          </Stack>
        ) : !projectLoaded ? (
          <Stack className={styles.loadingContainer}>
            <Text>Loading project...</Text>
          </Stack>
        ) : (
          <>
            <header className={styles.header} style={{ paddingTop: '15px', paddingBottom: '10px' }}>
              <div className={styles.headerContainer}>
                <div className={styles.headerTitleContainer}>
                  <img src={logo} alt="Company Logo" className={styles.headerIcon} />
                  <Text variant="large" className={styles.headerTitle} style={{ marginLeft: '15px' }}>
                    {projectName}
                  </Text>
                </div>
                <div style={{ flexGrow: 1 }}></div>
                <Stack horizontal tokens={{ childrenGap: 8 }} className={styles.headerButtonsContainer} style={{ marginRight: 20 }}>
                  <DefaultButton
                    text="Back to Projects"
                    onClick={handleBackToProjects}
                  />
                  <HistoryButton onClick={handleHistoryClick} text="History" />
                  <LogoutButton onClick={handleLogoutClick} />
                </Stack>
              </div>
            </header>

            <main className={styles.mainContentArea}>
              <Outlet />
            </main>

            {/* Share dialog */}
            <Dialog
              hidden={!isSharePanelOpen}
              onDismiss={handleSharePanelDismiss}
              dialogContentProps={{
                title: 'Share this chat',
                subText: 'Copy this link to share this chat with others',
              }}
            >
              <TextField
                value={window.location.href}
                readOnly
                className={styles.urlTextBox}
                styles={{ fieldGroup: { backgroundColor: '#F5F5F5' } }}
              />
              <Stack horizontal tokens={{ childrenGap: 10 }}>
                <DefaultButton
                  style={{ marginTop: 20 }}
                  onClick={handleCopyClick}
                  iconProps={copyClicked ? undefined : { iconName: 'Copy' }}
                  className={styles.copyButtonContainer}
                >
                  <span className={styles.copyButtonText}>{copyText}</span>
                </DefaultButton>
              </Stack>
            </Dialog>
          </>
        )}
      </div>
    </ProjectContext.Provider>
  );
};

export default ProjectLayout;
