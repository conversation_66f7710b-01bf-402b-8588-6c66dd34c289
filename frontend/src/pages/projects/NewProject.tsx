import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>ack,
  Text,
  TextField,
  PrimaryButton,
  DefaultButton,
  MessageBar,
  MessageBarType,
  ProgressIndicator,
  Icon,
  Label,
  IconButton,
  Dropdown,
  IDropdownOption,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../state/UserProvider';
import { UserRole } from '../../models/roles';
import userContextService from '../../services/userContextService';
import styles from './NewProject.module.css';

// Define the deployment resources that will be created
interface DeploymentResource {
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  timestamp?: string;
}

const NewProject: React.FC = () => {
  const { currentUser } = useUser();
  const [projectName, setProjectName] = useState('');
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [regions, setRegions] = useState<IDropdownOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingRegions, setIsLoadingRegions] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createdProjectId, setCreatedProjectId] = useState<string | null>(null);
  const [showDeployment, setShowDeployment] = useState(false);
  const [deploymentError, setDeploymentError] = useState<string | null>(null);
  const [hasDeploymentError, setHasDeploymentError] = useState(false);
  const [isSendingTicket, setIsSendingTicket] = useState(false);
  const [ticketSent, setTicketSent] = useState(false);
  const [deploymentResources, setDeploymentResources] = useState<DeploymentResource[]>([
    { name: 'Storage Containers (uploads, input, output)', status: 'pending' },
    { name: 'RAG (index, indexer, data source)', status: 'pending' },
    { name: 'Event Grid', status: 'pending' },
    { name: 'Function App Components (maturity assessment, executive summary)', status: 'pending' }
  ]);
  const [overallProgress, setOverallProgress] = useState(0);
  const wsRef = useRef<WebSocket | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const navigate = useNavigate();

  // Load regions on component mount
  useEffect(() => {
    loadRegions();
  }, []);

  // Load regions from user context API
  const loadRegions = async () => {
    setIsLoadingRegions(true);
    try {
      // Use the user context service to get regions
      const userContext = await userContextService.fetchUserContext();

      if (!userContext || !userContext.accessibleResources.regions) {
        console.error('Error loading regions from user context');
        setError('Failed to load regions. Please try again later.');
        return;
      }

      // Convert regions from user context to IDropdownOption format
      const regionOptions = userContext.accessibleResources.regions.map((region) => ({
        key: region.id,
        text: region.name
      }));

      setRegions(regionOptions);

      // If user is a Regional Admin, automatically select their region
      if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser?.region) {
        setSelectedRegion(currentUser.region);
      } else if (regionOptions.length > 0) {
        // Otherwise, select the first region by default
        setSelectedRegion(regionOptions[0].key as string);
      }
    } catch (error) {
      console.error('Error fetching regions:', error);
      setError('Failed to load regions. Please try again later.');
    } finally {
      setIsLoadingRegions(false);
    }
  };



  // Setup WebSocket connection for deployment status updates
  useEffect(() => {
    if (createdProjectId && showDeployment) {
      // Initial fetch to get the current status
      fetchDeploymentStatus();

      // Connect to WebSocket for real-time updates
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/ws/deployment-status/${createdProjectId}`;

      console.log(`[DEBUG] Connecting to deployment status WebSocket: ${wsUrl}`);
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      // Define the startPolling function
      const startPolling = () => {
        // Clear any existing polling interval
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }

        // Create a new polling interval
        pollingIntervalRef.current = setInterval(() => {
          console.log('[DEBUG] Polling for deployment status...');
          fetchDeploymentStatus();

          // Check if all resources are completed and stop polling if they are
          const allCompleted = deploymentResources.every(r => r.status === 'completed');
          if (allCompleted) {
            console.log('[DEBUG] All resources are completed, stopping polling');
            if (pollingIntervalRef.current) {
              clearInterval(pollingIntervalRef.current);
              pollingIntervalRef.current = null;
            }
          }
        }, 2000); // Poll every 2 seconds
      };

      // Start polling immediately regardless of WebSocket status
      console.log('[DEBUG] Starting polling immediately as a fallback');
      startPolling();

      ws.onopen = () => {
        console.log('[DEBUG] Deployment status WebSocket connected');
        console.log('[DEBUG] WebSocket readyState:', ws.readyState);
        console.log('[DEBUG] WebSocket URL:', wsUrl);

        // Send an initial ping to ensure the connection is working
        const pingMessage = JSON.stringify({ type: 'ping', timestamp: new Date().toISOString() });
        console.log('[DEBUG] Sending initial ping message:', pingMessage);
        ws.send(pingMessage);
      };

      ws.onmessage = (event) => {
        console.log('[DEBUG] WebSocket message received. Raw data:', event.data);
        try {
          const message = JSON.parse(event.data);
          console.log('[DEBUG] Parsed WebSocket message:', message);
          console.log('[DEBUG] Message type:', message.type);

          if (message.type === 'connection_established') {
            console.log('[DEBUG] WebSocket connection established message received');
          } else if (message.type === 'deployment_status_update' && message.data) {
            console.log('[DEBUG] Received deployment status update. Status:', message.data.status);
            console.log('[DEBUG] Full status data:', JSON.stringify(message.data, null, 2));

            updateResourceStatus(message.data);

            // If we receive a completed status, make sure to update all resources
            if (message.data.status === 'completed') {
              console.log('[DEBUG] Deployment completed via WebSocket, updating all resources to completed');
              setDeploymentResources(prev => prev.map(resource => ({
                ...resource,
                status: 'completed'
              })));

              // Force one final status check
              fetchDeploymentStatus();
            }
          } else if (message.type === 'pong') {
            console.log('[DEBUG] Received pong from server, connection is alive');
          } else {
            console.log('[DEBUG] Received unknown message type:', message.type);
          }
        } catch (err) {
          console.error('[DEBUG] Error parsing WebSocket message:', err);
          console.error('[DEBUG] Raw message that caused error:', event.data);
        }
      };

      ws.onerror = (error) => {
        console.error('[DEBUG] Deployment status WebSocket error:', error);
        console.error('[DEBUG] WebSocket readyState at error:', ws.readyState);
        console.error('[DEBUG] Error timestamp:', new Date().toISOString());

        // Fall back to polling if WebSocket fails
        if (!pollingIntervalRef.current) {
          console.log('[DEBUG] Falling back to polling for deployment status due to WebSocket error');
          startPolling();
        }
      };

      ws.onclose = (event) => {
        console.log('[DEBUG] Deployment status WebSocket closed');
        console.log('[DEBUG] Close event code:', event.code);
        console.log('[DEBUG] Close event reason:', event.reason);
        console.log('[DEBUG] Close event wasClean:', event.wasClean);
        console.log('[DEBUG] Close timestamp:', new Date().toISOString());

        // Fall back to polling if WebSocket closes
        if (!pollingIntervalRef.current) {
          console.log('[DEBUG] Falling back to polling for deployment status due to WebSocket close');
          startPolling();
        }
      };


    }

    // Set up a ping interval to keep the WebSocket connection alive
    const pingInterval = setInterval(() => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        console.log('Sending ping to keep WebSocket connection alive');
        wsRef.current.send(JSON.stringify({ type: 'ping', timestamp: new Date().toISOString() }));
      }
    }, 30000); // Send ping every 30 seconds

    return () => {
      // Clean up WebSocket, polling, and ping interval on unmount
      clearInterval(pingInterval);

      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }

      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [createdProjectId, showDeployment]);

  // Update overall progress when resource statuses change
  useEffect(() => {
    if (showDeployment) {
      const completedCount = deploymentResources.filter(r => r.status === 'completed').length;
      const totalCount = deploymentResources.length;
      const newProgress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;
      setOverallProgress(newProgress);

      // If all resources are deployed, navigate to projects page after a delay
      if (completedCount === totalCount && completedCount > 0) {
        console.log('All resources deployed successfully, navigating to projects page...');
        // Force one final status check to ensure we have the latest status
        fetchDeploymentStatus();

        const timer = setTimeout(() => {
          // Close WebSocket before navigating
          if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
          }

          // Clear any polling interval
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }

          navigate('/projects');
        }, 2000);
        return () => clearTimeout(timer);
      }
    }
  }, [deploymentResources, showDeployment, navigate]);

  const fetchDeploymentStatus = async () => {
    if (!createdProjectId) {
      console.log('[DEBUG] fetchDeploymentStatus called but createdProjectId is null');
      return;
    }

    console.log(`[DEBUG] Fetching deployment status via HTTP for project ${createdProjectId}`);
    try {
      const url = `/api/projects/${createdProjectId}/deployment-status`;
      console.log(`[DEBUG] Fetching from URL: ${url}`);

      const response = await fetch(url);
      console.log(`[DEBUG] HTTP response status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        console.log('[DEBUG] HTTP response data:', JSON.stringify(data, null, 2));
        updateResourceStatus(data);
      } else {
        console.error(`[DEBUG] HTTP error response: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error('[DEBUG] Error response body:', errorText);
      }
    } catch (err) {
      console.error('[DEBUG] Error fetching deployment status:', err);
      console.error('[DEBUG] Error timestamp:', new Date().toISOString());
    }
  };

  const updateResourceStatus = (data: any) => {
    if (!data) return;

    // Get the overall deployment status
    const status = data.status || 'pending';
    const message = data.message || '';
    const error = data.error || '';
    const details = data.details || {};
    const timestamp = data.updated_at || '';

    console.log('Deployment status update:', { status, message, error, details, timestamp });

    // Check if this is a new update based on timestamp
    const isNewUpdate = timestamp !== '' && timestamp !== lastTimestampRef.current;
    if (timestamp) {
      lastTimestampRef.current = timestamp;
    }

    // Check if we have resource details from the Azure Resource Monitor
    const resourceDetails = details.storage || details.search || details.function;
    const hasResourceDetails = !!resourceDetails;

    // Update all resources based on their individual status
    setDeploymentResources(prev => {
      const newResources = [...prev];

      // Update storage container status
      if (details.storage && details.storage_complete) {
        newResources[0].status = 'completed';
      } else if (details.storage && !details.storage_complete) {
        newResources[0].status = 'in_progress';
      }

      // Update search resources status
      if (details.search_complete ||
          (details.search && (details.search.index && details.search.indexer && details.search.datasource))) {
        newResources[1].status = 'completed';
      } else if (details.search || message.toLowerCase().includes('search') ||
                message.toLowerCase().includes('index') || message.toLowerCase().includes('indexer')) {
        newResources[1].status = 'in_progress';
      }

      // Update function app status
      if (details.function_complete ||
          (details.function && details.function.function_app)) {
        newResources[2].status = 'completed';
      } else if (details.function || message.toLowerCase().includes('function') ||
                message.toLowerCase().includes('app') || message.toLowerCase().includes('event grid')) {
        newResources[2].status = 'in_progress';
      }

      // Update function components status
      if (message.toLowerCase().includes('functions deployed successfully') ||
          message.toLowerCase().includes('function deployment successful')) {
        newResources[3].status = 'completed';
      } else if (message.toLowerCase().includes('deploying functions') ||
                message.toLowerCase().includes('maturity') ||
                message.toLowerCase().includes('executive') ||
                message.toLowerCase().includes('summary')) {
        newResources[3].status = 'in_progress';
      }

      // If all resources are complete, mark the deployment as complete
      if (details.overall_complete || status === 'completed') {
        console.log('Deployment marked as complete based on overall_complete flag or completed status');
        // Force all resources to completed state
        return prev.map(resource => ({
          ...resource,
          status: 'completed'
        }));
      }

      // If there's an error, mark all in-progress resources as failed
      if (status === 'failed' && error && !error.includes('DeploymentActive')) {
        return prev.map(resource => ({
          ...resource,
          status: resource.status === 'in_progress' ? 'failed' : resource.status
        }));
      }

      // Special case for RAG components - they often show as failed initially but are actually created
      if (newResources[0].status === 'completed' && newResources[1].status !== 'completed') {
        // If storage is completed, RAG components are likely created too
        newResources[1].status = 'completed';
      }

      return newResources;
    });

    // Capture detailed error information
    if (error) {
      setHasDeploymentError(true);

      // Format the error details for display
      const errorDetails = [
        `Status: ${status}`,
        `Message: ${message}`,
        `Error: ${error}`,
        `Timestamp: ${timestamp}`,
        '\nDetailed Error:',
        JSON.stringify(details, null, 2)
      ].join('\n');

      setDeploymentError(errorDetails);
    }

    // If we see a message about function deployment failing, show an error
    if (message.toLowerCase().includes('function deployment failed')) {
      setError(`Function deployment failed. Please try again or contact support. Details: ${message}`);
    }
  };

  // Reference to track the last timestamp we processed
  const lastTimestampRef = useRef<string>('');

  const handleSubmit = async () => {
    // Validate form
    if (!projectName.trim()) {
      setError('Project name is required');
      return;
    }

    if (!selectedRegion) {
      setError('Please select a region for the project');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Use a direct API call to create the project
      // Get authentication headers
      const { getAuthHeaders } = await import('../../services/authHeaderService');
      const authHeaders = await getAuthHeaders();

      const createProjectResponse = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders, // Add authentication headers
        },
        body: JSON.stringify({
          name: projectName,
          region: selectedRegion,
        }),
      });

      if (!createProjectResponse.ok) {
        let errorMsg = `Failed to create project. Status: ${createProjectResponse.status}`;
        try {
          const errorData = await createProjectResponse.json();
          errorMsg = errorData.error || errorData.message || errorMsg;
        } catch (e) {
          // If parsing JSON fails, use the status text
          errorMsg = `${errorMsg} - ${createProjectResponse.statusText}`;
        }
        throw new Error(errorMsg);
      }
      const newProjectData = await createProjectResponse.json(); // Assuming API returns the project data directly
      console.log('Project created:', newProjectData);

      // Store the project ID in localStorage to show banner on projects page
      if (newProjectData && newProjectData.id) {
        const deploymentStartTime = new Date().toISOString();
        localStorage.setItem('newProjectId', newProjectData.id);
        localStorage.setItem('newProjectName', projectName);
        localStorage.setItem('newProjectCreatedAt', deploymentStartTime);

        // Store the deployment start time in the project's environment field
        try {
          const updateProjectResponse = await fetch(`/api/projects/${newProjectData.id}`, {
            method: 'PUT', // Or PATCH, depending on API design
            headers: {
              'Content-Type': 'application/json',
              ...authHeaders, // Add authentication headers
            },
            body: JSON.stringify({
              environment: {
                ...(newProjectData.environment || {}), // Ensure environment exists
                deploymentStartTime: deploymentStartTime,
              },
            }),
          });

          if (!updateProjectResponse.ok) {
            // Log error and continue, similar to original behavior for this specific update
            let errorPayload = 'Could not retrieve error payload.';
            try {
                errorPayload = await updateProjectResponse.text();
            } catch (e) {
                // ignore if text() fails
            }
            console.error(`Failed to update project with deployment start time. Status: ${updateProjectResponse.status} ${updateProjectResponse.statusText}. Response: ${errorPayload}`);
          }
        } catch (updateErr) { // Original catch block style
          console.error('Failed to update project with deployment start time:', updateErr);
          // Continue anyway, as this is not critical
        }

        // Redirect to projects page immediately
        navigate('/projects');
      } else {
        throw new Error('Failed to get project ID from response');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create project. Please try again.');
      console.error('Error creating project:', err);
    } finally {
      setIsLoading(false);
    }
  };



  const handleCancel = () => {
    navigate('/projects');
  };

  // Function to create a support ticket
  const handleCreateSupportTicket = async () => {
    if (!createdProjectId || !deploymentError || isSendingTicket || ticketSent) return;

    setIsSendingTicket(true);

    try {
      const response = await fetch(`/api/projects/${createdProjectId}/support-ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectName,
          projectId: createdProjectId,
          error: deploymentError,
          email: '<EMAIL>',
          resourceStatuses: deploymentResources.map(r => ({
            name: r.name,
            status: r.status
          }))
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      setTicketSent(true);
      alert('Support ticket has been created successfully. The team will investigate the issue.');
    } catch (err: any) {
      console.error('Error creating support ticket:', err);
      alert(`Failed to create support ticket: ${err.message || 'Unknown error'}`);
    } finally {
      setIsSendingTicket(false);
    }
  };

  // Render status icon based on resource status
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Icon iconName="CheckMark" className={styles.completedIcon} />;
      case 'in_progress':
        return <Icon iconName="Sync" className={styles.inProgressIcon} />;
      case 'failed':
        return <Icon iconName="Error" className={styles.failedIcon} />;
      default:
        return <Icon iconName="CircleRing" className={styles.pendingIcon} />;
    }
  };

  return (
    <div className={styles.container}>
      <Stack className={styles.header}>
        <Text variant="xxLarge">Create {projectName ? `${projectName} ` : ''}Project</Text>
      </Stack>

      {error && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          dismissButtonAriaLabel="Close"
          onDismiss={() => setError(null)}
        >
          {error}
        </MessageBar>
      )}

      {!showDeployment ? (
        <Stack className={styles.content}>
          <Stack tokens={{ childrenGap: 16 }}>
            <TextField
              label="Project Name"
              required
              value={projectName}
              onChange={(_, newValue) => setProjectName(newValue || '')}
              placeholder="Enter project name"
            />

            {/* Region Dropdown - disabled for Regional Admins */}
            {isLoadingRegions ? (
              <Stack horizontalAlign="start" verticalAlign="center" styles={{ root: { height: 32, marginTop: 24 } }}>
                <Spinner size={SpinnerSize.small} label="Loading regions..." />
              </Stack>
            ) : (
              <Dropdown
                label="Region"
                required
                selectedKey={selectedRegion}
                options={regions}
                onChange={(_, option) => option && setSelectedRegion(option.key as string)}
                disabled={currentUser?.role === UserRole.REGIONAL_ADMIN} // Disabled for Regional Admins
                styles={{ dropdown: { width: '100%' } }}
              />
            )}
          </Stack>

          <Stack horizontal horizontalAlign="end" tokens={{ childrenGap: 8 }} className={styles.actionButtons}>
            <DefaultButton
              text="Cancel"
              onClick={handleCancel}
            />
            <PrimaryButton
              text="Create Project"
              onClick={handleSubmit}
              disabled={isLoading}
            />
          </Stack>
        </Stack>
      ) : (
        <Stack className={styles.content}>
          <Stack tokens={{ childrenGap: 16 }}>
            <Text variant="large" className={styles.deploymentTitle}>Creating Azure Resources</Text>
            <Text>Please wait while we set up your project resources...</Text>

            <ProgressIndicator
              percentComplete={overallProgress / 100}
              className={styles.overallProgress}
            />

            <Stack className={styles.resourceList}>
              {deploymentResources.map((resource, index) => (
                <Stack horizontal key={index} className={`${styles.resourceItem} ${styles[resource.status]}`}>
                  <div className={styles.resourceStatus}>
                    {renderStatusIcon(resource.status)}
                  </div>
                  <Stack className={styles.resourceInfo}>
                    <Text className={styles.resourceName}>{resource.name}</Text>
                    <Text className={styles.resourceStatusText}>
                      {resource.status === 'pending' && 'Waiting...'}
                      {resource.status === 'in_progress' && (
                        <>
                          Creating Azure resource...
                          <div className={styles.resourceDetails}>
                            {index === 0 && (
                              <>Creating blob containers for uploads, input, and output data</>
                            )}
                            {index === 1 && (
                              <>Setting up RAG with index, indexer, and data source</>
                            )}
                            {index === 2 && (
                              <>Deploying Event Grid</>
                            )}
                            {index === 3 && (
                              <>Configuring maturity assessment and executive summary functions</>
                            )}
                          </div>
                        </>
                      )}
                      {resource.status === 'completed' && (
                        <>
                          Successfully created
                          <div className={styles.resourceDetails}>
                            {index === 0 && (
                              <>Created 3 blob containers: uploads, input, and output</>
                            )}
                            {index === 1 && (
                              <>Created RAG index, indexer, and data source</>
                            )}
                            {index === 2 && (
                              <>Deployed Event Grid</>
                            )}
                            {index === 3 && (
                              <>Configured maturity assessment and executive summary functions</>
                            )}
                          </div>
                        </>
                      )}
                      {resource.status === 'failed' && 'Failed to create - See console for details'}
                    </Text>
                  </Stack>
                </Stack>
              ))}
            </Stack>

            <Stack className={styles.deploymentFooter}>
              <Text className={styles.deploymentNote}>
                {error ? (
                  <span className={styles.errorNote}>Error: {error}</span>
                ) : (
                  <span>Azure resources are being created. This process may take a few minutes.</span>
                )}
              </Text>

              <Stack horizontal tokens={{ childrenGap: 10 }}>
                {/* Return to projects button */}
                <DefaultButton
                  text="Return to Projects"
                  iconProps={{ iconName: 'ChevronLeft' }}
                  onClick={handleCancel}
                />

                {/* Support ticket button */}
                <PrimaryButton
                  text="Create Support Ticket"
                  onClick={handleCreateSupportTicket}
                  className={styles.supportButton}
                  disabled={!hasDeploymentError}
                />
              </Stack>

              {/* Detailed deployment error */}
              {deploymentError && (
                <div className={styles.detailedError}>
                  <Text variant="mediumPlus" className={styles.detailedErrorTitle}>Deployment Error Details:</Text>
                  <div className={styles.errorDetails}>
                    <pre>{deploymentError}</pre>
                  </div>
                </div>
              )}
            </Stack>
          </Stack>
        </Stack>
      )}
    </div>
  );
};

export default NewProject;
