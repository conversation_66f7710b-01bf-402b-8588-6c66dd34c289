import React, { useContext, useEffect, useState } from 'react';
import {
  Stack,
  Text,
  Pivot,
  PivotItem,
  IStackTokens,
  MessageBar,
  MessageBarType,
  PrimaryButton
  , Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '../../state/UserProvider';
import { UserRole, getRolePermissions } from '../../models/roles';
import RoleBasedHeader from '../../components/RoleBasedHeader';
import { AppStateContext } from '../../state/AppProvider';
import CompanyLogo from '../../assets/keyrus-2.svg';
import UserManagement from './UserManagement';
import GlobalSettings from './GlobalSettings';
import ProjectSettings from './ProjectSettings';
import TeamManagement from './TeamManagement';
import UserTagManagement from './UserTagManagement';
import RegionalAdminManagement from './RegionalAdminManagement';
import RegionManagement from './RegionManagement';
import CostAnalytics from './CostAnalytics';

const stackTokens: IStackTokens = { childrenGap: 20 };

const AdminPanel: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, isLoading: isUserLoading } = useUser();
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);
  const appStateContext = useContext(AppStateContext);
  const ui = appStateContext?.state.frontendSettings?.ui;
  const [logo, setLogo] = useState<string>(CompanyLogo);

  useEffect(() => {
    if (!appStateContext?.state.isLoading) {
      setLogo(ui?.logo || CompanyLogo);
    }
  }, [appStateContext?.state.isLoading, ui?.logo]);

  // Determine which tab should be active based on the URL
  const getSelectedKey = () => {
    if (location.pathname.includes('/admin/users')) return 'users';
    if (location.pathname.includes('/admin/global-settings')) return 'global';
    if (location.pathname.includes('/admin/project-settings')) return 'projects';
    if (location.pathname.includes('/admin/teams')) return 'teams';
    if (location.pathname.includes('/admin/tags')) return 'tags';
    if (location.pathname.includes('/admin/regional-admins')) return 'regional-admins';
    if (location.pathname.includes('/admin/regions')) return 'regions';
    if (location.pathname.includes('/admin/cost-analytics')) return 'cost-analytics';
    return 'users'; // Default tab
  };

  // Handle tab changes
  const handleLinkClick = (item?: PivotItem) => {
    if (!item) return;

    switch (item.props.itemKey) {
      case 'users':
        navigate('/admin/users');
        break;
      case 'global':
        navigate('/admin/global-settings');
        break;
      case 'projects':
        navigate('/admin/project-settings');
        break;
      case 'teams':
        navigate('/admin/teams');
        break;
      case 'tags':
        navigate('/admin/tags');
        break;
      case 'regional-admins':
        navigate('/admin/regional-admins');
        break;
      case 'regions':
        navigate('/admin/regions');
        break;
      case 'cost-analytics':
        navigate('/admin/cost-analytics');
        break;
      default:
        navigate('/admin');
    }
  };

  // Show a loading spinner while user information is being fetched
  if (isUserLoading) {
    return (
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <RoleBasedHeader logo={logo} />
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { flex: 1, padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading user..." />
        </Stack>
      </div>
    );
  }

  // If user doesn't have admin permissions, show access denied
  if (!permissions.canAccessAdminPanel) {
    return (
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <RoleBasedHeader logo={logo} />
        <Stack
          horizontalAlign="center"
          verticalAlign="center"
          styles={{ root: { flex: 1, padding: 20 } }}
        >
          <MessageBar
            messageBarType={MessageBarType.error}
            isMultiline={true}
            styles={{ root: { maxWidth: 600 } }}
          >
            <Text variant="large" styles={{ root: { marginBottom: 10 } }}>
              Access Denied
            </Text>
            <Text>
              You do not have permission to access the Admin Panel.
              Please contact your administrator if you believe this is an error.
            </Text>
          </MessageBar>
        </Stack>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <RoleBasedHeader logo={logo} />

      <Stack styles={{ root: { flex: 1, padding: '20px 40px' } }} tokens={stackTokens}>
        <Stack horizontal horizontalAlign="space-between" verticalAlign="center">
          <Text variant="xxLarge" styles={{ root: { fontWeight: 600 } }}>
            Admin Panel
          </Text>
          <Stack.Item>
            <PrimaryButton
              text="Back to Projects"
              iconProps={{ iconName: 'ChevronLeft' }}
              onClick={() => navigate('/projects')}
              styles={{
                root: {
                  marginBottom: 10,
                  backgroundColor: '#0078d4',
                  borderRadius: '4px'
                },
                rootHovered: {
                  backgroundColor: '#106ebe'
                }
              }}
            />
          </Stack.Item>
        </Stack>

        <Pivot
          selectedKey={getSelectedKey()}
          onLinkClick={handleLinkClick}
          styles={{ root: { marginBottom: 20 } }}
        >
          <PivotItem
            headerText="User Management"
            itemKey="users"
            headerButtonProps={{
              disabled: !permissions.canManageUsers
            }}
          />

          {/* Only show Global Settings for Super Admin */}
          {permissions.canManageGlobalSettings && (
            <PivotItem
              headerText="Global Settings"
              itemKey="global"
            />
          )}

          <PivotItem
            headerText="Project Settings"
            itemKey="projects"
          />

          <PivotItem
            headerText="Team Management"
            itemKey="teams"
            headerButtonProps={{
              disabled: !permissions.canCreateTeams
            }}
          />

          <PivotItem
            headerText="User Tags"
            itemKey="tags"
            headerButtonProps={{
              disabled: !permissions.canTagUsers
            }}
          />

          {/* Only show Regional Admin Setup for Super Admin */}
          {permissions.canSetupRegionalAdmins && (
            <PivotItem
              headerText="Regional Admins"
              itemKey="regional-admins"
            />
          )}

          {/* Only show Region Management for Super Admin */}
          {permissions.canSetupRegionalAdmins && (
            <PivotItem
              headerText="Regions"
              itemKey="regions"
            />
          )}

          {/* Cost Analytics - Available to all admin roles */}
          <PivotItem
            headerText="Cost Analytics"
            itemKey="cost-analytics"
          />
        </Pivot>

        {/* Render the appropriate component based on the selected tab */}
        {getSelectedKey() === 'users' && <UserManagement />}
        {getSelectedKey() === 'global' && permissions.canManageGlobalSettings && <GlobalSettings />}
        {getSelectedKey() === 'projects' && <ProjectSettings />}
        {getSelectedKey() === 'teams' && permissions.canCreateTeams && <TeamManagement />}
        {getSelectedKey() === 'tags' && permissions.canTagUsers && <UserTagManagement />}
        {getSelectedKey() === 'regional-admins' && permissions.canSetupRegionalAdmins && <RegionalAdminManagement />}
        {getSelectedKey() === 'regions' && permissions.canSetupRegionalAdmins && <RegionManagement />}
        {getSelectedKey() === 'cost-analytics' && <CostAnalytics />}
      </Stack>
    </div>
  );
};

export default AdminPanel;
