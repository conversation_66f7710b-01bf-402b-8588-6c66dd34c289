import React, { useState, useEffect } from 'react';
import {
  Stack,
  Text,
  Dropdown,
  IDropdownOption,
  Spinner,
  SpinnerSize,
  MessageBar,
  MessageBarType,
  IStackTokens,
  Label,
  Toggle,
  PrimaryButton
} from '@fluentui/react';
import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  ChartEvent
} from 'chart.js';
import costService, {
  CostData,
  CostFilterOptions
} from '../../services/costService';
import { UserRole } from '../../models/roles';
import { useUser } from '../../state/UserProvider';

// Function to create the percentage labels plugin with access to costData
const createBudgetThresholdPlugin = (currentCostData: CostData | null) => ({
  id: 'budgetThresholdPlugin',
  afterDraw(chart: any) {
    const {ctx, data, chartArea, scales} = chart;

    // Draw percentage labels on top of each bar
    if (data.datasets.length > 0 && currentCostData) {
      const dataset = data.datasets[0];
      const meta = chart.getDatasetMeta(0);

      for (let i = 0; i < meta.data.length; i++) {
        const project = currentCostData.projectCosts[i];
        if (!project) continue;

        const percentUsed = project.budget > 0 ? (project.cost / project.budget) * 100 : 0;
        let color;
        if (percentUsed >= 100) {
          color = RED_400; // Red for over budget
        } else if (percentUsed >= 80) {
          color = ORANGE_400; // Orange for approaching budget
        } else {
          color = CYAN_400; // Cyan for under budget
        }

        const bar = meta.data[i];
        const {x, y, width} = bar;

        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = color;

        // Position the text above the bar
        ctx.fillText(`${percentUsed.toFixed(0)}%`, x, y - 10);

        // If over budget, draw a horizontal line at the budget level
        if (percentUsed > 100) {
          const budgetHeight = project.budget / project.cost * (bar.base - bar.y);
          const budgetY = bar.base - budgetHeight;

          ctx.beginPath();
          ctx.moveTo(x - width/2, budgetY);
          ctx.lineTo(x + width/2, budgetY);
          ctx.lineWidth = 2;
          ctx.strokeStyle = '#000';
          ctx.stroke();
        }

        ctx.restore();
      }
    }
  }
});

// Function to create the percentage labels plugin for regions
const createRegionBudgetThresholdPlugin = (currentCostData: CostData | null) => ({
  id: 'regionBudgetThresholdPlugin',
  afterDraw(chart: any) {
    const {ctx, data, chartArea, scales} = chart;

    // Draw percentage labels on the right side of each horizontal bar
    if (data.datasets.length > 0 && currentCostData) {
      const dataset = data.datasets[0];
      const meta = chart.getDatasetMeta(0);

      // Get sorted regions to match chart order
      const sortedRegions = [...currentCostData.regionCosts].sort((a, b) => b.cost - a.cost);

      for (let i = 0; i < meta.data.length; i++) {
        const region = sortedRegions[i];
        if (!region || !region.budget) continue;

        const percentUsed = (region.cost / region.budget) * 100;
        let color;
        if (percentUsed >= 100) {
          color = RED_400; // Red for over budget
        } else if (percentUsed >= 80) {
          color = ORANGE_400; // Orange for approaching budget
        } else {
          color = CYAN_400; // Cyan for under budget
        }

        const bar = meta.data[i];
        const {x, y, height} = bar;

        ctx.save();
        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = color;
        ctx.textBaseline = 'middle';

        // Calculate text width to determine positioning
        const text = `${percentUsed.toFixed(0)}%`;
        const textWidth = ctx.measureText(text).width;
        const padding = 10;

        // Check if text would go beyond chart area when positioned to the right
        const textRightPosition = x + padding + textWidth;
        const chartRightEdge = chartArea.right;

        let textX;
        if (textRightPosition > chartRightEdge) {
          // Position text inside the bar (to the left of the bar end)
          textX = x - padding;
          ctx.textAlign = 'right';
          // Use white color for better contrast when text is inside the bar
          ctx.fillStyle = 'white';
        } else {
          // Position text to the right of the bar
          textX = x + padding;
          ctx.textAlign = 'left';
        }

        ctx.fillText(text, textX, y);

        ctx.restore();
      }
    }
  }
});

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

// Define stack tokens for spacing
const stackTokens: IStackTokens = { childrenGap: 20 };
const chartContainerTokens: IStackTokens = { childrenGap: 15 };

// Time range options
const timeRangeOptions: IDropdownOption[] = [
  { key: 'week', text: 'Last 7 Days' },
  { key: 'month', text: 'This Month' },
  { key: 'quarter', text: 'Last 3 Months' },
  { key: 'year', text: 'Year to Date' },
  { key: 'custom', text: 'Custom Range' }
];



// Color constants
const CYAN_400 = '#0EA5E9';
const ORANGE_400 = '#F97316';
const RED_400 = '#EF4444';
const BLUE_PALETTE = [
  'rgba(14, 165, 233, 1)',    // darker blue
  'rgba(56, 189, 248, 1)',    // medium blue
  'rgba(125, 211, 252, 1)',   // lighter blue
  'rgba(186, 230, 253, 1)',   // very light blue
  'rgba(224, 242, 254, 1)',   // lightest blue
];

const CostAnalytics: React.FC = () => {
  const { currentUser, isLoading: isUserLoading } = useUser();

  const userRole = currentUser?.role ?? UserRole.REGULAR_USER;
  const userRegionId = currentUser?.region;

  // State for cost data and filters
  const [costData, setCostData] = useState<CostData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('month');
  const [selectedRegionId, setSelectedRegionId] = useState<string | undefined>(undefined);
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>(undefined);

  const [showSharedResources, setShowSharedResources] = useState<boolean>(true);

  // Region options for dropdown (derived from accessible resources)
  const [regionOptions, setRegionOptions] = useState<IDropdownOption[]>([]);
  const [projectOptions, setProjectOptions] = useState<IDropdownOption[]>([]);

  const [isCollecting, setIsCollecting] = useState<boolean>(false);
  const [collectMessage, setCollectMessage] = useState<string | null>(null);

  // Load region options from accessible resources
  useEffect(() => {
    if (currentUser && currentUser.accessibleResources && currentUser.accessibleResources.regions) {
      if (userRole === UserRole.SUPER_ADMIN) {
        const regions = currentUser.accessibleResources.regions.map((region: any) => ({
          key: region.id,
          text: region.name
        }));
        setRegionOptions([{ key: 'all', text: 'All Regions' }, ...regions]);
      }
    }
  }, [currentUser, userRole]);

  // Load cost data on component mount and when filters change
  useEffect(() => {
    if (isUserLoading || !currentUser) {
      return;
    }

    const loadCostData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // For Regional Admins, always filter by their region
        const effectiveRegionId = userRole === UserRole.REGIONAL_ADMIN
          ? userRegionId
          : selectedRegionId;

        // Apply all filters
        const filters: Partial<CostFilterOptions> = {
          timeRange: selectedTimeRange,
          regionId: effectiveRegionId,
          projectId: selectedProjectId,
          showSharedResources: showSharedResources
        };

        // Get cost data with user role-based filtering
        const data = await costService.getCostData(
          selectedTimeRange,
          userRole,
          userRegionId,
          filters
        );

        setCostData(data);

        // Build project options from accessible projects
        if (currentUser.accessibleResources && currentUser.accessibleResources.projects) {
          let accessibleProjects = currentUser.accessibleResources.projects;

          // Filter projects by region if a region is selected (for Super Admin only)
          if (userRole === UserRole.SUPER_ADMIN && effectiveRegionId && effectiveRegionId !== 'all') {
            accessibleProjects = accessibleProjects.filter((project: any) => project.region === effectiveRegionId);
          }

          const projects = accessibleProjects.map((project: any) => ({
            key: project.id,
            text: project.name
          }));
          setProjectOptions([{ key: 'all', text: 'All Projects' }, ...projects]);
        }
      } catch (err) {
        setError('Failed to load cost data. Please try again later.');
        console.error('Error loading cost data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadCostData();
  }, [
    selectedTimeRange,
    // Only include selectedRegionId in the dependency array for Super Admins
    ...(userRole === UserRole.SUPER_ADMIN ? [selectedRegionId] : []),
    selectedProjectId,
    showSharedResources,
    userRole,
    userRegionId,
    isUserLoading,
    currentUser
  ]);

  // Handle filter changes
  const handleTimeRangeChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedTimeRange(option.key as string);
    }
  };

  const handleRegionChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedRegionId(option.key === 'all' ? undefined : option.key as string);
    }
  };

  const handleProjectChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedProjectId(option.key === 'all' ? undefined : option.key as string);
    }
  };

  const handleSharedResourcesToggle = (_ev: React.MouseEvent<HTMLElement>, checked?: boolean) => {
    setShowSharedResources(!!checked);
  };

  const handleCollectNow = async () => {
    setIsCollecting(true);
    setCollectMessage(null);
    try {
      await costService.collectCostDataNow();
      setCollectMessage('Cost data collection triggered successfully.');
      // Refresh data after collection
      const filters: Partial<CostFilterOptions> = {
        timeRange: selectedTimeRange,
        regionId: userRole === UserRole.REGIONAL_ADMIN ? userRegionId : selectedRegionId,
        projectId: selectedProjectId,
        showSharedResources: showSharedResources
      };
      const data = await costService.getCostData(
        selectedTimeRange,
        userRole,
        userRegionId,
        filters
      );
      setCostData(data);
    } catch (err) {
      console.error('Error triggering cost data collection', err);
      setCollectMessage('Failed to trigger cost data collection.');
    } finally {
      setIsCollecting(false);
    }
  };



  // Prepare data for Cost per Project chart
  const getProjectCostChartData = () => {
    if (!costData || costData.projectCosts.length === 0) return null;

    return {
      labels: costData.projectCosts.map(p => p.project),
      datasets: [
        {
          label: 'Cost (€)',
          data: costData.projectCosts.map(p => p.cost),
          backgroundColor: costData.projectCosts.map(p => {
            const percentUsed = p.budget > 0 ? (p.cost / p.budget) * 100 : 0;
            if (percentUsed >= 100) {
              return RED_400; // Red for over budget
            } else if (percentUsed >= 80) {
              return ORANGE_400; // Orange for approaching budget
            } else {
              return CYAN_400; // Cyan for under budget
            }
          }),
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare data for Cost by Service chart
  const getServiceCostChartData = () => {
    if (!costData || costData.serviceCosts.length === 0) return null;

    return {
      labels: costData.serviceCosts.map(s => `${s.service}${s.isShared ? ' (Shared)' : ''}`),
      datasets: [
        {
          label: 'Cost (€)',
          data: costData.serviceCosts.map(s => s.cost),
          backgroundColor: BLUE_PALETTE.slice(0, costData.serviceCosts.length),
          borderColor: 'rgba(255, 255, 255, 0.8)',
          borderWidth: 2,
          cutout: '70%',  // This makes it a donut chart
        }
      ]
    };
  };

  // Prepare data for Cost by Region chart with budget comparison
  const getRegionCostChartData = () => {
    if (!costData || costData.regionCosts.length === 0) return null;

    // Sort regions by cost (highest first)
    const sortedRegions = [...costData.regionCosts].sort((a, b) => b.cost - a.cost);

    return {
      labels: sortedRegions.map(r => r.region),
      datasets: [
        {
          label: 'Cost (€)',
          data: sortedRegions.map(r => r.cost),
          backgroundColor: sortedRegions.map(r => {
            if (!r.budget) return CYAN_400;
            const percentUsed = (r.cost / r.budget) * 100;
            if (percentUsed >= 100) return RED_400; // Red for over budget
            if (percentUsed >= 80) return ORANGE_400; // Orange for near budget
            return CYAN_400; // Cyan for under budget
          }),
          borderColor: 'rgba(0, 0, 0, 0.1)',
          borderWidth: 1,
        }
      ]
    };
  };





  // Chart options
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // Hide legend since we only have one dataset
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const projectIndex = context.dataIndex;
            const project = costData?.projectCosts[projectIndex];

            if (project) {
              const percentUsed = project.budget > 0 ? (project.cost / project.budget) * 100 : 0;
              return [
                `Project: ${project.project}`,
                `Cost: €${project.cost.toLocaleString()}`,
                `Budget: €${project.budget.toLocaleString()}`,
                `Budget Usage: ${percentUsed.toFixed(1)}%`
              ];
            }
            return '';
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cost (€)'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: function(value: any) {
            return '€' + value.toLocaleString();
          }
        }
      }
    },
    barPercentage: 0.7,
    categoryPercentage: 0.8
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,  // Hide the default legend since we're creating our own
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const serviceIndex = context.dataIndex;
            const service = costData?.serviceCosts[serviceIndex];
            if (service) {
              return `${service.service}: €${service.cost.toFixed(2)}`;
            }
            return '';
          }
        }
      }
    },
    cutout: '70%'  // This makes it a donut chart
  };

  const horizontalBarChartOptions = {
    indexAxis: 'y' as const,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const regionIndex = context.dataIndex;
            // Get the sorted regions data to match the chart order
            const sortedRegions = costData?.regionCosts ? [...costData.regionCosts].sort((a, b) => b.cost - a.cost) : [];
            const region = sortedRegions[regionIndex];

            if (region) {
              if (region.budget) {
                const percentUsed = (region.cost / region.budget) * 100;
                return [
                  `Region: ${region.region}`,
                  `Cost: €${region.cost.toLocaleString()}`,
                  `Budget: €${region.budget.toLocaleString()}`,
                  `Budget Usage: ${percentUsed.toFixed(1)}%`
                ];
              } else {
                return `${region.region}: €${region.cost.toFixed(2)}`;
              }
            }
            return '';
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Cost (€)'
        }
      }
    }
  };





  if (isUserLoading || !currentUser) {
    return <Spinner label="Loading user..." size={SpinnerSize.large} />;
  }

  return (
    <Stack tokens={stackTokens}>
      <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 10 }}>
        <Text variant="xLarge" styles={{ root: { fontWeight: 600, marginBottom: 10 } }}>
          Cost Analytics
        </Text>
        {userRole === UserRole.REGIONAL_ADMIN && userRegionId && (
          <Text
            variant="mediumPlus"
            styles={{
              root: {
                backgroundColor: CYAN_400,
                color: 'white',
                padding: '2px 10px',
                borderRadius: 4,
                marginBottom: 10
              }
            }}
          >
            {costData?.regionCosts.find(r => r.regionId === userRegionId)?.region || 'Regional'} View
          </Text>
        )}
        <PrimaryButton
          text="Collect Now"
          onClick={handleCollectNow}
          disabled={isCollecting}
        />
      </Stack>

      {collectMessage && (
        <MessageBar
          messageBarType={MessageBarType.info}
          isMultiline={false}
          dismissButtonAriaLabel="Close"
          onDismiss={() => setCollectMessage(null)}
        >
          {collectMessage}
        </MessageBar>
      )}

      {error && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          dismissButtonAriaLabel="Close"
        >
          {error}
        </MessageBar>
      )}

      {/* Filters Section */}
      <Stack
        horizontal
        wrap
        horizontalAlign="space-between"
        verticalAlign="center"
        tokens={{ childrenGap: 10 }}
        styles={{ root: { backgroundColor: 'white', padding: 15, borderRadius: 4, marginBottom: 10 } }}
      >
        <Stack horizontal wrap tokens={{ childrenGap: 15 }}>
          {/* Time Range Filter */}
          <Dropdown
            label="Time Range"
            selectedKey={selectedTimeRange}
            options={timeRangeOptions}
            styles={{ dropdown: { width: 150 } }}
            onChange={handleTimeRangeChange}
          />

          {/* Region Filter - Only show for Super Admins */}
          {userRole === UserRole.SUPER_ADMIN && (
            <Dropdown
              label="Region"
              selectedKey={selectedRegionId || 'all'}
              options={regionOptions}
              styles={{ dropdown: { width: 150 } }}
              onChange={handleRegionChange}
            />
          )}

          {/* Project Filter */}
          <Dropdown
            label="Project"
            selectedKey={selectedProjectId || 'all'}
            options={projectOptions}
            styles={{ dropdown: { width: 150 } }}
            onChange={handleProjectChange}
          />

          {/* Region indicator for Regional Admins */}
          {userRole === UserRole.REGIONAL_ADMIN && userRegionId && (
            <Stack verticalAlign="end" styles={{ root: { paddingBottom: 5 } }}>
              <Label>Region: {costData?.regionCosts.find(r => r.regionId === userRegionId)?.region || 'Loading...'}</Label>
            </Stack>
          )}
        </Stack>

        <Stack horizontal tokens={{ childrenGap: 15 }}>
          {/* Shared Resources Toggle */}
          <Toggle
            label="Include Shared Resources"
            checked={showSharedResources}
            onChange={handleSharedResourcesToggle}
          />
        </Stack>
      </Stack>



      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading cost data..." />
        </Stack>
      ) : (
        <Stack tokens={stackTokens}>
            {/* Cost per Project Chart */}
            <Stack
              styles={{
                root: {
                  backgroundColor: 'white',
                  padding: 20,
                  borderRadius: 4,
                  boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
                }
              }}
              tokens={chartContainerTokens}
            >
              <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Cost per Project</Text>
              <div style={{ height: 350 }}>
                {getProjectCostChartData() ? (
                  <Bar
                    data={getProjectCostChartData()!}
                    options={{
                      ...barChartOptions,
                      onClick: (event: ChartEvent, elements: any[]) => {
                        if (elements && elements.length > 0) {
                          const index = elements[0].index;
                          const projectId = costData?.projectCosts[index].projectId;
                          if (projectId) {
                            setSelectedProjectId(projectId);
                          }
                        }
                      }
                    }}
                    plugins={[createBudgetThresholdPlugin(costData)]}
                  />
                ) : (
                  <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
                    <Text>No project cost data available for the selected filters.</Text>
                  </Stack>
                )}
              </div>

              {/* Legend for the chart */}
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '15px',
                gap: '20px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    backgroundColor: CYAN_400,
                    marginRight: '5px',
                    borderRadius: '2px'
                  }}></div>
                  <Text styles={{ root: { fontSize: '12px' } }}>Under Budget</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    backgroundColor: ORANGE_400,
                    marginRight: '5px',
                    borderRadius: '2px'
                  }}></div>
                  <Text styles={{ root: { fontSize: '12px' } }}>Near Budget</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    backgroundColor: RED_400,
                    marginRight: '5px',
                    borderRadius: '2px'
                  }}></div>
                  <Text styles={{ root: { fontSize: '12px' } }}>Over Budget</Text>
                </div>
              </div>
            </Stack>

            {/* Cost by Service and Region Charts */}
            <Stack horizontal tokens={{ childrenGap: 20 }}>
              {/* Cost by Service Chart */}
              <Stack.Item grow={0.35}>
                <Stack
                  styles={{
                    root: {
                      backgroundColor: 'white',
                      padding: 20,
                      borderRadius: 4,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
                    }
                  }}
                  tokens={chartContainerTokens}
                >
                  <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Cost by Service</Text>
                  <div style={{ height: 300, position: 'relative', display: 'flex' }}>
                    {getServiceCostChartData() ? (
                      <>
                        <div style={{ flex: '0 0 70%', position: 'relative' }}>
                          <Doughnut
                            data={getServiceCostChartData()!}
                            options={pieChartOptions}
                          />
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            textAlign: 'center',
                            pointerEvents: 'none',
                            width: '100%'
                          }}>
                            <Text variant="small" styles={{ root: { color: '#666', fontSize: '12px' } }}>Total Cost</Text>
                            <Text variant="xLarge" styles={{ root: { fontWeight: 'bold', color: '#0EA5E9', lineHeight: 1.2 } }}>
                              €{costData?.totalCost.toFixed(2)}
                            </Text>
                          </div>
                        </div>

                        {/* Legend on the right side */}
                        <div style={{ flex: '0 0 30%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                          {costData?.serviceCosts.map((service, index) => {
                            return (
                              <div key={index} style={{
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: 10
                              }}>
                                <div style={{
                                  width: 12,
                                  height: 12,
                                  backgroundColor: BLUE_PALETTE[index % BLUE_PALETTE.length],
                                  borderRadius: '50%',
                                  marginRight: 8
                                }} />
                                <Text styles={{ root: { fontSize: '12px' } }}>
                                  {service.service}{service.isShared ? ' (Shared)' : ''}: €{service.cost.toFixed(2)}
                                </Text>
                              </div>
                            );
                          })}
                        </div>
                      </>
                    ) : (
                      <Stack horizontalAlign="center" verticalAlign="center" style={{ width: '100%' }}>
                        <Text>No service cost data available for the selected filters.</Text>
                      </Stack>
                    )}
                  </div>
                </Stack>
              </Stack.Item>

              {/* Cost by Region Chart */}
              <Stack.Item grow={1.65}>
                <Stack
                  styles={{
                    root: {
                      backgroundColor: 'white',
                      padding: 20,
                      borderRadius: 4,
                      boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)'
                    }
                  }}
                  tokens={chartContainerTokens}
                >
                  <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Cost by Region</Text>
                  <div style={{ height: 300 }}>
                    {getRegionCostChartData() ? (
                      <Bar
                        data={getRegionCostChartData()!}
                        options={horizontalBarChartOptions}
                        plugins={[createRegionBudgetThresholdPlugin(costData)]}
                      />
                    ) : (
                      <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
                        <Text>No region cost data available for the selected filters.</Text>
                      </Stack>
                    )}
                  </div>

                  {/* Legend for region budget status */}
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    marginTop: '15px',
                    gap: '20px'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: CYAN_400,
                        marginRight: '5px',
                        borderRadius: '2px'
                      }}></div>
                      <Text styles={{ root: { fontSize: '12px' } }}>Under Budget</Text>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: ORANGE_400,
                        marginRight: '5px',
                        borderRadius: '2px'
                      }}></div>
                      <Text styles={{ root: { fontSize: '12px' } }}>Near Budget</Text>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: RED_400,
                        marginRight: '5px',
                        borderRadius: '2px'
                      }}></div>
                      <Text styles={{ root: { fontSize: '12px' } }}>Over Budget</Text>
                    </div>
                  </div>
                </Stack>
              </Stack.Item>
            </Stack>
          </Stack>
      )}
    </Stack>
  );
};

export default CostAnalytics;
