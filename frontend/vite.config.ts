import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import { fileURLToPath } from 'url'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  // Load environment variables from the root directory
  envDir: '../',
  plugins: [react()],
  optimizeDeps: {
    include: [
      'react-syntax-highlighter/dist/cjs/styles/prism'
    ]
  },
  resolve: {
    alias: {
      'react-syntax-highlighter/dist/cjs/styles/prism': 'react-syntax-highlighter/dist/cjs/styles/prism',
      crypto: path.resolve(__dirname, 'crypto-polyfill.js'),
      refractor: path.resolve(__dirname, 'refractor-mock/index.js'),
      'refractor/core': path.resolve(__dirname, 'refractor-mock/core.js')
    }
  },
  build: {
    outDir: '../static',
    emptyOutDir: true,
    sourcemap: true, // Enable sourcemaps for debugging
    minify: 'esbuild', // Use esbuild instead of terser for better Node 16 compatibility
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      plugins: [],
      // Exclude problematic files from the build
      external: [
        'refractor/lang/php-extras.js',
        'refractor/node_modules/prismjs/components/prism-core.js',
        'refractor/index.js',
        'refractor'
      ],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          azure: ['@azure/msal-browser', '@azure/msal-react'],
          ui: ['@fluentui/react', '@fluentui/react-icons'],
          charts: ['chart.js', 'react-chartjs-2']
        }
      }
    }
  },
  define: {
    // Polyfill for Node.js crypto module
    'process.env': {},
    'global': {}
  },
  server: {
    port: 5000,
    proxy: {
      '/ask': 'http://localhost:50505',
      '/chat': 'http://localhost:50505',
      '/api': {
        target: 'http://localhost:50505',
        ws: true,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  }
})

