#!/usr/bin/env python3
"""
Deploy project resources using the Azure Python SDK and an ACR-based Function App.
This script is called by app.py when a new project is created.

This script is responsible for:
1. Provisioning the core infrastructure with Azure SDKs (storage, search, etc.)
2. Deploying the Function App from ACR
# NOTE: Resource creation now relies entirely on Azure SDK clients; any remaining Bicep references are legacy.
3. Updating the deployment status in CosmosDB
"""

import os
import sys
import json
import time
import logging
import uuid
import re
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple, Callable
import asyncio
import aiohttp
import requests
from backend.utils.azure_credentials import get_management_credential

from backend.utils.azure_resource_helpers import (
    create_storage_account_and_containers,
    create_search_service_and_resources,
    create_function_app_from_acr,
    create_event_grid_topic_and_subscription,
)
from azure.mgmt.resource import ResourceManagementClient

from azure.mgmt.web import WebSiteManagementClient
from azure.mgmt.web.models import (
    AppServicePlan,
    SkuDescription,
    Site,
    ManagedServiceIdentity,
    NameValuePair,
    SiteConfigResource
)
from azure.mgmt.storage import StorageManagementClient
from azure.mgmt.eventgrid import EventGridManagementClient
from azure.mgmt.search import SearchManagementClient
from azure.mgmt.storage.models import (
    StorageAccountCreateParameters,
    Sku,
    SkuName,
    Kind,
    Identity,
    StorageAccountUpdateParameters,
)
from azure.mgmt.resource.resources.models import (
    DeploymentMode,
    Deployment,
    DeploymentProperties,
    TemplateLink,
    DeploymentWhatIf,
    DeploymentWhatIfProperties,
    DeploymentMode,
)
from azure.core.exceptions import ResourceNotFoundError
from azure.storage.blob import generate_account_sas, ResourceTypes, AccountSasPermissions
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/cosmos_db.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Try to import the deployment status module if available
try:
    from backend.deployments.deployment_status import (
        DeploymentSummary,
        update_project_deployment_status,
    )

    using_deployment_status_module = True
except ImportError:
    DeploymentSummary = None
    using_deployment_status_module = False
    logger.warning(
        "Could not import DeploymentSummary from backend.deployments.deployment_status. Using fallback status updates."
    )

# Try to import the RBAC client if available
try:
    from backend.rbac.rbac_routes import rbac_client

    using_rbac_client = True
except ImportError:
    rbac_client = None
    using_rbac_client = False

# Try to import the update_project_with_deployment function
try:
    from update_project_with_deployment import update_project_with_deployment

    logging.info("Successfully imported update_project_with_deployment function")
except ImportError:
    update_project_with_deployment = None
    logging.warning(
        "Could not import update_project_with_deployment function, fallback method will not be available"
    )
    logger.warning(
        "Could not import rbac_client from backend.rbac.rbac_routes. Project creation in RBAC service will be skipped."
    )

# Try to import the project update functions if available
try:
    from update_project_with_deployment import update_project_with_deployment

    update_project_available = True
except ImportError as e:
    update_project_with_deployment = None
    update_project_available = False
    logger.warning(
        f"Could not import update_project_with_deployment: {e}. Project updates will be skipped."
    )

# Try to import the project creation function if available
try:
    from backend.rbac.rbac_routes import create_project

    create_project_available = True
except ImportError as e:
    create_project = None
    create_project_available = False
    logger.warning(
        f"Could not import create_project: {e}. Project creation will be skipped."
    )


def is_valid_uuid(uuid_to_test: str, version: int = 4) -> bool:
    """
    Check if uuid_to_test is a valid UUID.

    Args:
        uuid_to_test (str): String to test
        version (int): UUID version (1, 3, 4, or 5)

    Returns:
        bool: True if valid UUID, False otherwise
    """
    try:
        uuid_obj = uuid.UUID(str(uuid_to_test), version=version)
        return str(uuid_obj) == str(uuid_to_test).lower()
    except (ValueError, AttributeError, TypeError) as e:
        logger.warning(f"Invalid UUID format for '{uuid_to_test}': {e}")
        return False


def validate_project_parameters(
    project_id: str, project_name: str, region_id: str
) -> bool:
    """
    Validate project parameters before processing.

    Args:
        project_id (str): The project ID to validate
        project_name (str): The project name to validate
        region_id (str): The region ID to validate

    Returns:
        bool: True if all parameters are valid, False otherwise
    """
    if not is_valid_uuid(project_id):
        logger.error(
            f"Invalid project_id format: {project_id}. Must be a valid UUID v4."
        )
        return False

    if (
        not project_name
        or not isinstance(project_name, str)
        or len(project_name.strip()) == 0
    ):
        logger.error("Project name cannot be empty")
        return False

    if not region_id or not isinstance(region_id, str) or len(region_id.strip()) == 0:
        logger.error("Region ID cannot be empty")
        return False

    return True


# Constants for ACR deployment
ACR_NAME = "functionappaiscope"
FUNCTIONS_CONTAINER_IMAGE_NAME = "functionapp"
FUNCTIONS_CONTAINER_IMAGE_TAG = "latest"
SHARED_SEARCH_SERVICE_NAME = "search-shared-service"
SHARED_OPENAI_SERVICE_NAME = "your-openai-service"

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed to DEBUG level for more detailed logs
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)

# Create a file handler for detailed logs
os.makedirs("logs", exist_ok=True)
detailed_log_file = f"logs/deploy_project_resources_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
file_handler = logging.FileHandler(detailed_log_file)
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
file_handler.setFormatter(file_formatter)
logging.getLogger().addHandler(file_handler)

logging.info(f"Detailed logs will be written to {detailed_log_file}")

# ACR Configuration
ACR_NAME = os.environ.get("ACR_NAME", "functionappaiscope")
FUNCTIONS_CONTAINER_IMAGE_NAME = os.environ.get(
    "FUNCTIONS_CONTAINER_IMAGE_NAME", "functionapp"
)
FUNCTIONS_CONTAINER_IMAGE_TAG = os.environ.get(
    "FUNCTIONS_CONTAINER_IMAGE_TAG", "latest"
)
SHARED_SEARCH_SERVICE_NAME = os.environ.get("SHARED_SEARCH_SERVICE_NAME", "")
SHARED_OPENAI_SERVICE_NAME = os.environ.get(
    "SHARED_OPENAI_SERVICE_NAME", "openai-service"
)

# Initialize management clients
subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
credential = get_management_credential()

web_client = WebSiteManagementClient(credential, subscription_id) if subscription_id else None
storage_client = StorageManagementClient(credential, subscription_id) if subscription_id else None
eventgrid_client = EventGridManagementClient(credential, subscription_id) if subscription_id else None
search_client = SearchManagementClient(credential, subscription_id) if subscription_id else None


async def update_deployment_status(
    project_id, status_data, api_url="http://localhost:50505"
):
    """
    Update the deployment status via the API endpoint or using the deployment status module.

    Args:
        project_id (str): The ID of the project
        status_data (dict): Dictionary containing the status update
        api_url (str): The base URL of the API

    Returns:
        bool: True if the update was successful, False otherwise
    """
    # If we're using the new deployment status module, use it
    if using_deployment_status_module and update_project_deployment_status is not None:
        try:
            await update_project_deployment_status(
                project_id=project_id,
                status=status_data["status"],
                message=status_data["message"],
                details=status_data.get("details"),
                error=status_data.get("error"),
                api_url=api_url,
            )

            # If there are resources in the status data, add them
            if "resources" in status_data and add_deployment_resource is not None:
                for resource_name, resource_value in status_data["resources"].items():
                    await add_deployment_resource(
                        project_id=project_id,
                        resource_name=resource_name,
                        resource_value=resource_value,
                        api_url=api_url,
                    )

            # If there's an error, add it
            if (
                "error" in status_data
                and status_data["error"]
                and add_deployment_error is not None
            ):
                await add_deployment_error(
                    project_id=project_id, error=status_data["error"], api_url=api_url
                )

            logging.info(
                f"Successfully updated deployment status using deployment status module: {status_data['status']}"
            )
            return True
        except Exception as e:
            logging.error(
                f"Error updating deployment status using deployment status module: {e}"
            )
            # Fall back to the old method
            logging.info("Falling back to direct API call for deployment status update")

    # If we're not using the new module or it failed, use the old method
    update_url = f"{api_url}/api/projects/{project_id}/deployment-status"

    try:
        # Add headers to indicate we're using Azure CLI credentials
        headers = {
            "Content-Type": "application/json",
            "X-Azure-CLI-Credentials": "true",
        }

        # Send the update
        logging.info(f"Sending status update to {update_url}")

        try:
            if requests is None:
                logging.warning(
                    f"Requests module not available - skipping API update to {update_url}"
                )
                logging.info(f"Status data that would have been sent: {status_data}")
                return True  # Return True to allow deployment to continue

            response = requests.post(
                update_url, json=status_data, headers=headers, timeout=5
            )

            if response.status_code != 200:
                logging.error(
                    f"Error updating deployment status: {response.status_code} - {response.text}"
                )
                return False
            else:
                logging.info(
                    f"Successfully updated deployment status: {response.json()}"
                )
                return True
        except Exception as e:
            if (
                requests is not None
                and hasattr(requests, "exceptions")
                and isinstance(e, requests.exceptions.ConnectionError)
            ):
                logging.warning(
                    f"Could not connect to API at {update_url} - continuing without status updates"
                )
                # Log the status data for debugging
                logging.info(f"Status data that would have been sent: {status_data}")
                return True  # Return True to allow deployment to continue
            else:
                logging.error(f"Error making request to update deployment status: {e}")
                return False
    except Exception as e:
        logging.error(f"Error updating deployment status: {e}")
        return False


async def update_project_resources(
    project_id, resource_data, api_url="http://localhost:50505"
):
    """
    Update the project in CosmosDB with the actual resource names from Azure deployment.

    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names
        api_url (str): The base URL of the API (not used for direct update)

    Returns:
        bool: True if update was successful, False otherwise
    """
    # Validate project_id
    if not project_id or project_id == "11111111-1111-1111-1111-111111111111":
        logging.error("Invalid project ID for update")
        return False

    logging.info(
        f"Updating project {project_id} with resource data: {json.dumps(resource_data, indent=2)}"
    )

    # Try direct Cosmos DB update first
    try:
        # Import the direct update function
        from direct_cosmos_update import update_project_resources_direct

        # Call the direct update function
        direct_update_result = update_project_resources_direct(
            project_id, resource_data
        )
        if direct_update_result:
            logging.info(
                f"Successfully updated project {project_id} directly in Cosmos DB"
            )
            return True
        else:
            logging.warning(
                f"Direct Cosmos DB update failed for project {project_id}, trying fallback methods"
            )
    except Exception as e:
        logging.error(f"Error during direct Cosmos DB update: {e}")
        import traceback

        logging.error(f"Stack trace: {traceback.format_exc()}")
        logging.warning("Trying fallback methods")

    # Fallback to API endpoint if direct update failed
    try:
        # Add headers to indicate we're using Azure CLI credentials
        headers = {
            "Content-Type": "application/json",
            "X-Azure-CLI-Credentials": "true",
        }

        # Send the update
        update_url = f"{api_url}/api/projects/{project_id}/resources"
        logging.info(f"Sending resource update to {update_url}")

        # Import requests only when needed
        import requests

        response = requests.post(
            update_url, json=resource_data, headers=headers, timeout=10
        )

        if response.status_code != 200:
            logging.error(
                f"Error updating project resources via API: {response.status_code} - {response.text}"
            )
            # Try one more fallback method
            return await update_project_resources_fallback(project_id, resource_data)
        else:
            logging.info(
                f"Successfully updated project resources via API: {response.json()}"
            )
            return True
    except Exception as e:
        logging.error(f"Error updating project resources via API: {e}")
        import traceback

        logging.error(f"Stack trace: {traceback.format_exc()}")
        # Try one more fallback method
        return await update_project_resources_fallback(project_id, resource_data)


async def update_project_resources_fallback(project_id, resource_data):
    """
    Fallback method to update project resources in Cosmos DB.

    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names

    Returns:
        bool: True if update was successful, False otherwise
    """
    # Validate project_id
    if not project_id or project_id == "11111111-1111-1111-1111-111111111111":
        logging.error("Invalid project ID for fallback update")
        return False

    logging.info(f"Attempting fallback update for project {project_id}")

    # Try to import the update_project_with_deployment function
    try:
        from update_project_with_deployment import update_project_with_deployment

        update_project_available = True
    except ImportError:
        update_project_with_deployment = None
        update_project_available = False

    if update_project_available and update_project_with_deployment is not None:
        logging.info(
            "Attempting to update project resources using fallback method (update_project_with_deployment)"
        )

        # Create a deployment summary structure that update_project_with_deployment expects
        # First, try to load the latest deployment summary from the logs directory
        latest_summary = None
        try:
            # Find the latest deployment summary file for this project
            import glob

            summary_files = glob.glob(f"logs/deployment_summary_*_{project_id}.json")
            if summary_files:
                # Sort by modification time (newest first)
                latest_summary_file = max(summary_files, key=os.path.getmtime)
                logging.info(
                    f"Found latest deployment summary file: {latest_summary_file}"
                )

                # Load the summary file
                with open(latest_summary_file, "r") as f:
                    latest_summary = json.load(f)
                logging.info(
                    f"Successfully loaded deployment summary from {latest_summary_file}"
                )
        except Exception as e:
            logging.warning(f"Error loading latest deployment summary: {e}")
            latest_summary = None

        # If we found a valid summary file, use it; otherwise, create a minimal one
        if (
            latest_summary
            and isinstance(latest_summary, dict)
            and "resources" in latest_summary
        ):
            deployment_summary = latest_summary
            # Update the status to partial_success since we're in the fallback path
            deployment_summary["status"] = "partial_success"
            logging.info("Using existing deployment summary with updated status")
        else:
            # Create a minimal deployment summary
            deployment_summary = {
                "project_id": project_id,
                "resources": resource_data,
                "status": "partial_success",  # Use partial_success since we're in the fallback path
                "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
            }
            logging.info(
                "Created minimal deployment summary (no existing summary found)"
            )

        # Call the update_project_with_deployment function
        try:
            update_result = update_project_with_deployment(
                project_id, deployment_summary
            )
            if update_result:
                logging.info(
                    f"Successfully updated project {project_id} using update_project_with_deployment"
                )
                return True
            else:
                logging.error(
                    f"Failed to update project {project_id} using update_project_with_deployment"
                )
        except Exception as e:
            logging.error(f"Error in update_project_with_deployment: {e}")
            import traceback

            logging.error(f"Stack trace: {traceback.format_exc()}")

    # Try to use the RBAC service directly
    try:
        # Try to import the RBAC service
        from backend.rbac.cosmosdb_rbac_service import CosmosDBRbacService

        # Initialize the RBAC service
        rbac_service = CosmosDBRbacService()

        # Get the project to find its region
        project = await rbac_service.get_project(project_id)
        if not project:
            logging.error(f"Project {project_id} not found during fallback update")
            return False

        region = project.get(
            "region", "westeurope"
        )  # Default to westeurope if not found

        # We still need the region for logging purposes, but we won't use it for the update
        if region == project_id:
            logging.warning(
                f"Region parameter ({region}) matches project_id, this is likely an error. Using 'westeurope' as default."
            )
            region = "westeurope"

        # Update the project - note that region is still passed but won't be used for partition key
        updated_project = await rbac_service.update_project(
            project_id=project_id,
            region=region,  # This is only used for logging now
            project_data=resource_data,
            etag=None,  # Explicitly set ETag to None to ignore it
        )

        if updated_project:
            logging.info(f"Successfully updated project {project_id} via RBAC service")
            return True
        else:
            logging.error(f"Failed to update project {project_id} via RBAC service")
            return False
    except Exception as e:
        logging.error(f"Error in RBAC service update: {e}")
        import traceback

        logging.error(f"Stack trace: {traceback.format_exc()}")
        return False


def run_main_bicep_deployment(
    project_id: str,
    project_name: str,
    region_id: str,
    resource_group: str = "rg-internal-ai",
    location: str = "westeurope",
    function_app_id: Optional[str] = None,
    template_file: Optional[str] = None,  # kept for compatibility
    deploy_event_grid_only: bool = False,  # kept for backwards compatibility
    storage_account_id: Optional[str] = None,
):

    """Provision core resources using the Azure SDK instead of Bicep.

    This mirrors the configuration from the old Bicep templates and returns a
    tuple of ``(deployment_outputs, resource_data)``.
    """


    credential = get_management_credential()
    subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
    if not subscription_id:
        raise Exception("AZURE_SUBSCRIPTION_ID environment variable not set")

    storage_client = StorageManagementClient(credential, subscription_id)
    search_client = SearchManagementClient(credential, subscription_id)

    # ------------------------------------------------------------------
    # Name generation (replicates Bicep logic)
    sanitized_name = re.sub(r"[^a-z0-9-]", "", project_name.lower().replace(" ", "-"))
    storage_base = sanitized_name.replace("-", "")
    unique_suffix = hashlib.md5(project_id.encode()).hexdigest()[:4]
    storage_trunc = storage_base[:16] if len(storage_base) > 16 else storage_base

    storage_account_name = f"st{storage_trunc}{unique_suffix}"
    search_service_name = f"search-{sanitized_name}-{unique_suffix}"
    uploads_container = f"uploads-{sanitized_name}-{unique_suffix}"
    input_container = f"input-{sanitized_name}-{unique_suffix}"
    output_container = f"output-{sanitized_name}-{unique_suffix}"
    search_index_name = f"project-{sanitized_name}-index"
    search_indexer_name = f"project-{sanitized_name}-indexer"
    search_datasource_name = f"project-{sanitized_name}-ds"

    tags = {
        "project-id": project_id,
        "region-id": region_id,
        "project-name": project_name,
    }

    # ------------------------------------------------------------------
    # Storage account and containers
    storage_params = StorageAccountCreateParameters(
        sku=Sku(name=SkuName.standard_lrs),
        kind=Kind.storage_v2,
        location=location,
        tags=tags,
        encryption={
            "services": {
                "blob": {"keyType": "Account", "enabled": True},
                "file": {"keyType": "Account", "enabled": True},
            },
            "keySource": "Microsoft.Storage",
        },
        access_tier="Hot",
    )

    storage_client.storage_accounts.begin_create(
        resource_group, storage_account_name, storage_params
    ).result()

    # Containers
    for c in [uploads_container, input_container, output_container]:
        storage_client.blob_containers.create(
            resource_group, storage_account_name, c, {}
        )

    keys = storage_client.storage_accounts.list_keys(resource_group, storage_account_name)
    account_key = keys.keys[0].value
    storage_connection = (
        f"DefaultEndpointsProtocol=https;AccountName={storage_account_name};"
        f"AccountKey={account_key};EndpointSuffix=core.windows.net"
    )

    # ------------------------------------------------------------------
    # Search service and resources
    search_service_async = search_client.services.begin_create_or_update(
        resource_group,
        search_service_name,
        {
            "location": location,
            "sku": {"name": "standard"},
            "tags": tags,
            "replica_count": 1,
            "partition_count": 1,
            "hosting_mode": "default",
        },
    )
    search_service_async.result()

    admin_keys = search_client.admin_keys.get(resource_group, search_service_name)
    search_key = admin_keys.primary_key

    headers = {"Content-Type": "application/json", "api-key": search_key}
    endpoint = f"https://{search_service_name}.search.windows.net"

    # Datasource
    ds_body = {
        "name": search_datasource_name,
        "type": "azureblob",
        "credentials": {"connectionString": storage_connection},
        "container": {"name": uploads_container},
    }
    requests.put(
        f"{endpoint}/datasources/{search_datasource_name}?api-version=2023-11-01",
        headers=headers,
        json=ds_body,
    )

    # Index
    index_body = {
        "name": search_index_name,
        "fields": [
            {"name": "id", "type": "Edm.String", "key": True, "searchable": True, "filterable": True, "sortable": True, "facetable": False},
            {"name": "content", "type": "Edm.String", "searchable": True},
            {"name": "metadata_storage_name", "type": "Edm.String", "searchable": True, "filterable": True, "sortable": True, "facetable": True},
            {"name": "metadata_storage_path", "type": "Edm.String", "searchable": False, "filterable": True, "sortable": True},
            {"name": "metadata_content_type", "type": "Edm.String", "searchable": True, "filterable": True, "sortable": True, "facetable": True},
            {"name": "contentVector", "type": "Collection(Edm.Single)", "searchable": True, "retrievable": True, "dimensions": 1536, "vectorSearchProfile": "default-vector-profile"},
        ],
        "vectorSearch": {
            "algorithms": [
                {
                    "name": "my-hnsw-config-1",
                    "kind": "hnsw",
                    "hnswParameters": {
                        "m": 4,
                        "efConstruction": 400,
                        "efSearch": 500,
                        "metric": "cosine",
                    },
                }
            ],
            "profiles": [
                {"name": "default-vector-profile", "algorithm": "my-hnsw-config-1"}
            ],
        },
    }
    requests.put(
        f"{endpoint}/indexes/{search_index_name}?api-version=2023-11-01",
        headers=headers,
        json=index_body,
    )

    # Indexer
    indexer_body = {
        "name": search_indexer_name,
        "dataSourceName": search_datasource_name,
        "targetIndexName": search_index_name,
        "parameters": {"configuration": {"parsingMode": "default", "indexStorageMetadataOnlyForOversizedDocuments": True}},
        "schedule": {"interval": None},
    }
    requests.put(
        f"{endpoint}/indexers/{search_indexer_name}?api-version=2023-11-01",
        headers=headers,
        json=indexer_body,
    )

    resource_data = {
        "storage_account_name": storage_account_name,
        "storage_connection_string": storage_connection,
        "uploads_container": uploads_container,
        "input_container": input_container,
        "output_container": output_container,
        "search_service_name": search_service_name,
        "search_api_key": search_key,
        "search_index_name": search_index_name,
        "search_indexer_name": search_indexer_name,
        "search_datasource_name": search_datasource_name,
    }

    deployment_outputs = {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": region_id,
        "resources": resource_data,
        "status": "success",
    }

    return deployment_outputs, resource_data




async def generate_deployment_summary(project_id: str, project_name: str, region_id: str, resource_group: str,
                                   start_time: datetime, resource_data: Dict[str, Any],
                                   main_bicep_outputs: Dict[str, Any], resource_durations: Dict[str, float],
                                   status: str = "success",
                                   auto_update_project: bool = True, api_url: str = "http://localhost:50505") -> Optional[str]:

    """
    Generate a deployment summary JSON file with all the deployment information and update the project in CosmosDB.

    Args:
        project_id (str): The ID of the project (must be a valid UUID v4)
        project_name (str): The name of the project
        region_id (str): The Azure region ID
        resource_group (str): The resource group name
        start_time (datetime): The start time of the deployment
        resource_data (dict): Dictionary containing resource names from deployment
        main_bicep_outputs (dict): The outputs from the main Bicep deployment
        resource_durations (dict): Duration in seconds for each resource step
        status (str): The deployment status (success, partial_success, or failed)
        auto_update_project (bool): Whether to automatically update the project in CosmosDB
        api_url (str): The base URL of the API

    Returns:
        Optional[str]: Path to the generated summary file, or None if failed
    """
    # Validate input parameters
    if not validate_project_parameters(project_id, project_name, region_id):
        logger.error(
            "Invalid project parameters provided to generate_deployment_summary"
        )
        return None

    logger.info(
        f"[generate_deployment_summary] Starting deployment summary generation for project {project_id}"
    )
    logger.debug(
        f"[generate_deployment_summary] Project name: {project_name}, Region: {region_id}, Status: {status}"
    )
    logging.info(
        f"Generating deployment summary for project {project_id} with status {status}"
    )

    # Calculate deployment time
    end_time = datetime.now(timezone.utc)
    deployment_time = end_time - start_time
    deployment_time_str = f"{deployment_time.total_seconds():.1f}s"

    # Initialize summary dictionary
    summary = {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": region_id,
        "resources": {
            "storage_account_name": "",
            "storage_account_sas_token": "",
            "uploads_container": "",
            "input_container": "",
            "output_container": "",
            "search_service_name": "",
            "search_index_name": "",
            "search_indexer_name": "",
            "search_key": "",
            "search_api_version": "2021-04-30-Preview",
            "search_datasource_name": "",
            "function_app_name": "",
            "function_app_url": "",
            "function_key_maturity": "",
            "function_key_executive_summary": "",
            "function_key_powerpoint": "",
            "event_grid_subscription_name": "",
            "azure_function_maturity_assessment_url": "",
            "azure_function_executive_summary_url": "",
        },
        "status": status,
        "deployment_time": deployment_time_str,

        "resource_durations": resource_durations,
        "timestamp": end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

    }

    # Fill in resource data primarily from main_bicep_outputs['resources'] (using snake_case keys from shell script JSON)
    bicep_resources = main_bicep_outputs.get("resources", {})
    logging.debug(
        f"Populating summary from main_bicep_outputs resources: {bicep_resources}"
    )
    summary["resources"]["storage_account_name"] = bicep_resources.get(
        "storage_account_name", ""
    )
    summary["resources"]["uploads_container"] = bicep_resources.get(
        "uploads_container", ""
    )
    summary["resources"]["input_container"] = bicep_resources.get("input_container", "")
    summary["resources"]["output_container"] = bicep_resources.get(
        "output_container", ""
    )
    summary["resources"]["search_service_name"] = bicep_resources.get(
        "search_service_name", ""
    )
    summary["resources"]["search_index_name"] = bicep_resources.get(
        "search_index_name", ""
    )
    summary["resources"]["search_indexer_name"] = bicep_resources.get(
        "search_indexer_name", ""
    )
    summary["resources"]["search_datasource_name"] = bicep_resources.get(
        "search_datasource_name", ""
    )
    # Note: search_key is typically not an output of the main bicep, retrieved later if needed

    # Fill in/overwrite with data collected during the Python script execution (resource_data)
    # This is important for Function App and Event Grid details populated after the main Bicep run
    logging.debug(f"Populating/overwriting summary from resource_data: {resource_data}")
    if resource_data.get("storage_account_name"):
        summary["resources"]["storage_account_name"] = resource_data[
            "storage_account_name"
        ]
    if resource_data.get("search_service_name"):
        summary["resources"]["search_service_name"] = resource_data[
            "search_service_name"
        ]
    if resource_data.get("function_app_name"):
        summary["resources"]["function_app_name"] = resource_data["function_app_name"]
        summary["resources"][
            "function_app_url"
        ] = f"https://{resource_data['function_app_name']}.azurewebsites.net"
    if resource_data.get("event_grid_subscription_name"):
        summary["resources"]["event_grid_subscription_name"] = resource_data[
            "event_grid_subscription_name"
        ]

    # Generate SAS token for the main project storage account
    if summary["resources"]["storage_account_name"]:
        try:
            # Generate SAS token with all permissions for all services, valid for 1 year
            expiry_date = (datetime.now(timezone.utc) + timedelta(days=365)).strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            )
            subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
            credential = get_management_credential()
            storage_client = StorageManagementClient(credential, subscription_id)
            keys = storage_client.storage_accounts.list_keys(
                resource_group, summary["resources"]["storage_account_name"]
            )
            account_key = keys.keys[0].value if keys.keys else None
            if account_key:
                sas_token = generate_account_sas(
                    account_name=summary["resources"]["storage_account_name"],
                    account_key=account_key,
                    resource_types=ResourceTypes(service=True, container=True, object=True),
                    permission=AccountSasPermissions(read=True, write=True, delete=True, list=True, add=True, create=True, update=True, process=True),
                    expiry=datetime.utcnow() + timedelta(days=365),
                )
                summary["resources"]["storage_account_sas_token"] = sas_token
                resource_data["storage_account_sas_token"] = sas_token
                logging.debug("Added storage_account_sas_token to resource_data")
                logging.info(
                    f"Generated SAS token for storage account {summary['resources']['storage_account_name']}"
                )
            else:
                logging.warning("Could not retrieve storage account key for SAS generation")
        except Exception as e:
            logging.error(f"Error generating SAS token: {e}")
        finally:
            # Always log the SAS token generation result
            logging.info(
                f"SAS token generation for storage account {summary['resources']['storage_account_name']} completed."
            )

    # Get function keys if function app exists
    if summary["resources"]["function_app_name"]:
        try:
            # Use correct function names as provided by user
            function_names = [
                "HttpTriggerAppMaturityAssessment",
                "HttpTriggerAppExecutiveSummary",
                "HttpTriggerPowerPointGenerator",
            ]
            function_key_fields = [
                "function_key_maturity",
                "function_key_executive_summary",
                "function_key_powerpoint",
            ]
            function_url_fields = [
                "azure_function_maturity_assessment_url",
                "azure_function_executive_summary_url",
                None,
            ]
            function_endpoints = [
                "HttpTriggerAppMaturityAssessment",
                "HttpTriggerAppExecutiveSummary",
                None,
            ]

            # Ensure search key is populated (might be needed by functions, retrieve if not in bicep outputs)
            if (
                not summary["resources"]["search_key"]
                and summary["resources"]["search_service_name"]
            ):
                try:
                    logging.info(
                        f"Retrieving search key for summary for service: {summary['resources']['search_service_name']}"
                    )
                    subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
                    credential = get_management_credential()
                    search_client = SearchManagementClient(credential, subscription_id)
                    keys = search_client.admin_keys.get(resource_group, summary["resources"]["search_service_name"])
                    search_key = keys.primary_key
                    summary["resources"]["search_key"] = search_key
                    logging.info("Successfully retrieved search key for summary.")
                except Exception as e:
                    logging.error(f"Error retrieving search key: {e}")
                finally:
                    logging.info(
                        f"Search key retrieval for service {summary['resources']['search_service_name']} completed."
                    )

            # Get function keys
            for i, function_name in enumerate(function_names):
                if function_name:
                    try:
                        # Get the function key
                        logging.info(f"Getting function key for {function_name}")
                        try:
                            keys = web_client.web_apps.list_function_keys(
                                resource_group,
                                summary["resources"]["function_app_name"],
                                function_name,
                            )
                            key = (
                                getattr(keys, "default", None)
                                or (getattr(keys, "properties", {}) or {}).get("default")
                            )
                            if key:
                                summary["resources"][function_key_fields[i]] = key
                                logging.info(
                                    f"Successfully retrieved function key for {function_name}"
                                )
                            else:
                                summary["resources"][function_key_fields[i]] = ""
                                logging.warning(
                                    f"Function key for {function_name} not found"
                                )
                        except Exception as e:
                            summary["resources"][function_key_fields[i]] = ""
                            logging.error(
                                f"Error retrieving function key for {function_name}: {e}"
                            )
                    except Exception as e:
                        logging.error(
                            f"Error retrieving function key for {function_name}: {e}"
                        )
                    finally:
                        # Always log the function key retrieval result
                        logging.info(
                            f"Function key retrieval for {function_name} completed."
                        )
        except Exception as e:
            logging.error(f"Error retrieving function keys: {e}")

        try:
            # First try to update the project
            if not update_project_available or update_project_with_deployment is None:
                logger.warning(
                    "update_project_with_deployment function not available. Cannot update project in CosmosDB."
                )
                return None

            logger.info(
                f"[generate_deployment_summary] Attempting to update project {project_id} with deployment summary"
            )

            # Prepare project data for RBAC service
            project_data = {
                "id": project_id,
                "name": project_name,
                "region": region_id,
                "status": status,
                "last_updated": datetime.now(timezone.utc).isoformat(),
            }

            # First attempt to update the project
            logger.debug(
                f"[generate_deployment_summary] First update attempt for project {project_id}"
            )
            update_success = update_project_with_deployment(project_id, summary)

            if not update_success:
                # If update failed, create the project first
                logger.warning(
                    f"[generate_deployment_summary] Project {project_id} not found in CosmosDB, attempting to create it"
                )

                if not create_project_available or create_project is None:
                    logger.error(
                        "create_project function not available. Cannot create new project."
                    )
                    return None

                try:
                    # Create the project using RBAC service
                    logger.info(
                        f"[generate_deployment_summary] Creating new project with ID: {project_id}"
                    )
                    create_project(project_data)
                    logger.info(
                        f"[generate_deployment_summary] Successfully created project {project_id}"
                    )

                    # Wait a moment for the project to be fully created
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(
                        f"[generate_deployment_summary] Error creating project {project_id}: {str(e)}",
                        exc_info=True,
                    )
                    return None

            # Now update the project with deployment details
            logger.info(
                f"[generate_deployment_summary] Updating project {project_id} with deployment summary"
            )
            update_success = update_project_with_deployment(project_id, summary)

            if not update_success:
                logger.error(
                    f"[generate_deployment_summary] Failed to update project {project_id} after creation"
                )
                return None

            logger.info(
                f"[generate_deployment_summary] Successfully updated project {project_id} in CosmosDB"
            )

            # Get the RBAC client from the API URL
            from aiohttp import ClientSession
            from backend.rbac.rbac_routes import rbac_client

            try:
                async with ClientSession() as session:
                    async with session.get(f"{api_url}/api/health") as response:
                        if response.status == 200:
                            # Use the RBAC client to create the project
                            created_project = await rbac_client.create_project(
                                project_data
                            )
                            if created_project:
                                logging.info(
                                    f"Successfully created new project {project_id} with deployment summary"
                                )
                                create_success = True
                            else:
                                logging.error(
                                    f"Failed to create new project {project_id} with deployment summary"
                                )
                                create_success = False
                        else:
                            logging.error(
                                f"Unable to connect to API service at {api_url}"
                            )
                            create_success = False
            except Exception as e:
                logging.error(f"Error creating project {project_id}: {e}")
                create_success = False

            # First save the summary to a file with timestamp
            resource_count = len([v for v in summary["resources"].values() if v])
            summary["deployment_total"] = resource_count

            timestamp_str = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            summary_file = f"deployment-summary-{timestamp_str}-{project_id}.json"
            with open(summary_file, "w") as f:
                json.dump(summary, f, indent=2)
            logging.info(f"Saved deployment summary to {summary_file}")

            # Then try to update the project with the summary file
            try:
                with open(summary_file, "r") as f:
                    summary_data = json.load(f)
                if update_project_with_deployment is not None:
                    try:
                        logging.info(
                            f"Attempting to update project {project_id} with summary file"
                        )
                        update_success = update_project_with_deployment(
                            project_id, summary_data
                        )
                        if update_success:
                            logging.info(
                                f"Successfully updated project {project_id} with summary file"
                            )
                        else:
                            logging.warning(
                                f"Failed to update project {project_id} with summary file"
                            )
                    except Exception as e:
                        logging.error(
                            f"Error updating project {project_id} with summary file: {e}"
                        )
            except Exception as e:
                logging.error(f"Error reading summary file {summary_file}: {e}")

            # Update CosmosDB with deployment details
            try:
                # Update project resources in CosmosDB
                await update_project_resources(
                    project_id, summary["resources"], api_url=api_url
                )
                logging.info(
                    f"Successfully updated project {project_id} in CosmosDB with deployment details"
                )
            except Exception as e:
                logging.error(f"Error updating project {project_id} in CosmosDB: {e}")

            return summary_file
        except Exception as e:
            logging.error(f"Error in deployment summary generation: {e}")
            return None


def check_function_app_exists(project_id, resource_group):
    """
    Check if a Function App exists for the given project ID.

    Args:
        project_id (str): The ID of the project
        resource_group (str): The resource group name

    Returns:
        str: The name of the Function App if found, None otherwise
    """
    logging.info(f"Checking if Function App exists for project {project_id}")

    global web_client

    try:
        if web_client:
            apps = web_client.web_apps.list_by_resource_group(resource_group)
            for app in apps:
                if project_id in app.name:
                    logging.info(f"Found Function App: {app.name}")
                    return app.name
            logging.warning(f"No Function App found for project {project_id}")
            return None
        # Fallback to Azure CLI if client not available
        cmd = [
            "az",
            "functionapp",
            "list",
            "--resource-group",
            resource_group,
            "--query",
            f"[?contains(name, '{project_id}')].name",
            "-o",
            "tsv",
        ]


        credential = get_management_credential()
        web_client = WebSiteManagementClient(credential, subscription_id)


        if process.returncode == 0 and process.stdout.strip():
            function_app_name = process.stdout.strip()
            logging.info(f"Found Function App via CLI: {function_app_name}")
            return function_app_name
        else:
            logging.warning(f"No Function App found for project {project_id}")
            return None

        logging.warning(f"No Function App found for project {project_id}")
        return None
    except Exception as e:
        logging.error(f"Error checking Function App existence: {e}")



def deploy_acr_function_app(
    project_id: str,
    project_name: str,
    resource_group: str,
    location: str,
    main_bicep_outputs: dict,
    resource_data: dict,
) -> str:
    """Deploy the Function App from ACR using the Azure SDK."""

    logging.info(
        f"Starting ACR Function App deployment for project {project_id}"
    )

    logging.debug("ACR Function App deployment parameters:")
    logging.debug("  project_id: %s", project_id)
    logging.debug("  project_name: %s", project_name)
    logging.debug("  resource_group: %s", resource_group)
    logging.debug("  location: %s", location)

    credential = get_management_credential()
    subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
    if not subscription_id:
        raise ValueError("AZURE_SUBSCRIPTION_ID environment variable not set")

    web_client = WebSiteManagementClient(credential, subscription_id)
    storage_client = StorageManagementClient(credential, subscription_id)

    # Generate names
    random_suffix = uuid.uuid4().hex[:4]
    function_app_name = f"func-{project_id}-{random_suffix}"
    app_service_plan_name = f"plan-{function_app_name}"

    # Generate Function App's dedicated storage account name
    # Ensure it's compliant: lowercase, no hyphens, 3-24 chars.
    clean_project_id_for_storage = project_id.replace("-", "").lower()
    func_storage_account_name_base = (
        f"stfunc{clean_project_id_for_storage}{random_suffix}"
    )
    func_storage_account_name = func_storage_account_name_base[:24]  # Max 24 chars
    logging.info(f"Generated Function App name: {function_app_name}")
    logging.info(
        f"Generated Function App's dedicated storage account name: {func_storage_account_name}"
    )

    # Create storage account and retrieve connection string
    storage_async = storage_client.storage_accounts.begin_create(
        resource_group_name=resource_group,
        account_name=func_storage_account_name,
        parameters=StorageAccountCreateParameters(
            sku=Sku(name=SkuName.standard_lrs),
            kind=Kind.storage_v2,
            location=location,
        ),
    )
    storage_async.result()

    keys = storage_client.storage_accounts.list_keys(
        resource_group, func_storage_account_name
    )
    primary_key = keys.keys[0].value
    func_storage_connection_string = (
        f"DefaultEndpointsProtocol=https;AccountName={func_storage_account_name};"
        f"AccountKey={primary_key};EndpointSuffix=core.windows.net"
    )

    search_api_key = os.environ.get("AZURE_SEARCH_KEY") or os.environ.get(
        "AZURE_AI_SEARCH_API_KEY"
    )
    openai_api_key = os.environ.get("AZURE_OPENAI_KEY") or os.environ.get(
        "OPENAI_API_KEY"
    )

    bicep_resources = main_bicep_outputs.get("resources", {})
    bicep_func_app_params = {
        "uploadsContainer": bicep_resources.get("uploads_container", ""),
        "inputContainer": bicep_resources.get("input_container", ""),
        "outputContainer": bicep_resources.get("output_container", ""),
        "searchIndexName": bicep_resources.get("search_index_name", ""),
        "searchIndexerName": bicep_resources.get("search_indexer_name", ""),
        "searchDatasourceName": bicep_resources.get("search_datasource_name", ""),
        "searchServiceName": resource_data.get(
            "search_service_name", SHARED_SEARCH_SERVICE_NAME
        ),
    }

    plan = web_client.app_service_plans.begin_create_or_update(
        resource_group_name=resource_group,
        name=app_service_plan_name,
        app_service_plan=AppServicePlan(
            location=location,
            sku=SkuDescription(name="B1", tier="Basic"),
            kind="linux",
            reserved=True,
        ),
    ).result()

    site = Site(
        location=location,
        kind="functionapp,linux,container",
        server_farm_id=plan.id,
        identity=ManagedServiceIdentity(type="SystemAssigned"),
    )

    web_client.web_apps.begin_create_or_update(
        resource_group_name=resource_group,
        name=function_app_name,
        site_envelope=site,
    ).result()

    acr_login_server = f"{ACR_NAME}.azurecr.io"
    linux_fx_version = (
        f"DOCKER|{acr_login_server}/{FUNCTIONS_CONTAINER_IMAGE_NAME}:{FUNCTIONS_CONTAINER_IMAGE_TAG}"
    )

    app_settings = [
        NameValuePair(name="AzureWebJobsStorage", value=func_storage_connection_string),
        NameValuePair(name="FUNCTIONS_EXTENSION_VERSION", value="~4"),
        NameValuePair(name="FUNCTIONS_WORKER_RUNTIME", value="python"),
        NameValuePair(name="WEBSITES_ENABLE_APP_SERVICE_STORAGE", value="false"),
        NameValuePair(name="DOCKER_REGISTRY_SERVER_URL", value=f"https://{acr_login_server}"),
        NameValuePair(name="__PROJECT_ID__", value=project_id),
        NameValuePair(name="__PROJECT_NAME__", value=project_name),
        NameValuePair(name="UPLOADS_CONTAINER", value=bicep_func_app_params.get("uploadsContainer")),
        NameValuePair(name="INPUT_CONTAINER", value=bicep_func_app_params.get("inputContainer")),
        NameValuePair(name="OUTPUT_CONTAINER", value=bicep_func_app_params.get("outputContainer")),
    ]

    if search_api_key:
        app_settings.append(
            NameValuePair(name="AZURE_AI_SEARCH_API_KEY", value=search_api_key)
        )
    if openai_api_key:
        app_settings.append(
            NameValuePair(name="AZURE_OPENAI_API_KEY", value=openai_api_key)
        )

    acr_username = os.environ.get("ACR_USERNAME")
    acr_password = os.environ.get("ACR_PASSWORD")
    if acr_username and acr_password:
        app_settings.append(
            NameValuePair(name="DOCKER_REGISTRY_SERVER_USERNAME", value=acr_username)
        )
        app_settings.append(
            NameValuePair(name="DOCKER_REGISTRY_SERVER_PASSWORD", value=acr_password)
        )

    config = SiteConfigResource(
        linux_fx_version=linux_fx_version,
        app_settings=app_settings,
    )

    web_client.web_apps.begin_create_or_update_configuration(
        resource_group_name=resource_group,
        name=function_app_name,
        site_config=config,
    ).result()

    app = web_client.web_apps.get(resource_group, function_app_name)
    host = app.default_host_name
    resource_data["function_app_name"] = function_app_name
    resource_data["function_app_url"] = f"https://{host}"

    return function_app_name


async def deploy_project_resources(
    project_id,
    project_name,
    region_id="westeurope",
    api_url="http://localhost:50505",
    resource_group="rg-internal-ai",
    location="westeurope",
    function_app_id=None,
):
    # Note: function_app_id is used in the code below when setting up Event Grid
    """
    Deploy Azure resources for a project using the Azure SDK and ACR-based Function App.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The Azure region ID (for tagging)
        api_url (str): The base URL of the API
        resource_group (str): The resource group name
        location (str): The Azure region to deploy to
        function_app_id (str, optional): The resource ID of the function app to connect to the event grid system topic

    Returns:
        bool: True if deployment was successful, False otherwise
    """
    # Check if the API server is running
    try:
        # Extract port from api_url
        import re

        port_match = re.search(r":(\d+)", api_url)
        port = port_match.group(1) if port_match else "50505"

        # Set environment variable for the API port
        os.environ["API_PORT"] = port

        # Run the check_api_server.py script to ensure the API server is running
        script_dir = os.path.dirname(os.path.abspath(__file__))
        check_api_script = os.path.join(script_dir, "check_api_server.py")

        # Make sure the script is executable
        try:
            os.chmod(check_api_script, 0o755)
        except Exception as e:
            logging.warning(f"Error making check_api_server.py executable: {e}")

        logging.info(f"Checking if API server is running on port {port}...")
        try:
            import requests

            response = requests.get(f"http://localhost:{port}/api/health", timeout=5)
            if response.status_code == 200:
                logging.info("API server is running and ready to receive status updates")
            else:
                logging.warning("API server responded but health check failed")
        except Exception as e:
            logging.warning(f"API server check failed: {e}")
            logging.warning("Continuing with deployment, but status updates may fail")
    except Exception as e:
        logging.warning(f"Error checking API server: {e}")
        logging.warning("Continuing with deployment, but status updates may fail")
    deployment_success = False
    resource_data = {}  # Initialize resource_data
    main_bicep_outputs = {}  # Initialize main_bicep_outputs

    # Record deployment start time for calculating total deployment time
    deployment_start_time = datetime.now(timezone.utc)

    # Track timing for individual resource deployments
    resource_durations = {}

    try:
        # Create a DeploymentSummary instance if the module is available
        deployment_summary = None
        if using_deployment_status_module and DeploymentSummary is not None:
            try:
                deployment_summary = DeploymentSummary(project_id, api_url)
                logging.info(
                    f"Created DeploymentSummary instance for project {project_id}"
                )
            except Exception as e:
                logging.error(f"Error creating DeploymentSummary instance: {e}")
                deployment_summary = None

        # Update deployment status to "in_progress"
        status_data = {
            "status": "in_progress",
            "message": "Starting infrastructure deployment...",
        }
        # If we have a deployment_summary instance, use it to update the status directly
        if deployment_summary:
            try:
                deployment_summary.update_status(
                    "in_progress", "Starting infrastructure deployment..."
                )
                deployment_summary.save_summary()
                logging.info(
                    "Updated deployment status to 'in_progress' using DeploymentSummary"
                )
            except Exception as e:
                logging.error(
                    f"Error updating deployment status using DeploymentSummary: {e}"
                )
                # Fall back to the old method
                await update_deployment_status(project_id, status_data, api_url)
                logging.info(
                    "Updated deployment status to 'in_progress' using fallback method"
                )
        else:
            # Use the old method
            await update_deployment_status(project_id, status_data, api_url)
            logging.info("Updated deployment status to 'in_progress'")

        # Step 1: Create storage and search resources using SDKs
        try:
            logging.info(f"Creating Azure resources for project {project_id}")

            storage_start_time = datetime.now(timezone.utc)
            storage_info = create_storage_account_and_containers(
                storage_client,
                resource_group,
                location,
                project_id,
            )
            resource_data.update(storage_info)
            storage_end_time = datetime.now(timezone.utc)
            resource_durations["storage"] = (
                storage_end_time - storage_start_time
            ).total_seconds()

            search_start_time = datetime.now(timezone.utc)
            keys = storage_client.storage_accounts.list_keys(
                resource_group, storage_info["storage_account_name"]
            )
            account_key = keys.keys[0].value
            search_service_name = f"search-{project_id[:8]}"
            search_info = create_search_service_and_resources(
                search_client,
                resource_group,
                location,
                search_service_name,
                storage_info["storage_account_name"],
                account_key,
                storage_info["uploads_container"],
                f"idx-{project_id[:8]}",
                f"ds-{project_id[:8]}",
                f"ixr-{project_id[:8]}",
            )
            resource_data.update(search_info)
            search_end_time = datetime.now(timezone.utc)
            resource_durations["search"] = (
                search_end_time - search_start_time
            ).total_seconds()

            main_bicep_outputs = {"resources": resource_data.copy()}

            status_data = {
                "status": "in_progress",
                "message": "Main infrastructure deployed successfully.",
                "details": {
                    "infrastructure_complete": True,
                    "storage": {
                        "storage_account": "storage_account_name" in resource_data,
                        "containers": {
                            "uploads": "uploads_container" in resource_data,
                            "input": "input_container" in resource_data,
                            "output": "output_container" in resource_data,
                        },
                    },
                    "storage_complete": "storage_account_name" in resource_data,
                    "search": {
                        "search_service": "search_service_name" in resource_data,
                        "index": "search_index_name" in resource_data,
                        "indexer": "search_indexer_name" in resource_data,
                        "datasource": "search_datasource_name" in resource_data,
                    },
                    "search_complete": "search_service_name" in resource_data,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False,
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "completion_percentage": 50,
                },
            }
            await update_deployment_status(project_id, status_data, api_url)

            if resource_data:
                update_result = await update_project_resources(
                    project_id, resource_data, api_url
                )
                if update_result:
                    logging.info(
                        f"Successfully updated project {project_id} with resource names in CosmosDB"
                    )
                else:
                    logging.warning(
                        f"Failed to update project {project_id} with resource names in CosmosDB, but continuing"
                    )

            deployment_success = True

            # Step 2: Deploy ACR Function App
            try:
                logging.info(
                    f"Starting ACR Function App deployment for project {project_id}"
                )
                status_data = {
                    "status": "in_progress",
                    "message": "Main infrastructure deployed. Starting Function App deployment.",
                    "details": {
                        "infrastructure_complete": True,
                        "storage": {
                            "storage_account": "storage_account_name" in resource_data,
                            "containers": {
                                "uploads": "uploads_container"
                                in main_bicep_outputs.get("resources", {}),
                                "input": "input_container"
                                in main_bicep_outputs.get("resources", {}),
                                "output": "output_container"
                                in main_bicep_outputs.get("resources", {}),
                            },
                        },
                        "storage_complete": "storage_account_name" in resource_data,
                        "search": {
                            "search_service": "search_service_name" in resource_data,
                            "index": "search_index_name"
                            in main_bicep_outputs.get("resources", {}),
                            "indexer": "search_indexer_name"
                            in main_bicep_outputs.get("resources", {}),
                            "datasource": "search_datasource_name"
                            in main_bicep_outputs.get("resources", {}),
                        },
                        "search_complete": "search_service_name" in resource_data,
                        "function": {
                            "function_app": False,
                            "event_grid_topic": False,
                            "event_grid_system_topic": False,
                            "event_grid": False,
                            "maturity_assessment": False,
                            "executive_summary": False,
                        },
                        "function_app_deployment_started": True,
                        "function_complete": False,
                        "overall_complete": False,
                        "completion_percentage": 60,
                    },
                }
                await update_deployment_status(project_id, status_data, api_url)

                function_start_time = datetime.now(timezone.utc)
                deployed_function_app_name = deploy_acr_function_app(
                    project_id,
                    project_name,
                    resource_group,
                    location,
                    main_bicep_outputs,
                    resource_data,
                )
                function_end_time = datetime.now(timezone.utc)
                resource_durations["function_app"] = (function_end_time - function_start_time).total_seconds()

                if deployed_function_app_name:
                    # Update resource data with function app name
                    resource_data["function_app_name"] = deployed_function_app_name
                    resource_data["function_app_url"] = (
                        f"https://{deployed_function_app_name}.azurewebsites.net"
                    )

                    # Update project resources in CosmosDB with basic function app details
                    logging.info(
                        f"Updating Cosmos DB with basic function app details for {project_id}"
                    )
                    update_result = await update_project_resources(
                        project_id, resource_data, api_url
                    )
                    if update_result:
                        logging.info(
                            f"Successfully updated project {project_id} with basic function app details in CosmosDB"
                        )
                    else:
                        logging.warning(
                            f"Failed to update project {project_id} with basic function app details in CosmosDB"
                        )

                    # Retrieve function keys
                    logging.info(
                        f"Starting function key retrieval for {deployed_function_app_name}"
                    )
                    function_names = [
                        "HttpTriggerAppMaturityAssessment",
                        "HttpTriggerAppExecutiveSummary",
                        "HttpTriggerPowerPointGenerator",
                    ]
                    keys_retrieved = False

                    # Add a delay to ensure the function app is fully deployed
                    logging.info(
                        "Waiting 30 seconds for function app to be fully deployed..."
                    )
                    time.sleep(30)

                    for function_name in function_names:
                        try:
                            keys = web_client.web_apps.list_function_keys(
                                resource_group,
                                deployed_function_app_name,
                                function_name,
                            )
                            function_key = (
                                getattr(keys, "default", None)
                                or (getattr(keys, "properties", {}) or {}).get("default")
                            )
                            if function_key:
                                if function_name == "HttpTriggerAppMaturityAssessment":
                                    resource_data["function_key_maturity"] = function_key
                                    resource_data[
                                        "azure_function_maturity_assessment_url"
                                    ] = f"{resource_data['function_app_url']}/api/{function_name}"
                                elif function_name == "HttpTriggerAppExecutiveSummary":
                                    resource_data["function_key_executive_summary"] = function_key
                                    resource_data[
                                        "azure_function_executive_summary_url"
                                    ] = f"{resource_data['function_app_url']}/api/{function_name}"
                                elif function_name == "HttpTriggerPowerPointGenerator":
                                    resource_data["function_key_powerpoint"] = function_key
                                keys_retrieved = True
                                logging.info(
                                    f"Retrieved function key for {function_name}"
                                )
                            else:
                                logging.warning(
                                    f"Function key for {function_name} not found"
                                )
                        except Exception as e:
                            logging.error(
                                f"Error retrieving function key for {function_name}: {e}"
                            )

                    # Log the keys that were retrieved
                    logging.info(
                        f"Completed function key retrieval. Keys retrieved: {list(k for k in resource_data.keys() if 'function_key' in k)}"
                    )

                    # Update Cosmos DB with function keys and endpoints
                    if keys_retrieved:
                        logging.info(
                            f"Updating Cosmos DB with function keys and endpoints for {project_id}"
                        )
                        update_result = await update_project_resources(
                            project_id, resource_data, api_url
                        )
                        if update_result:
                            logging.info(
                                f"Successfully updated project {project_id} with function keys and endpoints in CosmosDB"
                            )
                        else:
                            logging.warning(
                                f"Failed to update project {project_id} with function keys and endpoints in CosmosDB"
                            )
                    else:
                        logging.warning(
                            f"No function keys were retrieved for {project_id}, skipping Cosmos DB update"
                        )

                    # Send status update after function keys have been retrieved
                    if keys_retrieved:
                        status_data = {
                            "status": "function_app_keys_retrieved",
                            "message": "Function keys retrieved successfully.",
                            "details": {
                                "infrastructure_complete": True,
                                "storage": {
                                    "storage_account": "storage_account_name"
                                    in resource_data,
                                    "containers": {
                                        "uploads": "uploads_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "input": "input_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "output": "output_container"
                                        in main_bicep_outputs.get("resources", {}),
                                    },
                                },
                                "storage_complete": "storage_account_name"
                                in resource_data,
                                "search": {
                                    "search_service": "search_service_name"
                                    in resource_data,
                                    "index": "search_index_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "indexer": "search_indexer_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "datasource": "search_datasource_name"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                                "search_complete": "search_service_name"
                                in resource_data,
                                "function": {
                                    "function_app": True,
                                    "event_grid_topic": False,
                                    "event_grid_system_topic": False,
                                    "event_grid": False,
                                    "maturity_assessment": True,
                                    "executive_summary": True,
                                },
                                "function_app_complete": True,
                                "function_complete": False,
                                "overall_complete": False,
                                "completion_percentage": 80,
                            },
                        }
                        await update_deployment_status(project_id, status_data, api_url)

                    # Get the function app ID
                    retrieved_function_app_id = None
                    try:
                        try:
                            app_obj = web_client.web_apps.get(
                                resource_group, deployed_function_app_name
                            )
                            retrieved_function_app_id = app_obj.id
                            logging.info(
                                f"Retrieved function app ID: {retrieved_function_app_id}"
                            )
                        except Exception as e:
                            logging.warning(
                                f"Could not retrieve function app ID: {e}"
                            )
                    except Exception as e:
                        logging.error(f"Error retrieving function app ID: {e}")

                    # Step 3: Deploy Event Grid System Topic (only if function app ID was retrieved)
                    event_grid_complete = False
                    max_event_grid_retries = 3
                    event_grid_retry_count = 0

                    if retrieved_function_app_id:

                        logging.info(f"Attempting to deploy event grid system topic and subscription using function app ID: {retrieved_function_app_id}")
                        event_grid_start_time = datetime.now(timezone.utc)


                        # Wait for Function App to be fully deployed and ready
                        logging.info(
                            "Waiting 60 seconds for Function App to be fully deployed before setting up Event Grid..."
                        )
                        time.sleep(60)

                        # Get the function app name from resource data
                        function_app_name = deployed_function_app_name

                        # Verify Function App exists and is running
                        try:
                            func_app_check_cmd = [
                                "az",
                                "functionapp",
                                "show",
                                "--name",
                                function_app_name,
                                "--resource-group",
                                resource_group,
                                "--query",
                                "state",
                                "-o",
                                "tsv",
                            ]
                            try:
                                app_check = web_client.web_apps.get(
                                    resource_group, function_app_name
                                )
                                if app_check.state == "Running":
                                    logging.info(
                                        f"Function App {function_app_name} is running and ready for Event Grid setup"
                                    )
                                else:
                                    logging.warning(
                                        f"Function App {function_app_name} is not in 'Running' state: {app_check.state}"
                                    )
                                    logging.info(
                                        "Waiting additional 60 seconds for Function App to be ready..."
                                    )
                                    time.sleep(60)
                            except Exception as e:
                                logging.warning(f"Error checking Function App state: {e}")
                                logging.info("Continuing with Event Grid setup anyway...")

                            # Get storage account ID
                            storage_account_id = None
                            if (
                                "storage_account_name" in resource_data
                                and resource_data["storage_account_name"]
                            ):
                                try:
                                    try:
                                        sa = storage_client.storage_accounts.get_properties(
                                            resource_group, resource_data["storage_account_name"]
                                        )
                                        storage_account_id = sa.id
                                        logging.info(
                                            f"Retrieved storage account ID: {storage_account_id}"
                                        )
                                    except Exception as id_err:
                                        logging.error(
                                            f"Error retrieving storage account ID: {id_err}"
                                        )
                                except Exception as e:
                                    logging.error(
                                        f"Error retrieving storage account ID: {e}"
                                    )

                            if storage_account_id:
                                # Retry loop for event grid deployment
                                while (
                                    event_grid_retry_count < max_event_grid_retries
                                    and not event_grid_complete
                                ):
                                    event_grid_retry_count += 1
                                    logging.info(
                                        f"Attempting event grid deployment (attempt {event_grid_retry_count}/{max_event_grid_retries})..."
                                    )

                                    try:
                                        # Verify function app exists and is running before deploying event grid
                                        logging.info(
                                            f"Verifying function app {deployed_function_app_name} exists and is running..."
                                        )
                                        try:
                                            app_obj = web_client.web_apps.get(
                                                resource_group, deployed_function_app_name
                                            )
                                            if app_obj.state == "Running":
                                                logging.info(
                                                    f"Function app {deployed_function_app_name} is running"
                                                )
                                            else:
                                                logging.warning(
                                                    f"Function app {deployed_function_app_name} is not in Running state: {app_obj.state}"
                                                )
                                                if (
                                                    event_grid_retry_count < max_event_grid_retries
                                                ):
                                                    logging.info(
                                                        f"Waiting 30 seconds before retry {event_grid_retry_count + 1}..."
                                                    )
                                                    time.sleep(30)
                                                    continue
                                        except Exception as state_err:
                                            logging.warning(
                                                f"Error checking function app state: {state_err}"
                                            )
                                            if event_grid_retry_count < max_event_grid_retries:
                                                logging.info(
                                                    f"Waiting 30 seconds before retry {event_grid_retry_count + 1}..."
                                                )
                                                time.sleep(30)
                                                continue

                                        # We assume the EventGridTriggerBlobIndexer function already exists in the function app
                                        # We don't need to verify or create it, just proceed with event grid deployment
                                        logging.info(
                                            f"Proceeding with event grid deployment assuming EventGridTriggerBlobIndexer function exists in function app {deployed_function_app_name}..."
                                        )

                                        # Generate event grid system topic name
                                        sanitized_name = project_name.lower().replace(
                                            " ", "-"
                                        )
                                        # Remove any characters that are not alphanumeric or hyphens
                                        import re

                                        sanitized_name = re.sub(
                                            r"[^a-z0-9-]", "", sanitized_name
                                        )
                                        # Generate a unique suffix
                                        import hashlib

                                        unique_suffix = hashlib.md5(
                                            project_id.encode()
                                        ).hexdigest()[:4]
                                        event_grid_system_topic_name = (
                                            f"evgt-{sanitized_name}-{unique_suffix}"
                                        )
                                        logging.info(
                                            f"Generated event grid system topic name: {event_grid_system_topic_name}"
                                        )

                                        try:
                                            subscription_id = os.getenv("AZURE_SUBSCRIPTION_ID")
                                            if not subscription_id:
                                                raise ValueError("AZURE_SUBSCRIPTION_ID environment variable not set")

                                            eventgrid_client = EventGridManagementClient(get_management_credential(), subscription_id)

                                            topic_params = {
                                                "location": location,
                                                "tags": {
                                                    "project-id": project_id,
                                                    "region-id": region_id,
                                                    "project-name": project_name,
                                                },
                                                "source": storage_account_id,
                                                "topic_type": "Microsoft.Storage.StorageAccounts",
                                            }

                                            topic_poller = eventgrid_client.system_topics.begin_create_or_update(
                                                resource_group,
                                                event_grid_system_topic_name,
                                                topic_params,
                                            )
                                            topic = topic_poller.result()
                                            resource_data["event_grid_system_topic_name"] = topic.name
                                            logging.info(f"Event Grid System Topic created: {topic.name}")

                                            subscription_name = "blob-to-function-subscription"
                                            sub_params = {
                                                "destination": {
                                                    "endpoint_type": "AzureFunction",
                                                    "properties": {
                                                        "resource_id": f"{retrieved_function_app_id}/functions/EventGridTriggerBlobIndexer",
                                                        "max_events_per_batch": 1,
                                                        "preferred_batch_size_in_kilobytes": 64,
                                                    },
                                                },
                                                "filter": {
                                                    "included_event_types": [
                                                        "Microsoft.Storage.BlobCreated",
                                                        "Microsoft.Storage.BlobDeleted",
                                                    ],
                                                    "subject_begins_with": "/blobServices/default/containers/",
                                                    "enable_advanced_filtering_on_arrays": True,
                                                },
                                                "event_delivery_schema": "EventGridSchema",
                                                "retry_policy": {
                                                    "max_delivery_attempts": 30,
                                                    "event_time_to_live_in_minutes": 1440,
                                                },
                                            }

                                            sub_poller = eventgrid_client.system_topic_event_subscriptions.begin_create_or_update(
                                                resource_group,
                                                event_grid_system_topic_name,
                                                subscription_name,
                                                sub_params,
                                            )
                                            sub = sub_poller.result()
                                            resource_data["event_grid_subscription_name"] = sub.name
                                            logging.info(f"Event Subscription created: {sub.name}")

                                            created_topic = eventgrid_client.system_topics.get(resource_group, event_grid_system_topic_name)
                                            created_subscription = eventgrid_client.system_topic_event_subscriptions.get(
                                                resource_group,
                                                event_grid_system_topic_name,
                                                subscription_name,
                                            )

                                            if created_topic and created_subscription:
                                                event_grid_complete = True
                                                logging.info(
                                                    f"Event Grid deployment and verification successful on attempt {event_grid_retry_count}."
                                                )

                                                logging.info(
                                                    f"Updating Cosmos DB with Event Grid details for {project_id}"
                                                )
                                                update_result = await update_project_resources(
                                                    project_id,
                                                    resource_data,
                                                    api_url,
                                                )
                                                if update_result:
                                                    logging.info(
                                                        f"Successfully updated project {project_id} with Event Grid details in CosmosDB"
                                                    )
                                                else:
                                                    logging.warning(
                                                        f"Failed to update project {project_id} with Event Grid details in CosmosDB"
                                                    )
                                                break
                                            else:
                                                logging.warning("Event Grid resources not found after creation attempt")
                                                if event_grid_retry_count < max_event_grid_retries:
                                                    logging.info(
                                                        f"Waiting 30 seconds before retry {event_grid_retry_count + 1}..."
                                                    )
                                                    time.sleep(30)
                                                else:
                                                    logging.error(
                                                        f"Failed to verify Event Grid resources after {max_event_grid_retries} attempts"
                                                    )

                                        except Exception as event_grid_error:
                                            logging.error(
                                                f"Error deploying event grid system topic (attempt {event_grid_retry_count}/{max_event_grid_retries}): {event_grid_error}"
                                            )
                                            if event_grid_retry_count < max_event_grid_retries:
                                                logging.info(
                                                    f"Waiting 30 seconds before retry {event_grid_retry_count + 1}..."
                                                )
                                                time.sleep(30)
                                            else:
                                                logging.error(
                                                    f"Failed to deploy event grid system topic after {max_event_grid_retries} attempts"
                                                )
                                    except Exception as event_grid_error_outer:
                                        logging.error(
                                            f"Error during event grid deployment (attempt {event_grid_retry_count}/{max_event_grid_retries}): {event_grid_error_outer}"
                                        )
                                        break

                                if not event_grid_complete:
                                    logging.error(
                                        "Event grid deployment failed after all retry attempts"
                                    )
                            else:
                                logging.error(
                                    "Could not attempt event grid system topic/subscription deployment because storage account ID could not be retrieved"
                                )
                        except Exception as e:
                            logging.error(f"Error during Function App verification and Event Grid setup: {e}")
                        else:
                            logging.error(
                                "Could not attempt event grid system topic/subscription deployment because storage account name was not found in resource data"
                            )
                    else:
                        logging.warning(
                            "Skipping Event Grid deployment because Function App ID was not retrieved."
                        )

                    # Send status update after Event Grid verification if completed
                    if event_grid_complete:
                        status_data = {
                            "status": "event_grid_configured",
                            "message": "Event Grid system topic and subscription verified.",
                            "details": {
                                "infrastructure_complete": True,
                                "storage": {
                                    "storage_account": "storage_account_name"
                                    in resource_data,
                                    "containers": {
                                        "uploads": "uploads_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "input": "input_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "output": "output_container"
                                        in main_bicep_outputs.get("resources", {}),
                                    },
                                },
                                "storage_complete": "storage_account_name"
                                in resource_data,
                                "search": {
                                    "search_service": "search_service_name"
                                    in resource_data,
                                    "index": "search_index_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "indexer": "search_indexer_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "datasource": "search_datasource_name"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                                "search_complete": "search_service_name"
                                in resource_data,
                                "function": {
                                    "function_app": True,
                                    "event_grid_topic": True,
                                    "event_grid_system_topic": True,
                                    "event_grid": True,
                                    "maturity_assessment": True,
                                    "executive_summary": True,
                                },
                                "function_app_complete": True,
                                "event_grid_complete": True,
                                "function_complete": True,
                                "overall_complete": False,
                                "completion_percentage": 90,
                            },
                        }
                        await update_deployment_status(project_id, status_data, api_url)

                    # Update project resources in CosmosDB with function app and potentially event grid info
                    update_result = await update_project_resources(
                        project_id, resource_data, api_url
                    )
                    if update_result:
                        logging.info(
                            f"Successfully updated project {project_id} with function app and event grid info in CosmosDB"
                        )
                    else:
                        logging.warning(
                            f"Failed to update project {project_id} with function app and event grid info in CosmosDB"
                        )

                    event_grid_end_time = datetime.now(timezone.utc)
                    resource_durations["event_grid"] = (event_grid_end_time - event_grid_start_time).total_seconds()

                    # Add a final verification step
                    try:
                        # Get the project from Cosmos DB
                        logging.info(
                            f"Performing final verification of project {project_id} in Cosmos DB"
                        )

                        # Try to import the RBAC service
                        try:
                            from backend.rbac.cosmosdb_rbac_service import (
                                CosmosDBRbacService,
                            )

                            # Initialize the RBAC service
                            rbac_service = CosmosDBRbacService()

                            # Get the project
                            project = await rbac_service.get_project(project_id)

                            if project:
                                # Check if all required fields are present
                                required_fields = [
                                    "function_app_name",
                                    "function_app_url",
                                    "function_key_maturity",
                                    "function_key_executive_summary",
                                    "azure_function_maturity_assessment_url",
                                    "azure_function_executive_summary_url",
                                ]

                                if event_grid_complete:
                                    required_fields.append(
                                        "event_grid_system_topic_name"
                                    )

                                missing_fields = [
                                    field
                                    for field in required_fields
                                    if field not in project or not project[field]
                                ]

                                if missing_fields:
                                    logging.warning(
                                        f"Project is missing the following fields in Cosmos DB: {missing_fields}"
                                    )

                                    # Try to update the missing fields
                                    update_needed = False
                                    for field in missing_fields:
                                        if (
                                            field in resource_data
                                            and resource_data[field]
                                        ):
                                            project[field] = resource_data[field]
                                            update_needed = True

                                    if update_needed:
                                        logging.info(
                                            f"Attempting to update missing fields in Cosmos DB: {missing_fields}"
                                        )
                                        region = project.get("region", "westeurope")
                                        updated_project = (
                                            await rbac_service.update_project(
                                                project_id=project_id,
                                                region=region,
                                                project_data=project,
                                                etag=None,
                                            )
                                        )

                                        if updated_project:
                                            logging.info(
                                                f"Successfully updated missing fields in Cosmos DB"
                                            )
                                        else:
                                            logging.error(
                                                f"Failed to update missing fields in Cosmos DB"
                                            )
                                else:
                                    logging.info(
                                        "All required fields are present in Cosmos DB"
                                    )
                            else:
                                logging.error(
                                    f"Project {project_id} not found in Cosmos DB during final verification"
                                )
                        except ImportError:
                            logging.warning(
                                "CosmosDBRbacService not available for final verification"
                            )
                        except Exception as e:
                            logging.error(
                                f"Error during final verification with RBAC service: {e}"
                            )
                    except Exception as e:
                        logging.error(f"Error during final verification: {e}")

                    # Generate deployment summary and automatically update the project in CosmosDB
                    try:
                        summary_file_path = await generate_deployment_summary(
                            project_id,
                            project_name,
                            region_id,
                            resource_group,
                            deployment_start_time,
                            resource_data,
                            main_bicep_outputs,
                            resource_durations,
                            status="success",
                            auto_update_project=True,
                            api_url=api_url,
                        )
                        logging.info(
                            f"Deployment summary generated at {summary_file_path if summary_file_path else 'unknown path'} and project updated in CosmosDB"
                        )
                    except Exception as e:
                        logging.error(f"Error generating deployment summary: {e}")
                        logging.warning(
                            "Continuing with deployment status update despite summary generation error"
                        )

                    # Update deployment status to completed
                    status_data = {
                        "status": "completed",
                        "message": "ACR Function App deployed successfully."
                        + (
                            " Event Grid deployment attempted."
                            if retrieved_function_app_id
                            else ""
                        ),
                        "details": {
                            "infrastructure_complete": True,
                            "storage": {
                                "storage_account": "storage_account_name"
                                in resource_data,
                                "containers": {
                                    "uploads": "uploads_container"
                                    in main_bicep_outputs.get("resources", {}),
                                    "input": "input_container"
                                    in main_bicep_outputs.get("resources", {}),
                                    "output": "output_container"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                            },
                            "storage_complete": "storage_account_name" in resource_data,
                            "search": {
                                "search_service": "search_service_name"
                                in resource_data,
                                "index": "search_index_name"
                                in main_bicep_outputs.get("resources", {}),
                                "indexer": "search_indexer_name"
                                in main_bicep_outputs.get("resources", {}),
                                "datasource": "search_datasource_name"
                                in main_bicep_outputs.get("resources", {}),
                            },
                            "search_complete": "search_service_name" in resource_data,
                            "function": {
                                "function_app": True,
                                "event_grid_topic": event_grid_complete,
                                "event_grid_system_topic": event_grid_complete,
                                "event_grid": event_grid_complete,
                                "maturity_assessment": True,
                                "executive_summary": True,
                            },
                            "function_app_complete": True,
                            "event_grid_complete": event_grid_complete,
                            "function_complete": True,
                            "overall_complete": True,
                            "ready_for_function_deployment": False,
                            "completion_percentage": 100,
                        },
                    }
                    await update_deployment_status(project_id, status_data, api_url)
                else:
                    # Function App deployment failed, but main infrastructure is still good
                    logging.warning(
                        "Function App deployment failed or name could not be retrieved"
                    )

                    # Generate deployment summary with partial success status and automatically update the project in CosmosDB
                    try:
                        summary_file_path = await generate_deployment_summary(
                            project_id,
                            project_name,
                            region_id,
                            resource_group,
                            deployment_start_time,
                            resource_data,
                            main_bicep_outputs,
                            resource_durations,
                            status="failed",
                            auto_update_project=True,
                            api_url=api_url,
                        )
                        logging.info(
                            f"Deployment summary generated at {summary_file_path if summary_file_path else 'unknown path'} with partial success status and project updated in CosmosDB"
                        )
                    except Exception as e:
                        logging.error(f"Error generating deployment summary: {e}")
                        logging.warning(
                            "Continuing with deployment status update despite summary generation error"
                        )

                    status_data = {
                        "status": "failed",
                        "message": "Main infrastructure deployed successfully, but Function App deployment failed.",
                        "details": {
                            "infrastructure_complete": True,
                            "storage": {
                                "storage_account": "storage_account_name"
                                in resource_data,
                                "containers": {
                                    "uploads": "uploads_container"
                                    in main_bicep_outputs.get("resources", {}),
                                    "input": "input_container"
                                    in main_bicep_outputs.get("resources", {}),
                                    "output": "output_container"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                            },
                            "storage_complete": "storage_account_name" in resource_data,
                            "search": {
                                "search_service": "search_service_name"
                                in resource_data,
                                "index": "search_index_name"
                                in main_bicep_outputs.get("resources", {}),
                                "indexer": "search_indexer_name"
                                in main_bicep_outputs.get("resources", {}),
                                "datasource": "search_datasource_name"
                                in main_bicep_outputs.get("resources", {}),
                            },
                            "search_complete": "search_service_name" in resource_data,
                            "function": {
                                "function_app": False,
                                "event_grid_topic": False,
                                "event_grid_system_topic": False,
                                "event_grid": False,
                                "maturity_assessment": False,
                                "executive_summary": False,
                            },
                            "function_app_complete": False,
                            "function_complete": False,
                            "overall_complete": False,
                            "error_type": "function_app_deployment_error",
                            "completion_percentage": 50,
                        },
                    }
                    await update_deployment_status(project_id, status_data, api_url)
            except Exception as func_app_error:
                # Function App deployment exception
                logging.error(f"Error deploying Function App: {func_app_error}")

                # Generate deployment summary with partial success status and automatically update the project in CosmosDB
                try:
                    summary_file_path = await generate_deployment_summary(
                        project_id,
                        project_name,
                        region_id,
                        resource_group,
                        deployment_start_time,
                        resource_data,
                        main_bicep_outputs,
                        resource_durations,
                        status="failed",
                        auto_update_project=True,
                        api_url=api_url,
                    )
                    logging.info(
                        f"Deployment summary generated at {summary_file_path if summary_file_path else 'unknown path'} with partial success status and project updated in CosmosDB"
                    )
                except Exception as e:
                    logging.error(f"Error generating deployment summary: {e}")
                    logging.warning(
                        "Continuing with deployment status update despite summary generation error"
                    )

                status_data = {
                    "status": "failed",
                    "message": f"Main infrastructure deployed successfully, but Function App deployment failed: {str(func_app_error)}",
                    "details": {
                        "infrastructure_complete": True,
                        "storage": {
                            "storage_account": "storage_account_name" in resource_data,
                            "containers": {
                                "uploads": "uploads_container"
                                in main_bicep_outputs.get("resources", {}),
                                "input": "input_container"
                                in main_bicep_outputs.get("resources", {}),
                                "output": "output_container"
                                in main_bicep_outputs.get("resources", {}),
                            },
                        },
                        "storage_complete": "storage_account_name" in resource_data,
                        "search": {
                            "search_service": "search_service_name" in resource_data,
                            "index": "search_index_name"
                            in main_bicep_outputs.get("resources", {}),
                            "indexer": "search_indexer_name"
                            in main_bicep_outputs.get("resources", {}),
                            "datasource": "search_datasource_name"
                            in main_bicep_outputs.get("resources", {}),
                        },
                        "search_complete": "search_service_name" in resource_data,
                        "function": {
                            "function_app": False,
                            "event_grid_topic": False,
                            "event_grid_system_topic": False,
                            "event_grid": False,
                            "maturity_assessment": False,
                            "executive_summary": False,
                        },
                        "function_app_complete": False,
                        "function_complete": False,
                        "overall_complete": False,
                        "error_type": "function_app_deployment_error",
                        "error_details": str(func_app_error),
                        "completion_percentage": 50,
                    },
                }
                await update_deployment_status(project_id, status_data, api_url)

        except Exception as e:
            error_message = f"Error during main infrastructure deployment: {str(e)}"
            logging.error(error_message)

            # Generate deployment summary with failed status and automatically update the project in CosmosDB
            try:
                summary_file_path = await generate_deployment_summary(
                    project_id,
                    project_name,
                    region_id,
                    resource_group,
                    deployment_start_time,
                    resource_data,
                    main_bicep_outputs,
                    resource_durations,
                    status="failed",
                    auto_update_project=True,
                    api_url=api_url,
                )
                logging.info(
                    f"Deployment summary generated at {summary_file_path if summary_file_path else 'unknown path'} with failed status and project updated in CosmosDB"
                )
            except Exception as summary_error:
                logging.error(f"Error generating deployment summary: {summary_error}")
                logging.warning(
                    "Continuing with deployment status update despite summary generation error"
                )

            # Update deployment status to failed
            status_data = {
                "status": "failed",
                "message": error_message,
                "details": {
                    "infrastructure_complete": False,
                    "storage": {
                        "storage_account": False,
                        "containers": {
                            "uploads": False,
                            "input": False,
                            "output": False,
                        },
                    },
                    "storage_complete": False,
                    "search": {
                        "search_service": False,
                        "index": False,
                        "indexer": False,
                        "datasource": False,
                    },
                    "search_complete": False,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False,
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "error_type": "deployment_error",
                    "error_details": str(e),
                    "completion_percentage": 0,
                },
            }
            await update_deployment_status(project_id, status_data, api_url)
            deployment_success = False

    except Exception as e:
        logging.error(f"Error in deploy_project_resources: {e}")

        # Try to generate a deployment summary even in case of failure and automatically update the project in CosmosDB
        try:
            summary_file_path = await generate_deployment_summary(
                project_id,
                project_name,
                region_id,
                resource_group,
                deployment_start_time,
                resource_data,
                main_bicep_outputs,
                resource_durations,
                status="failed",
                auto_update_project=True,
                api_url=api_url,
            )
            logging.info(
                f"Deployment summary generated at {summary_file_path if summary_file_path else 'unknown path'} despite deployment failure and project updated in CosmosDB"
            )
        except Exception as summary_error:
            logging.error(
                f"Error generating deployment summary after deployment failure: {summary_error}"
            )

        # Update status to failed
        try:
            status_data = {
                "status": "failed",
                "message": f"Error in deployment process: {str(e)}",
                "details": {
                    "infrastructure_complete": False,
                    "storage": {
                        "storage_account": False,
                        "containers": {
                            "uploads": False,
                            "input": False,
                            "output": False,
                        },
                    },
                    "storage_complete": False,
                    "search": {
                        "search_service": False,
                        "index": False,
                        "indexer": False,
                        "datasource": False,
                    },
                    "search_complete": False,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False,
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "error_type": "unexpected_error",
                    "error_details": str(e),
                    "completion_percentage": 0,
                },
            }
            await update_deployment_status(project_id, status_data, api_url)
        except Exception as update_error:
            logging.error(f"Error updating deployment status: {update_error}")

        deployment_success = False

    return deployment_success


# If run directly, execute the deployment
if __name__ == "__main__":
    # Set up logging to file for debugging
    debug_log_file = "logs/deploy_project_resources_debug.log"
    debug_handler = logging.FileHandler(debug_log_file)
    debug_handler.setLevel(logging.DEBUG)
    debug_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    debug_handler.setFormatter(debug_formatter)
    logging.getLogger().addHandler(debug_handler)

    logging.info(f"Starting deploy_project_resources.py with args: {sys.argv}")

    if len(sys.argv) < 3:
        print(
            "Usage: deploy_project_resources.py <project_id> <project_name> [--region-id <region_id>] [--resource-group <resource_group>] [--location <location>] [--function-app-id <function_app_id>] [api_url]"
        )
        print(
            "Example: deploy_project_resources.py my-project-id my-project-name --region-id westeurope --resource-group rg-internal-ai --location westeurope --function-app-id /subscriptions/xxx/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/func-my-project-id http://localhost:50505"
        )
        sys.exit(1)

    project_id = sys.argv[1]
    project_name = sys.argv[2]

    # Parse remaining arguments
    region_id = "westeurope"  # Default value
    resource_group = "rg-internal-ai"  # Default value
    location = "westeurope"  # Default value
    api_url = "http://localhost:50505"  # Default value
    function_app_id = None  # Default value

    # Parse remaining arguments
    i = 3
    while i < len(sys.argv):
        if sys.argv[i] == "--region-id" and i + 1 < len(sys.argv):
            region_id = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--resource-group" and i + 1 < len(sys.argv):
            resource_group = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--location" and i + 1 < len(sys.argv):
            location = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--function-app-id" and i + 1 < len(sys.argv):
            function_app_id = sys.argv[i + 1]
            i += 2
        elif not sys.argv[i].startswith("--"):
            # Assume this is the API URL if it's not a flag
            api_url = sys.argv[i]
            i += 1
        else:
            # Skip unknown arguments
            i += 1

    # Run the deployment
    asyncio.run(
        deploy_project_resources(
            project_id,
            project_name,
            region_id,
            api_url,
            resource_group,
            location,
            function_app_id,
        )
    )
