# Authentication Issue Analysis and Fix

## Problem Summary

The application was failing to authenticate in production while working correctly in local development. The root cause was that **Azure App Service Easy Auth was not providing authentication tokens to the backend**.

## Root Cause Analysis

### Local Environment (Working)
- Uses MSAL.js directly for authentication
- MSAL successfully caches user accounts in localStorage
- API calls include `Authorization: Bearer <token>` headers
- Backend receives and validates MSAL tokens

### Production Environment (Failing)
- Azure App Service Easy Auth handles initial authentication
- User gets authenticated and redirected to the app
- **Critical Issue**: Easy Auth was not providing the expected headers (`X-MS-CLIENT-PRINCIPAL`) OR the Azure-specific token headers (`x-ms-token-aad-id-token`, `x-ms-token-aad-access-token`)
- Frontend skips MSAL token acquisition in production (correct behavior)
- Backend receives no authentication information and falls back to mock user

## Key Evidence from Logs

**Backend Production Logs:**
```
All request headers: {'host': 'ai-scope-app3.azurewebsites.net', 'user-agent': 'Mozilla/5.0...', 'accept': 'application/json', ...}
```
**Missing**: No `X-MS-CLIENT-PRINCIPAL`, `x-ms-token-aad-id-token`, or `x-ms-token-aad-access-token` headers

**Result:**
```
No Easy Auth headers found - checking if Easy Auth is properly configured
Falling back to client credentials.
Entra ID authentication not configured. Using mock user.
```

## Solution Implemented

### 1. Frontend Changes (`frontend/src/services/authHeaderService.ts`)

Modified `getAuthHeaders()` to detect production environment and skip MSAL token acquisition (this was correct):

```typescript
export async function getAuthHeaders(customScopes?: string[]): Promise<HeadersInit> {
  // Check if we're in production with Azure App Service Easy Auth
  const isProduction = window.location.hostname !== 'localhost';

  if (isProduction) {
    console.log('Production environment: Using Azure App Service Easy Auth - no explicit Authorization header needed');
    // In production with Easy Auth, the App Service automatically handles authentication
    return {
      'Content-Type': 'application/json'
    };
  }

  // For development, use MSAL.js as before
  // ... existing MSAL logic
}
```

### 2. Backend Changes (`backend/auth/entra_auth.py`)

**Key Fix**: Implemented proper Azure App Service Easy Auth token extraction based on `token_utils.md`:

```python
def extract_token_from_request_with_easy_auth(request: Request) -> Optional[str]:
    """
    Extract the Bearer token from the Authorization header or Azure Easy Auth headers.

    This function implements the token extraction logic from token_utils.md to properly
    handle both standard Authorization headers and Azure App Service Easy Auth headers.
    """
    # Check for Authorization header first
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header.split()[1]
        if not _is_token_expired(token):
            return token

    # Fallback to Azure-specific Entra ID headers when running on Azure Web App
    id_token_header = request.headers.get("x-ms-token-aad-id-token")
    if id_token_header and not _is_token_expired(id_token_header):
        logging.info("✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-id-token header")
        return id_token_header

    access_token_header = request.headers.get("x-ms-token-aad-access-token")
    if access_token_header and not _is_token_expired(access_token_header):
        logging.info("✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-access-token header")
        return access_token_header

    return None
```

### 3. Enhanced Logging and Debugging

Added comprehensive logging to identify exactly what headers are being received:

```python
# Log all headers for debugging
logging.info(f"All request headers: {dict(request.headers)}")

# Detailed token extraction logging
logging.info("🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request")
logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Available headers: {list(request.headers.keys())}")
```

## How the Fix Works

### Production Flow (After Fix)
1. User accesses the application
2. Azure App Service Easy Auth handles authentication and redirects to app
3. User reaches the application (already authenticated)
4. Frontend makes API calls **without** Authorization headers
5. Azure App Service automatically adds Easy Auth token headers (`x-ms-token-aad-id-token`, `x-ms-token-aad-access-token`)
6. Backend detects Easy Auth token headers and extracts the JWT token
7. Backend validates the token and gets user information from Microsoft Graph API
8. API calls succeed with proper user context

### Development Flow (Unchanged)
1. User accesses the application
2. MSAL.js handles authentication
3. Frontend makes API calls **with** Authorization headers
4. Backend validates MSAL tokens and extracts user information
5. API calls succeed with proper user context

## Files Modified

1. `frontend/src/services/authHeaderService.ts` - Skip MSAL token acquisition in production
2. `backend/auth/entra_auth.py` - Implemented proper Easy Auth token extraction
3. `infra/core/host/appservice.bicep` - Enhanced Easy Auth configuration
4. `env.json` - Added environment variables for Easy Auth endpoint fallback

## Expected Behavior After Fix

### Production
- No MSAL authentication prompts
- Direct access to projects page after Easy Auth
- API calls work without explicit Authorization headers from frontend
- Backend extracts tokens from Easy Auth headers (`x-ms-token-aad-id-token` or `x-ms-token-aad-access-token`)
- Backend validates tokens and gets real user information from Microsoft Graph API
- **Real authenticated user data** instead of mock user

### Development
- MSAL authentication works as before
- API calls include Authorization headers
- Backend validates MSAL tokens

## Testing the Fix

1. Deploy the changes to production
2. Access the application directly
3. Check the backend logs for:
   - `✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-id-token header` or similar
   - Real user information instead of "Regular User"
4. Verify that:
   - No authentication loops occur
   - API calls to `/api/user-context/me` succeed with real user data
   - Projects page loads correctly with proper user context

## Key Difference from Previous Approach

**Previous Approach**: Tried to parse `X-MS-CLIENT-PRINCIPAL` headers (which weren't present)
**New Approach**: Uses the Azure-specific token headers (`x-ms-token-aad-id-token`, `x-ms-token-aad-access-token`) that Azure App Service Easy Auth provides

This approach follows the exact pattern from `token_utils.md` and `extract_live_token.md` which are proven to work with Azure App Service Easy Auth.
