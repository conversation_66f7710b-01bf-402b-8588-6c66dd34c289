#!/bin/bash

# Unified startup script for AI Scope App
# This script provides a consistent way to start the application with Hypercorn

set -e  # Exit on any error

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --port PORT     Set the port (default: from API_PORT env var or 50508)"
    echo "  -h, --help          Show this help message"
    echo "  --build-frontend    Build frontend before starting (default: false)"
    echo "  --clean             Clean build artifacts before starting (default: false)"
    echo ""
    echo "Environment Variables:"
    echo "  API_PORT           Port to run the server on (default: 50508)"
    echo ""
    echo "Examples:"
    echo "  $0                 # Start with default settings"
    echo "  $0 -p 8080         # Start on port 8080"
    echo "  $0 --build-frontend --clean  # Clean build and rebuild frontend"
}

# Parse command line arguments
BUILD_FRONTEND=true
CLEAN_BUILD=true
PORT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        --build-frontend)
            BUILD_FRONTEND=true
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

echo "=== AI Scope App Unified Startup ==="
echo ""

# Load environment variables
echo "Loading environment variables..."
. ./scripts/loadenv.sh

# Set port from command line, environment variable, or default
if [ -n "$PORT" ]; then
    API_PORT="$PORT"
elif [ -z "$API_PORT" ]; then
    API_PORT=50508
fi

echo "Configuration:"
echo "  Port: $API_PORT"
echo "  Build Frontend: $BUILD_FRONTEND"
echo "  Clean Build: $CLEAN_BUILD"
echo ""

# Kill any running processes
echo "Stopping any running processes..."
if command_exists pkill; then
    pkill -f "hypercorn" || true
    pkill -f "python run.py" || true
    pkill -f "vite" || true
fi

# Clean build artifacts if requested
if [ "$CLEAN_BUILD" = true ]; then
    echo "Cleaning build artifacts..."
    find . -type d -name "__pycache__" -exec rm -r {} + 2>/dev/null || true
    find . -name "*.pyc" -delete
    if [ -d "frontend/node_modules" ]; then
        echo "Removing frontend/node_modules..."
        rm -rf frontend/node_modules
    fi
    if [ -d "static" ]; then
        echo "Removing static build directory..."
        rm -rf static
    fi
fi

# Build frontend if requested
if [ "$BUILD_FRONTEND" = true ]; then
    echo "Building frontend..."
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        echo "Installing frontend dependencies..."
        npm install
    fi
    
    echo "Building frontend..."
    npm run build
    cd ..
fi

# Verify virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Virtual environment not found. Creating..."
    python3 -m venv .venv
    echo "Installing dependencies..."
    ./.venv/bin/python -m pip install -r requirements-dev.txt
fi

# Start the server with Hypercorn
echo ""
echo "Starting server with Hypercorn on port $API_PORT..."
echo "Server will be available at: http://localhost:$API_PORT"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Export the port for the application
export API_PORT

# Start with Hypercorn (preferred) or fallback to run.py
if command_exists ./.venv/bin/hypercorn; then
    echo "Using Hypercorn server..."
    ./.venv/bin/hypercorn app:app --bind 0.0.0.0:$API_PORT --reload
else
    echo "Hypercorn not found, falling back to Quart development server..."
    ./.venv/bin/python run.py
fi

 